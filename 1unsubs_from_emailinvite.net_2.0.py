import os
import time
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains

# Import the rich progress module
from rich_progress import wait_for_download_with_progress, print_status, RICH_AVAILABLE

path_dir = r'H:\Master Bounces and Unsubs\Postpanel Unsubs\mw'

if not os.path.exists(path_dir):
    os.makedirs(path_dir)
os.chdir(path_dir)

# The wait_for_download_with_progress function is now imported from rich_progress module

# Selenium setup
options = webdriver.ChromeOptions()

# Suppress console logging messages
options.add_experimental_option("excludeSwitches", ["enable-logging"])

# Add logging preferences to capture browser logs
options.add_argument('log-level=3')  # Set log level to errors only

# Headless mode and other performance settings
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")
options.add_argument("--window-size=1920,1080")  # Set window size for headless mode
options.add_argument("--disable-blink-features=AutomationControlled")
options.add_argument("--disable-web-security")
options.add_argument("--allow-running-insecure-content")

# Disable extensions and other features that might cause errors
options.add_argument("--disable-extensions")
options.add_argument("--disable-notifications")

# Download preferences
prefs = {
    "download.default_directory": path_dir,
    "download.prompt_for_download": False,
    "download.directory_upgrade": True,
    "browser.download.show_plugins_in_list": False,
    "browser.download.folderList": 2,
    "browser.download.manager.showWhenStarting": False,
    "download.manager.showTaskbarProgress": False  # Disable taskbar progress updates
}
options.add_experimental_option('prefs', prefs)

# Print header
print_status("Starting EMAILINVITE.NET Unsubscribes Download Process", "header")

# Helper function for safe element clicking
def safe_click(driver, by, value, timeout=10):
    """Safely click an element with wait and scroll into view"""
    try:
        wait = WebDriverWait(driver, timeout)
        element = wait.until(EC.element_to_be_clickable((by, value)))

        # Scroll element into view
        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
        time.sleep(1)

        # Try regular click first
        try:
            element.click()
        except Exception:
            # If regular click fails, use JavaScript click
            driver.execute_script("arguments[0].click();", element)

        return True
    except Exception as e:
        print_status(f"Failed to click element {value}: {str(e)}", "error")
        return False

# Perform the automated download process
try:
    print_status("Initializing Chrome browser...", "info")
    driver = webdriver.Chrome(options=options)

    # Set window size for headless mode
    driver.set_window_size(1920, 1080)

    # Initialize WebDriverWait
    wait = WebDriverWait(driver, 10)

    print_status("Navigating to cPanel login...", "info")
    driver.get("https://216-10-242-48.cprapid.com:2087/")
    time.sleep(3)

    print_status("Logging in...", "info")
    wait.until(EC.presence_of_element_located((By.ID, "user"))).send_keys("root")
    wait.until(EC.presence_of_element_located((By.ID, "pass"))).send_keys("Magnus@12527")
    safe_click(driver, By.XPATH, "//button[@id='login_submit']")
    time.sleep(3)

    print_status("Navigating to List Accounts...", "info")
    safe_click(driver, By.XPATH, "//span[@class='cp-app__details-title'][normalize-space()='List Accounts']")
    time.sleep(3)

    print_status("Opening account details...", "info")
    safe_click(driver, By.XPATH, "//input[@type='image']")
    time.sleep(5)

    print_status("Switching to phpMyAdmin...", "info")
    driver.switch_to.window(driver.window_handles[1])
    safe_click(driver, By.XPATH, "//span[normalize-space()='phpMyAdmin']")
    time.sleep(5)

    print_status("Navigating to database...", "info")
    driver.switch_to.window(driver.window_handles[2])
    safe_click(driver, By.XPATH, "//a[normalize-space()='emailinvite_mailWizz']")
    time.sleep(10)

    print_status("Searching for subscriber table...", "info")
    wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="filterText"]'))).send_keys('mw_list_subscriber')
    safe_click(driver, By.XPATH, '//*[@id="row_tbl_105"]/th/a')
    time.sleep(10)

    print_status("Navigating to export...", "info")
    safe_click(driver, By.XPATH, '//*[@id="topmenu"]/li[6]/a')
    time.sleep(6)

    print_status("Setting up CSV export...", "info")
    safe_click(driver, By.XPATH, '//*[@id="plugins"]/option[2]')
    time.sleep(6)

    print_status("Initiating download...", "info")
    safe_click(driver, By.XPATH, '//*[@id="buttonGo"]')
    time.sleep(6)

    # Wait for the download to complete with progress bar (using blue gradient)
    try:
        wait_for_download_with_progress(path_dir, timeout=300, color_scheme="blue")
        print_status("Download process completed successfully!", "success")
    except TimeoutError as e:
        print_status(f"Download timeout: {str(e)}", "error")

except Exception as e:
    print_status(f"Error during execution: {str(e)}", "error")

finally:
    # Always close the browser to clean up resources
    try:
        if 'driver' in locals():
            print_status("Closing browser...", "info")
            driver.quit()
    except Exception as close_error:
        print_status(f"Error closing browser: {str(close_error)}", "error")