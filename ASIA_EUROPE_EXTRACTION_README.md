# Asia-Europe Data Extraction Scripts

This collection of scripts extracts data from China, Japan, Korea, and all European countries from conference datasets. The scripts are built on top of the existing HIC filtering functionality and provide both single-directory and batch processing capabilities.

## Scripts Overview

### 1. `extract_asia_europe_data.py` - Single Directory Processing
**Purpose**: Extract Asia-Europe data from a single directory containing CSV files.

**Features**:
- Extracts data from China (including special Chinese domains like qq.com, 163.com, etc.)
- Extracts data from Japan (.jp domains)
- Extracts data from South Korea (.kr domains)
- Extracts data from all European countries (47+ European TLDs)
- Creates individual files for each European country with data
- Provides detailed statistics and progress tracking

**Usage**:
1. Run the script: `python extract_asia_europe_data.py`
2. Enter the directory path containing CSV files
3. The script will automatically detect the conference name from the path
4. Results are saved in an `asia_europe_extraction` folder with subfolders:
   - `asia/` - Contains China, Japan, and South Korea files
   - `europe/` - Contains combined European data
   - `individual_european_countries/` - Individual files for each European country

### 2. `batch_extract_asia_europe_data.py` - Batch Processing
**Purpose**: Process multiple directories at once using a text file containing directory paths.

**Features**:
- Processes multiple conference datasets in one run
- Same extraction logic as the single-directory version
- Comprehensive batch processing statistics
- Progress tracking across all directories
- Error handling for individual directory failures

**Usage**:
1. Create a text file with directory paths (one per line)
2. Run the script: `python batch_extract_asia_europe_data.py`
3. Provide the path to your directory list file
4. Each directory will get its own `asia_europe_extraction` folder

## Target Countries and Domains

### China
- **TLD**: `.cn`
- **Special Domains**: `163.com`, `qq.com`, `126.com`, `sina.com`, `sohu.com`, `tom.com`, `aliyun.com`, `21cn.com`, `baidu.com`, `yeah.net`, `sogou.com`, `163.net`, `sina.net`, `chinaren.com`
- **Related**: Hong Kong (`.hk`), Macao (`.mo`), Taiwan (`.tw`)

### Japan
- **TLD**: `.jp`

### South Korea
- **TLD**: `.kr`

### European Countries (47+ countries)
- **Major Countries**: Germany (`.de`), United Kingdom (`.uk`), France (`.fr`), Italy (`.it`), Spain (`.es`), Netherlands (`.nl`), Poland (`.pl`), etc.
- **All European TLDs**: `.ad`, `.al`, `.at`, `.ba`, `.be`, `.bg`, `.by`, `.ch`, `.cy`, `.cz`, `.de`, `.dk`, `.ee`, `.es`, `.eu`, `.fi`, `.fr`, `.gb`, `.gr`, `.hr`, `.hu`, `.ie`, `.is`, `.it`, `.li`, `.lt`, `.lu`, `.lv`, `.mc`, `.md`, `.me`, `.mk`, `.mt`, `.nl`, `.no`, `.pl`, `.pt`, `.ro`, `.rs`, `.ru`, `.se`, `.si`, `.sk`, `.sm`, `.tr`, `.ua`, `.uk`, `.va`

## Output Structure

```
asia_europe_extraction/
├── asia/
│   ├── [Conference]_China.csv
│   ├── [Conference]_Japan.csv
│   └── [Conference]_South_Korea.csv
├── europe/
│   └── [Conference]_Europe_Combined.csv
└── individual_european_countries/
    ├── [Conference]_Germany.csv
    ├── [Conference]_United_Kingdom.csv
    ├── [Conference]_France.csv
    └── ... (one file per European country with data)
```

## Directory List File Format

For batch processing, create a text file with directory paths:

```
# Conference directories to process
# Lines starting with # are comments
C:\Data\Conference_2023
C:\Data\Conference_2024
C:\Data\Workshop_2023
# Empty lines are ignored
C:\Data\Symposium_2024
```

## Requirements

- Python 3.6+
- pandas
- rich_progress (custom module for progress bars)
- Standard libraries: os, glob, re, warnings, datetime, pathlib

## Statistics and Reporting

Both scripts provide comprehensive statistics including:
- Total records processed
- Records extracted per country/region
- Number of files created
- Extraction percentages
- Top European countries by record count
- Processing success rates (batch version)

## Error Handling

- Invalid directories are logged but don't stop processing
- CSV reading errors are handled gracefully
- Individual directory failures don't affect batch processing
- Comprehensive error messages and warnings

## Integration with Existing Scripts

These scripts use the same core functionality as:
- `hic_continents.py` - For continent-level filtering
- `hic_individual_countries.py` - For individual country filtering

The new scripts combine and optimize the best features from both existing scripts while focusing specifically on the Asia-Europe extraction requirements.
