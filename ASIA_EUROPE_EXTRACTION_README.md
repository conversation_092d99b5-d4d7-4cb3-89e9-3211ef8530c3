# Asia-Europe Data Extraction Script

This unified script extracts data from China, Japan, Korea, and all European countries from conference datasets. The script is built on top of the existing HIC filtering functionality and provides both single-directory and batch processing capabilities in one unified interface.

## Script Overview

### `extract_asia_europe_data.py` - Unified Processing Script
**Purpose**: Extract Asia-Europe data from single or multiple directories, processing each directory individually.

**Key Features**:
- **Unified Interface**: Choose between single directory or batch processing with a simple menu (1 or 2)
- **Individual Processing**: Each directory is processed separately and gets its own output file
- **Comprehensive Coverage**: Extracts data from China (including special Chinese domains), Japan, South Korea, and all European countries
- **Individual Output Files**: Each directory gets its own randomized CSV file in its own `asia_europe_extraction` folder
- **Randomized Output**: Each output file is shuffled to ensure random distribution
- **Batch Processing**: Process multiple directories from a text file, each getting individual results
- **Progress Tracking**: Rich progress bars and detailed statistics
- **Source Tracking**: Maintains information about which conference each record came from

**Usage**:
1. Run the script: `python extract_asia_europe_data.py`
2. Choose processing mode:
   - **Option 1**: Single directory - Enter one directory path
   - **Option 2**: Batch processing - Provide path to text file with directory paths
3. Each directory is processed individually and gets its own output file
4. Results are saved in each directory's own `asia_europe_extraction` folder with a timestamped filename

**Output**:
- **Individual Files**: Each directory gets its own CSV file containing extracted records from that directory
- **Randomized**: Each output file's records are shuffled for random distribution
- **Timestamped**: Each filename includes date/time for uniqueness
- **Local Storage**: Each output file is saved in the source directory's `asia_europe_extraction` folder
- **Comprehensive**: Includes source conference and region identification columns

## Target Countries and Domains

### China
- **TLD**: `.cn`
- **Special Domains**: `163.com`, `qq.com`, `126.com`, `sina.com`, `sohu.com`, `tom.com`, `aliyun.com`, `21cn.com`, `baidu.com`, `yeah.net`, `sogou.com`, `163.net`, `sina.net`, `chinaren.com`
- **Related**: Hong Kong (`.hk`), Macao (`.mo`), Taiwan (`.tw`)

### Japan
- **TLD**: `.jp`

### South Korea
- **TLD**: `.kr`

### European Countries (47+ countries)
- **Major Countries**: Germany (`.de`), United Kingdom (`.uk`), France (`.fr`), Italy (`.it`), Spain (`.es`), Netherlands (`.nl`), Poland (`.pl`), etc.
- **All European TLDs**: `.ad`, `.al`, `.at`, `.ba`, `.be`, `.bg`, `.by`, `.ch`, `.cy`, `.cz`, `.de`, `.dk`, `.ee`, `.es`, `.eu`, `.fi`, `.fr`, `.gb`, `.gr`, `.hr`, `.hu`, `.ie`, `.is`, `.it`, `.li`, `.lt`, `.lu`, `.lv`, `.mc`, `.md`, `.me`, `.mk`, `.mt`, `.nl`, `.no`, `.pl`, `.pt`, `.ro`, `.rs`, `.ru`, `.se`, `.si`, `.sk`, `.sm`, `.tr`, `.ua`, `.uk`, `.va`

## Output Structure

**Individual Directory Processing**: Each directory gets its own `asia_europe_extraction` folder with extracted data.

**Single Directory Example**:
```
/path/to/conference_directory/
├── original_file1.csv
├── original_file2.csv
└── asia_europe_extraction/
    └── Asia_Europe_Extract_Conference_2023_20260130_143022.csv
```

**Batch Processing Example**:
```
/path/to/conference1/
├── data.csv
└── asia_europe_extraction/
    └── Asia_Europe_Extract_Conference1_2023_20260130_143022.csv

/path/to/conference2/
├── participants.csv
└── asia_europe_extraction/
    └── Asia_Europe_Extract_Conference2_2024_20260130_143025.csv

/path/to/conference3/
├── attendees.csv
└── asia_europe_extraction/
    └── Asia_Europe_Extract_Conference3_2024_20260130_143028.csv
```

## Output File Columns

The output CSV file contains the following columns:
- **Original Data Columns**: All original columns from the source CSV files
- **Country**: Identified country based on email domain
- **Source_Conference**: Name of the conference/directory the record came from
- **Target_Region**: Primary region classification (China, Japan, South Korea, Europe)
- **European_Country**: Specific European country (only for European records)

## Directory List File Format

For batch processing, create a text file with directory paths:

```
# Conference directories to process
# Lines starting with # are comments
C:\Data\Conference_2023
C:\Data\Conference_2024
C:\Data\Workshop_2023
# Empty lines are ignored
C:\Data\Symposium_2024
```

## Requirements

- Python 3.6+
- pandas
- rich_progress (custom module for progress bars)
- Standard libraries: os, glob, re, warnings, datetime, pathlib

## Statistics and Reporting

The script provides comprehensive statistics including:
- **Processing Summary**: Total records processed vs. extracted
- **Regional Breakdown**: Records count and percentage by region (China, Japan, South Korea, Europe)
- **European Details**: Top 10 European countries by record count
- **Source Tracking**: Which conferences contributed data (batch mode)
- **Extraction Percentage**: Overall success rate of data extraction
- **File Information**: Output file details, size, and column structure
- **Randomization Confirmation**: Verification that data has been shuffled

## Error Handling

- Invalid directories are logged but don't stop processing
- CSV reading errors are handled gracefully
- Individual directory failures don't affect batch processing
- Comprehensive error messages and warnings

## Integration with Existing Scripts

These scripts use the same core functionality as:
- `hic_continents.py` - For continent-level filtering
- `hic_individual_countries.py` - For individual country filtering

The new scripts combine and optimize the best features from both existing scripts while focusing specifically on the Asia-Europe extraction requirements.
