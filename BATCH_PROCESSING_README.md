# Batch Processing Guide

Both `convert_csv_encoding.py` and `professional_data_merger.py` now support batch processing functionality that allows you to process multiple directories at once.

## How to Use Batch Processing

### Step 1: Create a Directory List File

Create a text file (e.g., `directories.txt`) containing the paths to all directories you want to process. Each directory path should be on a separate line.

**Example format:**
```
C:\Users\<USER>\OneDrive\My Files\data\project1
C:\Users\<USER>\OneDrive\My Files\data\project2
C:\Users\<USER>\OneDrive\My Files\data\project3
```

**Features of the directory list file:**
- Lines starting with `#` are treated as comments and ignored
- Empty lines are ignored
- Only valid, existing directories will be processed
- Invalid paths will be logged but won't stop the batch processing

### Step 2: Run the Scripts

When you run either script, you'll now see a new option:

```
Choose operation mode:
1. Single directory - Process files in one directory
2. Batch processing - Process files in multiple directories from a text file
```

Choose option 2 for batch processing, then provide the path to your directory list file.

## convert_csv_encoding.py Batch Processing

**What it does:**
- Processes CSV files in multiple directories
- Converts encoding to UTF-8-SIG
- Applies character preservation or transliteration based on your choice
- Creates backup directories in each processed folder

**Usage:**
1. Run the script
2. Choose "2" for batch processing
3. Choose character processing mode (preserve or transliterate)
4. Provide path to your directory list file

## professional_data_merger.py Batch Processing

**What it does:**
- Merges Excel and CSV files in multiple directories
- Creates an "output" folder in each directory with merged results
- Applies encoding fixes and standardizes column names
- Removes duplicates and prioritizes entries with author names

**Usage:**
1. Run the script
2. Choose "2" for batch processing
3. Provide path to your directory list file

## Output and Logging

- **convert_csv_encoding.py**: Creates backup folders and processes files in-place
- **professional_data_merger.py**: Creates "output" folders with merged files and detailed logs
- Both scripts provide comprehensive logging of the batch processing progress
- Summary statistics are displayed at the end of batch processing

## Example Directory List File

See `directories_example.txt` for a template you can modify with your actual directory paths.
