# Professional Data Engineering - File Merger

## Overview

This professional-grade data engineering script merges Excel (.xlsx, .xls) and CSV files from a directory with comprehensive encoding handling and character preservation. It's designed to handle international data with special characters, multiple languages, and various encoding formats.

## Features

✅ **Multi-format Support**: Handles both Excel and CSV files  
✅ **Encoding Detection**: Automatically detects and converts CSV encodings to UTF-8-SIG  
✅ **Character Preservation**: Maintains special characters, accents, and international text  
✅ **Column Standardization**: Maps columns to standard format (Author Name, Email, Article Title)  
✅ **Duplicate Removal**: Removes duplicate entries based on email addresses  
✅ **Professional Logging**: Comprehensive logging with timestamps and error tracking  
✅ **Output Management**: Creates organized output directory with timestamped files  

## Requirements

- Python 3.7+
- pandas
- chardet
- openpyxl (for Excel file support)

## Installation

1. Ensure you have Python installed
2. Install required packages:
```bash
pip install pandas chardet openpyxl
```

## Usage

### Method 1: Interactive Mode
1. Double-click `run_data_merger.bat` or run:
```bash
python professional_data_merger.py
```
2. Enter the directory path when prompted
3. The script will process all files and create an output folder

### Method 2: Command Line
```bash
python professional_data_merger.py "C:\path\to\your\data\directory"
```

## How It Works

1. **File Discovery**: Scans the input directory for .csv, .xlsx, and .xls files
2. **Encoding Detection**: For CSV files, detects current encoding and converts to UTF-8-SIG if needed
3. **Data Reading**: Reads all files with proper encoding handling
4. **Column Standardization**: Maps various column names to standard format:
   - Column 1: `Author Name` (from "Name", "Author", "Full Name", etc.)
   - Column 2: `Email` (from "Email", "E-mail", etc.)
   - Column 3: `Article Title` (from "Title", "Article", "Subject", etc.)
5. **Character Preservation**: Fixes encoding issues while preserving special characters
6. **Deduplication**: Removes duplicate entries based on email addresses
7. **Output Generation**: Creates merged CSV file in UTF-8-SIG format in the output folder

## Output

- **Location**: `{input_directory}/output/`
- **Filename**: `merged_data_YYYYMMDD_HHMMSS.csv`
- **Encoding**: UTF-8-SIG (preserves special characters and is Excel-compatible)
- **Log File**: `data_merger.log` (detailed processing information)

## Character Handling

The script handles various encoding issues including:
- Latin characters with diacritics (á, é, í, ó, ú, ñ, ç, etc.)
- Germanic characters (ä, ö, ü, ß, etc.)
- Nordic characters (å, æ, ø, etc.)
- Cyrillic characters
- Greek characters
- Common punctuation encoding issues

## Error Handling

- Comprehensive error logging
- Graceful handling of corrupted files
- Multiple encoding fallback options
- Detailed progress reporting

## Example Directory Structure

```
Input Directory/
├── file1.csv
├── file2.xlsx
├── data.xls
└── output/           (created by script)
    ├── merged_data_20260121_143022.csv
    └── data_merger.log
```

## Support

For issues or questions, check the log file for detailed error information.
