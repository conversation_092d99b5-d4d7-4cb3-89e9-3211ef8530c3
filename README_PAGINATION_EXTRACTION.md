# Magnus Group Complete Pagination Data Extraction

## 🎯 Objective
Extract data from ALL pages of Magnus Group CMS with:
- **Dropdown set to "All Conferences"**
- **All pages processed (10 results per page)**
- **Specific fields**: S.No, Conference, Name, Email, Abstract Title, Submitted On, Abstract Type

## 📋 Required Data Fields
1. **S.No** - Serial number
2. **Conference** - Conference name/type
3. **Name** - Author name
4. **Email Addresses** - Primary and secondary emails
5. **Abstract Title** - Title of the submission
6. **Submitted On** - Submission date
7. **Abstract Type** - From View/Edit details (e.g., "Oral Presentation (Virtual)")

## 🚀 Method 1: Automated Browser Scraping (Recommended)

### Prerequisites
```bash
pip install selenium beautifulsoup4 requests
```

### Run Automated Scraper
```bash
python complete_pagination_scraper.py
```

**What it does:**
1. ✅ Logs in automatically
2. ✅ Sets dropdown to "All Conferences"
3. ✅ Extracts data from each page
4. ✅ Handles pagination automatically
5. ✅ Saves to CSV and JSON

**Note**: Requires ChromeDriver to be installed and in PATH.

## 🔧 Method 2: Manual Collection (Backup)

If automated scraping fails, use manual collection:

### Step-by-Step Instructions

1. **Login**
   - Go to: https://admin.magnusgroup.biz/admin-login.php
   - Username: `<EMAIL>`
   - Password: `Magnus@38`

2. **Navigate to Abstracts**
   - Go to: https://admin.magnusgroup.biz/view-all-abstracts.php

3. **Set Dropdown**
   - Find the conference dropdown/filter
   - Select **"All Conferences"**
   - Wait for page to reload

4. **Save Each Page**
   - **Page 1**: Save as `page_1.html` (Ctrl+S → Save as HTML)
   - Click "Next" or page 2
   - **Page 2**: Save as `page_2.html`
   - **Continue for all pages** until you reach the last page
   - Save each page with format: `page_X.html`

5. **Process Saved Files**
   ```bash
   python manual_pagination_guide.py
   ```

## 📊 Expected Output Files

### CSV Format
```csv
s_no,conference,name,email_primary,email_secondary,abstract_title,submitted_on,abstract_type,page_number,row_number
1,Climate Change Conference,Dr. John Smith,<EMAIL>,<EMAIL>,Climate Impact Study,Aug 12 2024 10:56:46 AM,Oral Presentation (Virtual),1,1
```

### JSON Format
```json
[
  {
    "s_no": "1",
    "conference": "Climate Change Conference",
    "name": "Dr. John Smith",
    "email_primary": "<EMAIL>",
    "email_secondary": "<EMAIL>",
    "abstract_title": "Climate Impact Study",
    "submitted_on": "Aug 12, 2024 10:56:46 AM",
    "abstract_type": "Oral Presentation (Virtual)",
    "page_number": 1,
    "row_number": 1
  }
]
```

## 🔍 Data Extraction Details

### From Main Table
- **S.No**: First column
- **Details**: Second column contains name, emails, title, date
- **Conference**: Extracted from page context or separate column

### From View/Edit Details
- **Abstract Type**: Found in detailed form fields
- **Additional Info**: Phone, country, university, etc.

## 📈 Progress Tracking

The scripts will show:
- Current page being processed
- Records extracted per page
- Total records collected
- Data quality statistics

## 🛠️ Troubleshooting

### If Automated Scraper Fails
1. Install ChromeDriver: https://chromedriver.chromium.org/
2. Add ChromeDriver to system PATH
3. Or use Manual Method (Method 2)

### If Manual Processing Fails
1. Ensure HTML files are saved correctly
2. Check file naming: `page_1.html`, `page_2.html`, etc.
3. Verify files contain the data table

## 📞 Support

If you encounter issues:
1. Check the console output for error messages
2. Verify login credentials are correct
3. Ensure the dropdown is set to "All Conferences"
4. Try the manual method as backup

## 🎯 Final Output

You'll get complete data from ALL conferences across ALL pages with the exact fields you requested:
- S.No, Conference, Name, Email, Abstract Title, Submitted On, Abstract Type
