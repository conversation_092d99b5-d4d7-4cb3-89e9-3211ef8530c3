"""
Access Your Specific Google Sheet
This script will access your sheet: https://docs.google.com/spreadsheets/d/1fnTv_S9MEOHUX_awF6Fxgm3vTXNqtes2DlLEGcpEOv4/edit
"""

import gspread
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
import pandas as pd
import pickle
import os
from datetime import datetime

# Your sheet URL
SHEET_URL = "https://docs.google.com/spreadsheets/d/1fnTv_S9MEOHUX_awF6Fxgm3vTXNqtes2DlLEGcpEOv4/edit?gid=0#gid=0"

def authenticate_google():
    """Authenticate with Google using OAuth2"""
    SCOPES = [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive'
    ]
    
    creds = None
    
    # Check if we have saved credentials
    if os.path.exists('token.pickle'):
        with open('token.pickle', 'rb') as token:
            creds = pickle.load(token)
    
    # If there are no valid credentials, get new ones
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            print("🔄 Refreshing credentials...")
            creds.refresh(Request())
        else:
            print("🔐 Please authenticate with your Google account...")
            
            # Create a simple credentials.json if it doesn't exist
            if not os.path.exists('credentials.json'):
                create_credentials_file()
            
            flow = InstalledAppFlow.from_client_secrets_file('credentials.json', SCOPES)
            creds = flow.run_local_server(port=0)
        
        # Save credentials for next time
        with open('token.pickle', 'wb') as token:
            pickle.dump(creds, token)
    
    return gspread.authorize(creds)

def create_credentials_file():
    """Create a template credentials file"""
    import json
    
    print("\n❌ credentials.json not found!")
    print("You need to create OAuth2 credentials from Google Cloud Console.")
    print("\n📋 Quick Setup:")
    print("1. Go to: https://console.developers.google.com/")
    print("2. Create project → Enable Google Sheets API & Google Drive API")
    print("3. Create OAuth 2.0 Client ID (Desktop application)")
    print("4. Download JSON file and save as 'credentials.json'")
    
    # Create a template
    template = {
        "installed": *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
    
    with open('credentials_template.json', 'w') as f:
        json.dump(template, f, indent=2)
    
    print("📄 Created credentials_template.json as reference")
    raise FileNotFoundError("Please create credentials.json first")

def access_your_sheet():
    """Access and work with your specific Google Sheet"""
    print("🔗 ACCESSING YOUR GOOGLE SHEET")
    print("=" * 40)
    print(f"Sheet URL: {SHEET_URL}")
    
    try:
        # Authenticate
        print("1. Authenticating with Google...")
        gc = authenticate_google()
        print("✅ Authentication successful!")
        
        # Open your specific sheet
        print("2. Opening your sheet...")
        sheet = gc.open_by_url(SHEET_URL)
        worksheet = sheet.sheet1  # First worksheet
        print(f"✅ Opened sheet: {sheet.title}")
        
        # Read all data
        print("3. Reading data from sheet...")
        data = worksheet.get_all_records()
        df = pd.DataFrame(data)
        
        if df.empty:
            print("📋 Sheet appears to be empty or has no headers")
            # Get all values including headers
            all_values = worksheet.get_all_values()
            if all_values:
                print("Raw data from sheet:")
                for i, row in enumerate(all_values[:5]):  # Show first 5 rows
                    print(f"Row {i+1}: {row}")
        else:
            print(f"✅ Read {len(df)} rows and {len(df.columns)} columns")
            print("\n📊 Data Preview:")
            print(df.head())
            print(f"\n📋 Columns: {list(df.columns)}")
        
        return gc, sheet, worksheet, df
        
    except Exception as e:
        print(f"❌ Error accessing sheet: {e}")
        return None, None, None, None

def demonstrate_operations(gc, sheet, worksheet, df):
    """Demonstrate common operations on your sheet"""
    if df is None or df.empty:
        print("⚠️ No data to work with")
        return
    
    print("\n🛠️ DEMONSTRATING OPERATIONS")
    print("=" * 40)
    
    # Show current data
    print("Current data:")
    print(df)
    
    # Add a timestamp column
    print("\n1. Adding timestamp column...")
    df['Last_Updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Example: If there's an email column, clean it
    email_columns = [col for col in df.columns if 'email' in col.lower()]
    if email_columns:
        email_col = email_columns[0]
        print(f"2. Cleaning email column: {email_col}")
        df[email_col] = df[email_col].str.strip().str.lower()
    
    # Write back to sheet
    print("3. Writing updated data back to sheet...")
    
    # Clear the sheet first
    worksheet.clear()
    
    # Convert DataFrame to list of lists (including headers)
    data_to_write = [df.columns.tolist()] + df.values.tolist()
    
    # Update the sheet
    worksheet.update('A1', data_to_write)
    
    print("✅ Data updated successfully!")
    print(f"📊 Updated sheet URL: {sheet.url}")

def main():
    """Main function"""
    print("📊 GOOGLE SHEETS ACCESS - YOUR SPECIFIC SHEET")
    print("=" * 50)
    
    # Access your sheet
    gc, sheet, worksheet, df = access_your_sheet()
    
    if gc is None:
        print("❌ Failed to access sheet. Please check authentication.")
        return
    
    # Ask what you want to do
    print("\n🤔 What would you like to do?")
    print("1. Just view the data")
    print("2. Demonstrate operations (add timestamp, clean data)")
    print("3. Add new data")
    print("4. Export to CSV")
    
    try:
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "1":
            print("✅ Data viewing complete!")
            
        elif choice == "2":
            demonstrate_operations(gc, sheet, worksheet, df)
            
        elif choice == "3":
            print("\n➕ Adding new data...")
            # Example: Add a new row
            new_row = ["New Entry", "<EMAIL>", "Active", datetime.now().strftime('%Y-%m-%d')]
            worksheet.append_row(new_row)
            print("✅ New row added!")
            
        elif choice == "4":
            if not df.empty:
                filename = f"sheet_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"✅ Data exported to: {filename}")
            else:
                print("❌ No data to export")
        
        else:
            print("Invalid choice")
            
    except KeyboardInterrupt:
        print("\n\nOperation cancelled")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
