# Asia-Europe Data Extraction - Directory List
# This file contains paths to directories for batch processing
# Lines starting with # are comments and will be ignored
# Each line should contain a full path to a directory containing CSV files

# Instructions:
# 1. Replace the example paths below with your actual directory paths
# 2. Each directory should contain CSV files from conference datasets
# 3. The script will extract data from China, Japan, Korea, and European countries
# 4. Results will be saved in an 'asia_europe_extraction' folder in each directory

# Example directory paths (replace with your actual paths):
# C:\Users\<USER>\OneDrive\My Files\Conference_2023
# C:\Users\<USER>\OneDrive\My Files\Conference_2024
# C:\Users\<USER>\OneDrive\My Files\Workshop_2023
# C:\Users\<USER>\OneDrive\My Files\Symposium_2024

# Sample format for your directories:
# /path/to/conference1
# /path/to/conference2
# /path/to/conference3

# Notes:
# - Empty lines are ignored
# - Only existing directories will be processed
# - Invalid paths will be logged but won't stop the batch processing
# - Each directory will get its own extraction results folder
