#!/usr/bin/env python3
"""
Asian Countries Data Separator Script

Separates CSV data by specific Asian countries based on email domains.
Based on the pattern from custom_countries_separator.py but focused on key Asian markets.

Countries included:
China, Korea (South Korea), Japan, India, Singapore, Malaysia

Author: AI Assistant
Date: 2025-11-27
"""

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
import rich_progress

# Suppress pandas warnings - compatible with both old and new pandas versions
try:
    # Try the new location first (pandas >= 1.5.0)
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        # Try the old location (pandas < 1.5.0)
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        # If neither works, just ignore pandas warnings in general
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

# TLD to Country Name Mapping for Asian countries
asian_countries_mapping = {
    '.cn': 'China',
    '.kr': 'South Korea',
    '.jp': 'Japan',
    '.in': 'India',
    '.sg': 'Singapore',
    '.my': 'Malaysia'
}

# Special domains for certain countries
special_domains = {
    # Chinese popular email domains
    '126.com': 'China',
    '163.com': 'China',
    'qq.com': 'China',
    'sina.com': 'China',
    'sina.cn': 'China',
    'sohu.com': 'China',
    'yeah.net': 'China',
    'tom.com': 'China',
    'foxmail.com': 'China',
    'aliyun.com': 'China',
    'vip.sina.com': 'China',
    'vip.163.com': 'China',
    'vip.126.com': 'China',
    '188.com': 'China',
    '139.com': 'China',
    'wo.com.cn': 'China',
    'chinaunicom.cn': 'China',
    '21cn.com': 'China',
    'eyou.com': 'China',
    'cntv.cn': 'China',
    # Chinese educational and government domains
    'edu.cn': 'China',
    'ac.cn': 'China',
    'gov.cn': 'China',
    'org.cn': 'China',
    'net.cn': 'China',
    'com.cn': 'China',

    # South Korean popular email domains
    'naver.com': 'South Korea',
    'daum.net': 'South Korea',
    'hanmail.net': 'South Korea',
    'nate.com': 'South Korea',
    'korea.com': 'South Korea',
    'chol.com': 'South Korea',
    'dreamwiz.com': 'South Korea',
    'empal.com': 'South Korea',
    'freechal.com': 'South Korea',
    'lycos.co.kr': 'South Korea',
    'paran.com': 'South Korea',
    # Korean educational and government domains
    'ac.kr': 'South Korea',
    'edu.kr': 'South Korea',
    'go.kr': 'South Korea',
    'or.kr': 'South Korea',
    'co.kr': 'South Korea',
    'ne.kr': 'South Korea',

    # Japanese popular email domains
    'yahoo.co.jp': 'Japan',
    'docomo.ne.jp': 'Japan',
    'ezweb.ne.jp': 'Japan',
    'softbank.ne.jp': 'Japan',
    'au.com': 'Japan',
    'i.softbank.jp': 'Japan',
    'willcom.com': 'Japan',
    'emobile.ne.jp': 'Japan',
    'biglobe.ne.jp': 'Japan',
    'nifty.com': 'Japan',
    'so-net.ne.jp': 'Japan',
    'ocn.ne.jp': 'Japan',
    'plala.or.jp': 'Japan',
    'dion.ne.jp': 'Japan',
    # Japanese educational and government domains
    'ac.jp': 'Japan',
    'ed.jp': 'Japan',
    'go.jp': 'Japan',
    'or.jp': 'Japan',
    'co.jp': 'Japan',
    'ne.jp': 'Japan',

    # Indian popular email domains
    'rediffmail.com': 'India',
    'sify.com': 'India',
    'in.com': 'India',
    'indiatimes.com': 'India',
    'vsnl.com': 'India',
    'sancharnet.in': 'India',
    'dataone.in': 'India',
    'eth.net': 'India',
    'mantraonline.com': 'India',
    'satyam.net.in': 'India',
    'touchtelindia.net': 'India',
    'airtelmail.in': 'India',
    'bsnl.in': 'India',
    'railmail.co.in': 'India',
    # Indian educational and government domains
    'ac.in': 'India',
    'edu.in': 'India',
    'gov.in': 'India',
    'nic.in': 'India',
    'org.in': 'India',
    'net.in': 'India',
    'co.in': 'India',

    # Singapore popular email domains
    'singnet.com.sg': 'Singapore',
    'pacific.net.sg': 'Singapore',
    'starhub.net.sg': 'Singapore',
    'cyberway.com.sg': 'Singapore',
    'tm.net.sg': 'Singapore',
    'asia.com': 'Singapore',
    'asiapac.net': 'Singapore',
    # Singapore educational and government domains
    'edu.sg': 'Singapore',
    'gov.sg': 'Singapore',
    'org.sg': 'Singapore',
    'net.sg': 'Singapore',
    'com.sg': 'Singapore',

    # Malaysian popular email domains
    'tm.net.my': 'Malaysia',
    'streamyx.com': 'Malaysia',
    'jaring.my': 'Malaysia',
    'maxis.com.my': 'Malaysia',
    'celcom.net.my': 'Malaysia',
    'digi.com.my': 'Malaysia',
    'time.net.my': 'Malaysia',
    'pc.jaring.my': 'Malaysia',
    'net.my': 'Malaysia',
    # Malaysian educational and government domains
    'edu.my': 'Malaysia',
    'gov.my': 'Malaysia',
    'org.my': 'Malaysia',
    'net.my': 'Malaysia',
    'com.my': 'Malaysia',
}

def extract_segment_from_path(path):
    """
    Extract conference segment name from path.

    Args:
        path (str): The file path to extract segment from

    Returns:
        str or None: The extracted segment name or None if not found
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        segment = match.group(1)
        rich_progress.print_status(f"Found segment: {segment}", "success")
        return segment
    else:
        rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
        # Prompt for manual input
        segment = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
        if segment.strip():
            rich_progress.print_status(f"Using manually entered segment: {segment}", "info")
            return segment
        else:
            rich_progress.print_status("No segment name provided", "error")
            return None

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special domains first (more specific matches)
    for domain, country in special_domains.items():
        if email.endswith(domain):
            return country

    # Check for Asian countries TLDs (general country domains)
    for tld, country in asian_countries_mapping.items():
        if email.endswith(tld):
            return country

    # If no match found, return 'Other'
    return 'Other'

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("Asian Countries Email Domain Filtering Tool")

# Get the path from the user
print_section("Input Path")
path = input("Loc: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")
csn = extract_segment_from_path(path)
if not csn:
    rich_progress.print_status("No segment name provided. Exiting.", "error")
    exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Read and concatenate CSV files with progress bar
print_section("Reading CSV Files")
rich_progress.print_status(f"Reading {len(csv_files)} CSV files...", "info")

# Create a progress bar for reading CSV files
read_bar, update_read = rich_progress.create_progress_bar(
    total=len(csv_files),
    description="Reading CSV files",
    color_scheme="blue"
)

# Read each CSV file with progress tracking
dfs = []
for csv_file in csv_files:
    try:
        df = pd.read_csv(csv_file, low_memory=False)
        dfs.append(df)
        update_read(1, f"Read {csv_file}")
    except Exception as e:
        rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")
        update_read(1, f"Error with {csv_file}")

# Stop the progress bar
read_bar.stop()

# Concatenate all dataframes
rich_progress.print_status("Concatenating files...", "info")
d2_EVENT = pd.concat(dfs, ignore_index=True)
rich_progress.print_status(f"Successfully read {len(d2_EVENT)} records from {len(csv_files)} files", "success")

# Add Country column
print_section("Adding Country Column")
rich_progress.print_status("Extracting country information from email domains...", "info")
d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)
rich_progress.print_status(f"Successfully added Country column to {len(d2_EVENT)} records", "success")

# Create filters for each Asian country
print_section("Creating Asian Country Filters")
rich_progress.print_status("Setting up filters for Asian countries...", "info")

# Create a dictionary to store country dataframes
country_dfs = {}

# Create a progress bar for filtering
filter_bar, update_filter = rich_progress.create_progress_bar(
    total=len(asian_countries_mapping),
    description="Creating country filters",
    color_scheme="green"
)

# Filter for each Asian country using the Country column (which includes both TLD and special domains)
for tld, country_name in asian_countries_mapping.items():
    # Use the Country column that was created by extract_country_from_email function
    # This will include both TLD matches (.cn) and special domain matches (126.com, qq.com, etc.)
    country_filter = d2_EVENT["Country"] == country_name
    country_dfs[country_name] = d2_EVENT[country_filter]
    update_filter(1, f"Filtered {country_name}")

# Stop the progress bar
filter_bar.stop()

# Print filtering results
print_section("Filtering Results")
rich_progress.print_status("Asian country filtering complete. Results:", "success")
rich_progress.print_status("-" * 50, "info")
rich_progress.print_status(f"{'Country':<25} {'Count':>10}", "info")
rich_progress.print_status("-" * 50, "info")

# Print counts for each Asian country (sorted by count)
country_counts = [(country, len(df)) for country, df in country_dfs.items()]
country_counts.sort(key=lambda x: x[1], reverse=True)

for country, count in country_counts:
    rich_progress.print_status(f"{country:<25} {count:>10}", "info")

rich_progress.print_status("-" * 50, "info")

# Print country distribution from the Country column
print_section("Country Distribution Summary")
country_distribution = d2_EVENT['Country'].value_counts()
rich_progress.print_status(f"Total unique countries identified: {len(country_distribution)}", "info")
asian_countries_found = [c for c in country_distribution.index if c in asian_countries_mapping.values()]
rich_progress.print_status(f"Asian countries found: {len(asian_countries_found)}", "info")
rich_progress.print_status(f"Asian countries with data: {', '.join(asian_countries_found)}", "info")

# Create output directory
print_section("Creating Output Directory")
countries_dir = os.path.join(os.getcwd(), "sorted")

# Create directory
os.makedirs(countries_dir, exist_ok=True)

rich_progress.print_status(f"Created output directory: {countries_dir}", "info")

# Prepare files to save
print_section("Preparing Output Files")
files_to_save = []

# Add country files (only for countries with records)
for country_name, df in country_dfs.items():
    if len(df) > 0:
        # Ensure no duplicates in the dataframe (safety check)
        df_clean = df.drop_duplicates()

        # Remove the Country column from output (keep only original columns)
        if 'Country' in df_clean.columns:
            df_clean = df_clean.drop('Country', axis=1)

        # Clean country name for filename (remove spaces, special characters)
        clean_country_name = country_name.replace(" ", "_").replace(".", "")

        # Add both CSV and XLSX files
        csv_filename = f"{csn}_{clean_country_name}.csv"
        xlsx_filename = f"{csn}_{clean_country_name}.xlsx"
        files_to_save.append((df_clean, csv_filename, country_name, countries_dir, 'csv'))
        files_to_save.append((df_clean, xlsx_filename, country_name, countries_dir, 'xlsx'))

        # Log if duplicates were found and removed
        if len(df) != len(df_clean):
            rich_progress.print_status(f"Removed {len(df) - len(df_clean)} duplicate records from {country_name}", "warning")

rich_progress.print_status(f"Prepared {len(files_to_save)} files for saving", "info")

# Save files with progress tracking
print_section("Saving Output Files")

# Create a progress bar for saving files
save_bar, update_save = rich_progress.create_progress_bar(
    total=len(files_to_save),
    description="Saving files",
    color_scheme="purple"
)

# Save each file with progress tracking
saved_count = 0
error_count = 0

for df, filename, category, directory, file_format in files_to_save:
    try:
        output_path = os.path.join(directory, filename)

        if file_format == 'csv':
            df.to_csv(output_path, encoding='utf-8-sig', index=False)
            update_save(1, f"Saved {category} CSV ({len(df)} records)")
        elif file_format == 'xlsx':
            # Save XLSX without header formatting (plain text headers)
            from openpyxl.styles import Font, Alignment, Border

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Sheet1')
                # Get the workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets['Sheet1']

                # Remove any default formatting from headers (keep them as plain text)
                for cell in worksheet[1]:  # First row (headers)
                    cell.font = Font(bold=False)
                    cell.alignment = Alignment(horizontal='left', vertical='top')
                    cell.border = Border()  # Remove all borders

            update_save(1, f"Saved {category} XLSX ({len(df)} records)")

        saved_count += 1
    except Exception as e:
        rich_progress.print_status(f"Error saving {filename}: {str(e)}", "error")
        update_save(1, f"Error with {filename}")
        error_count += 1

# Stop the progress bar
save_bar.stop()

# Print completion message
print_header("Processing Completed Successfully!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")
rich_progress.print_status(f"Files successfully saved: {saved_count}", "success")
if error_count > 0:
    rich_progress.print_status(f"Files with errors: {error_count}", "error")

rich_progress.print_status(f"Output directory: {countries_dir}", "info")

# Print summary of saved files
print_section("Saved Files Summary")
rich_progress.print_status("-" * 60, "info")
rich_progress.print_status(f"{'Filename':<35} {'Records':>10} {'Country':>15}", "info")
rich_progress.print_status("-" * 60, "info")

for df, filename, category, directory, file_format in files_to_save:
    rich_progress.print_status(f"{filename:<35} {len(df):>10} {category:>15}", "info")

rich_progress.print_status("-" * 60, "info")
rich_progress.print_status("All files saved in both CSV (utf-8-sig) and XLSX formats in 'sorted' directory", "success")
rich_progress.print_status(f"Only the specified 6 Asian countries were processed", "info")
