#!/usr/bin/env python3
"""
Batch Asia-Europe Data Extraction Script
========================================

This script performs batch extraction of data from China, Japan, Korea, and all European countries
across multiple directories specified in a text file. It uses the same filtering logic as the
single-directory version but processes multiple conference datasets at once.

Target Countries:
- China (including special Chinese domains)
- Japan
- South Korea
- All European countries

Author: Data Engineering Team
Version: 1.0.0
Date: 2026-01-30
"""

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime
from pathlib import Path

# Import rich_progress for gradient progress bars
import rich_progress

# Suppress pandas warnings
try:
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

# TLD to Country Name Mapping (comprehensive)
tld_to_country = {
    # European countries
    '.ad': 'Andorra', '.al': 'Albania', '.at': 'Austria', '.ba': 'Bosnia and Herzegovina',
    '.be': 'Belgium', '.bg': 'Bulgaria', '.by': 'Belarus', '.ch': 'Switzerland',
    '.cy': 'Cyprus', '.cz': 'Czech Republic', '.de': 'Germany', '.dk': 'Denmark',
    '.ee': 'Estonia', '.es': 'Spain', '.eu': 'European Union', '.fi': 'Finland',
    '.fr': 'France', '.gb': 'United Kingdom', '.gr': 'Greece', '.hr': 'Croatia',
    '.hu': 'Hungary', '.ie': 'Ireland', '.is': 'Iceland', '.it': 'Italy',
    '.li': 'Liechtenstein', '.lt': 'Lithuania', '.lu': 'Luxembourg', '.lv': 'Latvia',
    '.mc': 'Monaco', '.md': 'Moldova', '.me': 'Montenegro', '.mk': 'North Macedonia',
    '.mt': 'Malta', '.nl': 'Netherlands', '.no': 'Norway', '.pl': 'Poland',
    '.pt': 'Portugal', '.ro': 'Romania', '.rs': 'Serbia', '.ru': 'Russia',
    '.se': 'Sweden', '.si': 'Slovenia', '.sk': 'Slovakia', '.sm': 'San Marino',
    '.tr': 'Turkey', '.ua': 'Ukraine', '.uk': 'United Kingdom', '.va': 'Vatican City',
    
    # Asian countries (China, Japan, Korea)
    '.cn': 'China', '.jp': 'Japan', '.kr': 'South Korea', '.kp': 'North Korea',
    '.hk': 'Hong Kong', '.mo': 'Macao', '.tw': 'Taiwan',
    
    # Special domains
    '.edu': 'United States', '.gov': 'United States', '.mil': 'United States'
}

# Special Chinese email domains mapping
chinese_domains = {
    '163.com': 'China', 'qq.com': 'China', '126.com': 'China', 'sina.com': 'China',
    'sohu.com': 'China', 'tom.com': 'China', 'aliyun.com': 'China', '21cn.com': 'China',
    'baidu.com': 'China', 'yeah.net': 'China', 'sogou.com': 'China', '163.net': 'China',
    'sina.net': 'China', 'chinaren.com': 'China'
}

# European country TLDs
european_tlds = [
    '.ad', '.al', '.at', '.ba', '.be', '.bg', '.by', '.ch', '.cy', '.cz', 
    '.de', '.dk', '.ee', '.es', '.eu', '.fi', '.fr', '.gb', '.gr', '.hr', 
    '.hu', '.ie', '.is', '.it', '.li', '.lt', '.lu', '.lv', '.mc', '.md', 
    '.me', '.mk', '.mt', '.nl', '.no', '.pl', '.pt', '.ro', '.rs', '.ru', 
    '.se', '.si', '.sk', '.sm', '.tr', '.ua', '.uk', '.va'
]

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special Chinese domains first
    for domain, country in chinese_domains.items():
        if email.endswith(domain):
            return country

    # Check for country TLDs
    for tld, country in tld_to_country.items():
        if email.endswith(tld):
            return country

    return 'Other'

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 70, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def read_directories_from_file(file_path):
    """Read directory paths from a text file."""
    directories = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # Skip empty lines and comments
                    if os.path.exists(line) and os.path.isdir(line):
                        directories.append(line)
                        rich_progress.print_status(f"✓ Valid directory: {line}", "success")
                    else:
                        rich_progress.print_status(f"⚠ Line {line_num}: Directory not found: {line}", "warning")
    except FileNotFoundError:
        rich_progress.print_status(f"Error: File not found: {file_path}", "error")
        return []
    except Exception as e:
        rich_progress.print_status(f"Error reading file {file_path}: {str(e)}", "error")
        return []
    
    return directories

def extract_conference_name(path):
    """Extract conference segment name from path."""
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)

    if match:
        return match.group(1)
    else:
        # Try to extract from directory name
        dir_name = os.path.basename(path.rstrip('\\'))
        if dir_name:
            return dir_name
        return "Unknown_Conference"

def process_single_directory(directory_path):
    """Process a single directory and extract Asia-Europe data."""
    try:
        original_dir = os.getcwd()
        os.chdir(directory_path)

        # Extract conference segment name
        csn = extract_conference_name(directory_path)
        rich_progress.print_status(f"Processing: {csn}", "info")

        # Find CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            rich_progress.print_status(f"No CSV files found in {directory_path}", "warning")
            return None, 0

        # Read and concatenate CSV files
        dfs = []
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file, low_memory=False)
                dfs.append(df)
            except Exception as e:
                rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")

        if not dfs:
            rich_progress.print_status(f"No valid CSV files could be read from {directory_path}", "error")
            return None, 0

        # Concatenate all dataframes
        d2_EVENT = pd.concat(dfs, ignore_index=True)

        # Add Country column
        d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)

        # Create filters for target countries
        country_data = {}

        # Filter China (including special domains)
        china_mask = pd.Series(False, index=d2_EVENT.index)
        china_mask = china_mask | d2_EVENT["Email"].str.endswith('.cn', na=False)

        for domain in chinese_domains.keys():
            china_mask = china_mask | d2_EVENT["Email"].str.endswith(domain, na=False)

        for tld in ['.hk', '.mo', '.tw']:
            china_mask = china_mask | d2_EVENT["Email"].str.endswith(tld, na=False)

        country_data['China'] = d2_EVENT[china_mask]

        # Filter Japan
        japan_mask = d2_EVENT["Email"].str.endswith('.jp', na=False)
        country_data['Japan'] = d2_EVENT[japan_mask]

        # Filter South Korea
        korea_mask = d2_EVENT["Email"].str.endswith('.kr', na=False)
        country_data['South_Korea'] = d2_EVENT[korea_mask]

        # Filter European countries
        europe_mask = pd.Series(False, index=d2_EVENT.index)
        for tld in european_tlds:
            europe_mask = europe_mask | d2_EVENT["Email"].str.endswith(tld, na=False)
        country_data['Europe'] = d2_EVENT[europe_mask]

        # Filter individual European countries
        individual_european_countries = {}
        for tld in european_tlds:
            country_mask = d2_EVENT["Email"].str.endswith(tld, na=False)
            country_df = d2_EVENT[country_mask]

            if len(country_df) > 0:
                country_name = tld_to_country.get(tld, tld.replace('.', '').upper())
                individual_european_countries[country_name] = country_df

        # Create output directories
        base_output_dir = os.path.join(directory_path, "asia_europe_extraction")
        os.makedirs(base_output_dir, exist_ok=True)

        asia_dir = os.path.join(base_output_dir, "asia")
        europe_dir = os.path.join(base_output_dir, "europe")
        individual_europe_dir = os.path.join(base_output_dir, "individual_european_countries")

        os.makedirs(asia_dir, exist_ok=True)
        os.makedirs(europe_dir, exist_ok=True)
        os.makedirs(individual_europe_dir, exist_ok=True)

        # Save files
        files_saved = 0

        # Save Asian countries
        for country_key, display_name in [('China', 'China'), ('Japan', 'Japan'), ('South_Korea', 'South_Korea')]:
            if len(country_data[country_key]) > 0:
                filepath = os.path.join(asia_dir, f"{csn}_{display_name}.csv")
                country_data[country_key].to_csv(filepath, encoding='utf-8-sig', index=False)
                files_saved += 1

        # Save combined Europe
        if len(country_data['Europe']) > 0:
            filepath = os.path.join(europe_dir, f"{csn}_Europe_Combined.csv")
            country_data['Europe'].to_csv(filepath, encoding='utf-8-sig', index=False)
            files_saved += 1

        # Save individual European countries
        for country_name, df in individual_european_countries.items():
            if len(df) > 0:
                safe_name = country_name.replace(' ', '_').replace('&', 'and')
                filepath = os.path.join(individual_europe_dir, f"{csn}_{safe_name}.csv")
                df.to_csv(filepath, encoding='utf-8-sig', index=False)
                files_saved += 1

        # Calculate statistics
        total_extracted = (len(country_data['China']) + len(country_data['Japan']) +
                          len(country_data['South_Korea']) + len(country_data['Europe']))

        stats = {
            'csn': csn,
            'total_records': len(d2_EVENT),
            'china_records': len(country_data['China']),
            'japan_records': len(country_data['Japan']),
            'korea_records': len(country_data['South_Korea']),
            'europe_records': len(country_data['Europe']),
            'individual_countries': len(individual_european_countries),
            'files_saved': files_saved,
            'total_extracted': total_extracted,
            'output_dir': base_output_dir
        }

        os.chdir(original_dir)
        return stats, files_saved

    except Exception as e:
        rich_progress.print_status(f"Error processing {directory_path}: {str(e)}", "error")
        try:
            os.chdir(original_dir)
        except:
            pass
        return None, 0

def main():
    """Main function for batch processing."""
    print_header("Batch Asia-Europe Data Extraction Script")
    rich_progress.print_status("Batch processing for China, Japan, Korea, and European countries", "info")

    # Get directories file path
    print_section("Input Configuration")
    directories_file = input("Enter the path to the text file containing directory paths: ").strip().strip('"\'')

    if not directories_file:
        rich_progress.print_status("Error: No file path specified", "error")
        return

    # Read directories from file
    print_section("Reading Directory List")
    directories = read_directories_from_file(directories_file)

    if not directories:
        rich_progress.print_status("No valid directories found. Exiting.", "error")
        return

    rich_progress.print_status(f"Found {len(directories)} valid directories to process", "success")

    # Process each directory
    print_section("Batch Processing")

    # Create overall progress bar
    batch_bar, update_batch = rich_progress.create_progress_bar(
        total=len(directories),
        description="Processing directories",
        color_scheme="blue"
    )

    # Initialize statistics
    all_stats = []
    total_files_saved = 0
    successful_dirs = 0

    for i, directory in enumerate(directories, 1):
        rich_progress.print_status(f"\nProcessing directory {i}/{len(directories)}: {directory}", "info")

        stats, files_saved = process_single_directory(directory)

        if stats:
            all_stats.append(stats)
            total_files_saved += files_saved
            successful_dirs += 1

            rich_progress.print_status(f"✓ Completed {stats['csn']}: {stats['total_extracted']} records extracted, {files_saved} files saved", "success")
        else:
            rich_progress.print_status(f"✗ Failed to process {directory}", "error")

        update_batch(1, f"Processed {os.path.basename(directory)}")

    batch_bar.stop()

    # Print final summary
    print_header("Batch Processing Completed!")
    rich_progress.print_status(f"Directories processed successfully: {successful_dirs}/{len(directories)}", "success")
    rich_progress.print_status(f"Total files saved: {total_files_saved}", "success")

    if all_stats:
        print_section("Processing Summary")
        rich_progress.print_status("-" * 80, "info")
        rich_progress.print_status(f"{'Conference':<25} {'Total':>8} {'China':>8} {'Japan':>8} {'Korea':>8} {'Europe':>8} {'Files':>8}", "info")
        rich_progress.print_status("-" * 80, "info")

        total_all_records = 0
        total_china = 0
        total_japan = 0
        total_korea = 0
        total_europe = 0

        for stats in all_stats:
            rich_progress.print_status(
                f"{stats['csn'][:24]:<25} "
                f"{stats['total_records']:>8} "
                f"{stats['china_records']:>8} "
                f"{stats['japan_records']:>8} "
                f"{stats['korea_records']:>8} "
                f"{stats['europe_records']:>8} "
                f"{stats['files_saved']:>8}", "info"
            )

            total_all_records += stats['total_records']
            total_china += stats['china_records']
            total_japan += stats['japan_records']
            total_korea += stats['korea_records']
            total_europe += stats['europe_records']

        rich_progress.print_status("-" * 80, "info")
        rich_progress.print_status(
            f"{'TOTALS':<25} "
            f"{total_all_records:>8} "
            f"{total_china:>8} "
            f"{total_japan:>8} "
            f"{total_korea:>8} "
            f"{total_europe:>8} "
            f"{total_files_saved:>8}", "success"
        )
        rich_progress.print_status("-" * 80, "info")

        # Calculate overall extraction percentage
        total_extracted = total_china + total_japan + total_korea + total_europe
        if total_all_records > 0:
            extraction_percentage = (total_extracted / total_all_records) * 100
            rich_progress.print_status(f"Overall extraction percentage: {extraction_percentage:.2f}%", "success")

    rich_progress.print_status("\n🎉 Batch processing completed successfully!", "header")
    rich_progress.print_status("📁 Check the 'asia_europe_extraction' folders in each processed directory", "info")

    # Pause for user to see results
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
