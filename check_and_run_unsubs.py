#!/usr/bin/env python
"""
<PERSON>ript to check if the specified directory contains any CSV files.
If no CSV files are found, it runs the @run_daily_unsubs.bat file.

Directory to check: H:\Master Bounces and Unsubs\Postpanel Unsubs
"""

import os
import glob
import subprocess
import logging
from datetime import datetime

# Configure logging
# Create logs directory if it doesn't exist
logs_dir = "logs"
os.makedirs(logs_dir, exist_ok=True)

log_filename = os.path.join(logs_dir, f"check_and_run_unsubs_log_{datetime.now().strftime('%Y%m%d')}.txt")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

def check_for_csv_files(directory="."):
    """
    Check if the specified directory contains any CSV files.

    Args:
        directory (str): Directory to check for CSV files. Default is current directory.

    Returns:
        bool: True if CSV files are found, False otherwise.
    """
    try:
        # Check if directory exists
        if not os.path.exists(directory):
            logging.error(f"Directory does not exist: {directory}")
            return False

        # Change to the specified directory
        os.chdir(directory)
        logging.info(f"Checking for CSV files in: {os.getcwd()}")

        # Find all CSV files
        csv_files = glob.glob('*.csv')

        if csv_files:
            logging.info(f"Found {len(csv_files)} CSV files: {', '.join(csv_files[:5])}{'...' if len(csv_files) > 5 else ''}")
            return True
        else:
            logging.info("No CSV files found in the directory.")
            return False

    except Exception as e:
        logging.error(f"Error checking for CSV files: {str(e)}")
        return False

def run_daily_unsubs_batch():
    """
    Run the @run_daily_unsubs.bat file.

    Returns:
        bool: True if the batch file executed successfully, False otherwise.
    """
    try:
        batch_file = "@run_daily_unsubs.bat"
        logging.info(f"Running batch file: {batch_file}")

        # Run the batch file and capture output
        process = subprocess.Popen(
            batch_file,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True
        )

        # Get output and error
        stdout, stderr = process.communicate()

        # Log the output
        if stdout:
            logging.info(f"Batch file output: {stdout.decode('utf-8', errors='ignore')}")

        # Check if there were any errors
        if stderr:
            logging.error(f"Batch file error: {stderr.decode('utf-8', errors='ignore')}")

        # Check return code
        if process.returncode == 0:
            logging.info("Batch file executed successfully.")
            return True
        else:
            logging.error(f"Batch file failed with return code: {process.returncode}")
            return False

    except Exception as e:
        logging.error(f"Error running batch file: {str(e)}")
        return False

def main():
    """Main function to check for CSV files and run batch file if needed."""
    logging.info("Starting check_and_run_unsubs script")

    # Define the directory to check
    target_directory = r"H:\Master Bounces and Unsubs\Postpanel Unsubs"
    logging.info(f"Target directory: {target_directory}")

    # Store the current directory to return to it later
    original_directory = os.getcwd()

    # Check if there are any CSV files in the target directory
    has_csv_files = check_for_csv_files(target_directory)

    # Return to the original directory
    try:
        os.chdir(original_directory)
    except Exception as e:
        logging.error(f"Error returning to original directory: {str(e)}")

    if not has_csv_files:
        logging.info("No CSV files found. Running @run_daily_unsubs.bat")
        success = run_daily_unsubs_batch()

        if success:
            logging.info("Process completed successfully.")
        else:
            logging.error("Process completed with errors.")
    else:
        logging.info("CSV files found. No need to run @run_daily_unsubs.bat")

    logging.info("Script execution completed.")

if __name__ == "__main__":
    main()
