import json
import csv
import re
from datetime import datetime
from urllib.parse import unquote, parse_qs

def parse_details_field(details_text):
    """Parse the Details field to extract structured information"""
    data = {}

    # Extract email - look for the first valid email
    email_matches = re.findall(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', details_text)
    if email_matches:
        data['email'] = email_matches[0]  # Take the first email found

    # Extract abstract title
    title_match = re.search(r'Abstract Title:([^S]+?)Submitted On:', details_text)
    if title_match:
        data['abstract_title'] = title_match.group(1).strip()

    # Extract submission date
    date_match = re.search(r'Submitted On:([^$]+)', details_text)
    if date_match:
        data['submission_date'] = date_match.group(1).strip()

    # Extract name - look for pattern like "Name,University" or just "Name"
    # First try to find name before comma and university
    name_uni_match = re.search(r'([A-Z][a-z]+ [A-Z][a-z]+),([^,]+University[^,]*)', details_text)
    if name_uni_match:
        data['name'] = name_uni_match.group(1).strip()
        data['university'] = name_uni_match.group(2).strip()

        # Extract country after university
        country_match = re.search(r'University[^,]*,([^@]+?)(?:[a-zA-Z0-9._%+-]+@|Set|$)', details_text)
        if country_match:
            data['country'] = country_match.group(1).strip()
    else:
        # Try to find name in other patterns
        name_match = re.search(r'([A-Z][a-z]+ [A-Z][a-z]+)', details_text)
        if name_match and '@' not in name_match.group(1) and 'Set Reminder' not in name_match.group(1):
            data['name'] = name_match.group(1)

        # Extract university/institution separately
        uni_match = re.search(r',([^,]+University[^,]*)', details_text)
        if uni_match:
            data['university'] = uni_match.group(1).strip()

            # Extract country from university line
            country_match = re.search(r'University[^,]*,([^@]+?)(?:[a-zA-Z0-9._%+-]+@|Set|$)', details_text)
            if country_match:
                data['country'] = country_match.group(1).strip()

    return data

def extract_download_links(actions_data):
    """Extract download links from actions field"""
    download_links = []
    if 'links' in actions_data:
        for link in actions_data['links']:
            if 'uploads/abstract/' in link and link.startswith('http'):
                download_links.append(link)
    return download_links

def parse_contact_labels(contact_label_text):
    """Parse contact labels into a list"""
    # Common labels that might be concatenated
    labels = []
    label_patterns = [
        'Positive', 'Full Waiver', 'Asking Support', 'Unable to Attend',
        'Confirmed OCM', 'Conversation with OCM', 'Abstract Submitted',
        'Registered', 'Abstract Tried', 'Registration Tried',
        'Onsite Registration', 'Media Partner/Collaborator',
        'Sponsors and Exhibitor', 'Past Attendees', 'Old Conversation',
        'Refund Query', 'Typical Query', 'Brochure Downloaded',
        'Contact Form Query', 'Subscribed', 'Website Form',
        'Updated Mail Address', 'Not Related', 'Junk'
    ]
    
    for pattern in label_patterns:
        if pattern in contact_label_text:
            labels.append(pattern)
    
    return labels

def clean_extracted_data(json_file):
    """Clean and structure the extracted data"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    cleaned_records = []
    
    for record in data['data']:
        clean_record = {}
        
        # Basic fields
        clean_record['serial_no'] = record.get('S.No', {}).get('text', '')
        
        # Parse details field
        details_text = record.get('Details', {}).get('text', '')
        parsed_details = parse_details_field(details_text)
        clean_record.update(parsed_details)
        
        # Parse contact labels
        contact_label_text = record.get('Contact Label', {}).get('text', '')
        clean_record['contact_labels'] = parse_contact_labels(contact_label_text)
        
        # Notes
        clean_record['notes'] = record.get('Notes', {}).get('text', '').replace('Add Notes', '').strip()
        
        # Extract download links
        actions_data = record.get('Actions', {})
        clean_record['download_links'] = extract_download_links(actions_data)
        
        # Mailbox links
        mailbox_data = record.get('Mailbox', {})
        clean_record['mailbox_links'] = mailbox_data.get('links', [])
        
        cleaned_records.append(clean_record)
    
    return cleaned_records

def save_cleaned_data(cleaned_records, base_filename):
    """Save cleaned data to CSV and JSON"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save to JSON
    json_filename = f"{base_filename}_cleaned_{timestamp}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(cleaned_records, f, indent=2, ensure_ascii=False)
    
    # Save to CSV
    csv_filename = f"{base_filename}_cleaned_{timestamp}.csv"
    if cleaned_records:
        # Get all possible fieldnames
        fieldnames = set()
        for record in cleaned_records:
            fieldnames.update(record.keys())
        
        # Remove complex fields for CSV
        csv_fieldnames = [f for f in fieldnames if f not in ['contact_labels', 'download_links', 'mailbox_links']]
        csv_fieldnames.extend(['contact_labels_text', 'download_links_text'])
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=csv_fieldnames)
            writer.writeheader()
            
            for record in cleaned_records:
                csv_record = {k: v for k, v in record.items() if k in csv_fieldnames}
                csv_record['contact_labels_text'] = '; '.join(record.get('contact_labels', []))
                csv_record['download_links_text'] = '; '.join(record.get('download_links', []))
                writer.writerow(csv_record)
    
    print(f"Cleaned data saved to:")
    print(f"  - {json_filename}")
    print(f"  - {csv_filename}")
    
    return json_filename, csv_filename

def main():
    print("=== CLEAN DATA EXTRACTOR ===")
    
    # Look for the most recent JSON file
    import glob
    json_files = glob.glob("abstracts_table_*.json")
    
    if not json_files:
        print("No JSON files found. Please run the main scraper first.")
        return
    
    # Use the most recent file
    latest_file = max(json_files, key=lambda x: x.split('_')[-1])
    print(f"Processing: {latest_file}")
    
    # Clean the data
    cleaned_records = clean_extracted_data(latest_file)
    
    print(f"\nExtracted {len(cleaned_records)} records")
    
    # Show sample of cleaned data
    if cleaned_records:
        print("\n=== SAMPLE RECORD ===")
        sample = cleaned_records[0]
        for key, value in sample.items():
            if isinstance(value, list):
                print(f"{key}: {len(value)} items - {value[:3]}{'...' if len(value) > 3 else ''}")
            else:
                print(f"{key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
    
    # Save cleaned data
    base_name = latest_file.replace('.json', '').replace('abstracts_table_1_', 'abstracts')
    json_file, csv_file = save_cleaned_data(cleaned_records, base_name)
    
    print(f"\n=== SUMMARY ===")
    print(f"Total records: {len(cleaned_records)}")
    
    # Count records with different data
    with_email = sum(1 for r in cleaned_records if r.get('email'))
    with_title = sum(1 for r in cleaned_records if r.get('abstract_title'))
    with_downloads = sum(1 for r in cleaned_records if r.get('download_links'))
    
    print(f"Records with email: {with_email}")
    print(f"Records with abstract title: {with_title}")
    print(f"Records with download links: {with_downloads}")

if __name__ == "__main__":
    main()
