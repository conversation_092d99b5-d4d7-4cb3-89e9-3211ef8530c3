"""
Complete Magnus Group CMS Data Extraction Tool

This script provides multiple methods to extract data from the Magnus Group admin panel:
1. Automated browser-based extraction (requires ChromeDriver)
2. Manual HTML file processing
3. Data cleaning and structuring
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import time
import re
from datetime import datetime
from urllib.parse import urljoin
import os
import glob

class MagnusDataExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })
        self.base_url = "https://admin.magnusgroup.biz"
        self.login_url = "https://admin.magnusgroup.biz/admin-login.php"
        self.abstracts_url = "https://admin.magnusgroup.biz/view-all-abstracts.php"
    
    def try_requests_login(self, username, password):
        """Attempt login using requests (may not work due to session handling)"""
        print("Attempting login with requests...")
        
        # Get login page
        login_page = self.session.get(self.login_url)
        soup = BeautifulSoup(login_page.content, 'html.parser')
        
        # Prepare login data
        login_data = {
            'name': username,
            'password': password
        }
        
        # Add any hidden fields
        hidden_inputs = soup.find_all('input', type='hidden')
        for hidden_input in hidden_inputs:
            name = hidden_input.get('name')
            value = hidden_input.get('value', '')
            if name:
                login_data[name] = value
        
        # Submit login
        response = self.session.post(self.login_url, data=login_data, allow_redirects=True)
        
        # Test session by accessing abstracts page
        test_response = self.session.get(self.abstracts_url)
        
        if "admin-login.php" not in test_response.text and len(test_response.text) > 1000:
            print("Login successful with requests!")
            return test_response
        else:
            print("Login failed with requests method")
            return None
    
    def extract_from_html_content(self, html_content, source_name="HTML"):
        """Extract data from HTML content"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Save HTML for reference
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_filename = f"abstracts_page_{timestamp}.html"
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(soup.prettify())
        
        print(f"HTML content saved to: {html_filename}")
        
        # Analyze and extract data
        tables = soup.find_all('table')
        if not tables:
            print("No tables found in the HTML content")
            return None
        
        print(f"Found {len(tables)} table(s)")
        
        all_data = []
        for i, table in enumerate(tables):
            table_data = self.extract_table_data(table, i)
            if table_data:
                all_data.append(table_data)
        
        return all_data
    
    def extract_table_data(self, table, table_index=0):
        """Extract data from a table element"""
        rows = table.find_all('tr')
        if not rows:
            return None
        
        # Extract headers
        header_row = rows[0]
        headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
        
        # Extract data rows
        data = []
        for row in rows[1:]:
            cells = row.find_all(['td', 'th'])
            row_data = {}
            for i, cell in enumerate(cells):
                if i < len(headers):
                    text = cell.get_text(strip=True)
                    links = [a.get('href') for a in cell.find_all('a', href=True)]
                    
                    row_data[headers[i]] = {
                        'text': text,
                        'links': links
                    }
            data.append(row_data)
        
        return {
            'headers': headers,
            'data': data,
            'table_index': table_index
        }
    
    def clean_and_structure_data(self, raw_data):
        """Clean and structure the extracted data"""
        if not raw_data:
            return []
        
        cleaned_records = []
        
        for table_data in raw_data:
            for record in table_data['data']:
                clean_record = self.parse_record(record)
                if clean_record:
                    cleaned_records.append(clean_record)
        
        return cleaned_records
    
    def parse_record(self, record):
        """Parse a single record from the raw data"""
        clean_record = {}
        
        # Basic fields
        clean_record['serial_no'] = record.get('S.No', {}).get('text', '')
        
        # Parse details field
        details_text = record.get('Details', {}).get('text', '')
        if details_text:
            parsed_details = self.parse_details_field(details_text)
            clean_record.update(parsed_details)
        
        # Parse contact labels
        contact_label_text = record.get('Contact Label', {}).get('text', '')
        clean_record['contact_labels'] = self.parse_contact_labels(contact_label_text)
        
        # Notes
        notes_text = record.get('Notes', {}).get('text', '')
        clean_record['notes'] = notes_text.replace('Add Notes', '').strip()
        
        # Extract download links
        actions_data = record.get('Actions', {})
        clean_record['download_links'] = self.extract_download_links(actions_data)
        
        # Mailbox links
        mailbox_data = record.get('Mailbox', {})
        clean_record['mailbox_links'] = mailbox_data.get('links', [])
        
        return clean_record
    
    def parse_details_field(self, details_text):
        """Parse the Details field to extract structured information"""
        data = {}
        
        # Extract email
        email_matches = re.findall(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', details_text)
        if email_matches:
            # Clean up email extraction
            for email in email_matches:
                if not email.endswith('Set') and len(email) > 5:
                    data['email'] = email
                    break
        
        # Extract abstract title
        title_match = re.search(r'Abstract Title:([^S]+?)Submitted On:', details_text)
        if title_match:
            data['abstract_title'] = title_match.group(1).strip()
        
        # Extract submission date
        date_match = re.search(r'Submitted On:([^$]+)', details_text)
        if date_match:
            data['submission_date'] = date_match.group(1).strip()
        
        # Extract name and university
        name_uni_match = re.search(r'([A-Z][a-z]+ [A-Z][a-z]+),([^,]+University[^,]*)', details_text)
        if name_uni_match:
            data['name'] = name_uni_match.group(1).strip()
            data['university'] = name_uni_match.group(2).strip()
            
            # Extract country
            country_match = re.search(r'University[^,]*,([^@]+?)(?:[a-zA-Z0-9._%+-]+@|Set|$)', details_text)
            if country_match:
                data['country'] = country_match.group(1).strip()
        
        return data

    def parse_contact_labels(self, contact_label_text):
        """Parse contact labels into a list"""
        labels = []
        label_patterns = [
            'Positive', 'Full Waiver', 'Asking Support', 'Unable to Attend',
            'Confirmed OCM', 'Conversation with OCM', 'Abstract Submitted',
            'Registered', 'Abstract Tried', 'Registration Tried',
            'Onsite Registration', 'Media Partner/Collaborator',
            'Sponsors and Exhibitor', 'Past Attendees', 'Old Conversation',
            'Refund Query', 'Typical Query', 'Brochure Downloaded',
            'Contact Form Query', 'Subscribed', 'Website Form',
            'Updated Mail Address', 'Not Related', 'Junk'
        ]

        for pattern in label_patterns:
            if pattern in contact_label_text:
                labels.append(pattern)

        return labels

    def extract_download_links(self, actions_data):
        """Extract download links from actions field"""
        download_links = []
        if 'links' in actions_data:
            for link in actions_data['links']:
                if 'uploads/abstract/' in link and link.startswith('http'):
                    download_links.append(link)
        return download_links

    def save_data(self, cleaned_records, base_filename="magnus_abstracts"):
        """Save cleaned data to CSV and JSON"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save to JSON
        json_filename = f"{base_filename}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(cleaned_records, f, indent=2, ensure_ascii=False)

        # Save to CSV
        csv_filename = f"{base_filename}_{timestamp}.csv"
        if cleaned_records:
            fieldnames = ['serial_no', 'email', 'name', 'abstract_title', 'submission_date',
                         'university', 'country', 'notes', 'contact_labels_text', 'download_links_text']

            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for record in cleaned_records:
                    csv_record = {k: v for k, v in record.items() if k in fieldnames}
                    csv_record['contact_labels_text'] = '; '.join(record.get('contact_labels', []))
                    csv_record['download_links_text'] = '; '.join(record.get('download_links', []))
                    writer.writerow(csv_record)

        print(f"\nData saved to:")
        print(f"  - {json_filename}")
        print(f"  - {csv_filename}")

        return json_filename, csv_filename

    def run_extraction(self, username, password, method="auto"):
        """Run the complete extraction process"""
        print("=== MAGNUS GROUP DATA EXTRACTOR ===")
        print(f"Method: {method}")

        html_content = None

        if method == "auto" or method == "requests":
            # Try automated extraction
            response = self.try_requests_login(username, password)
            if response:
                html_content = response.text
                print("Successfully retrieved data using automated method")
            else:
                print("Automated method failed, falling back to manual method")
                method = "manual"

        if method == "manual":
            print("\n=== MANUAL EXTRACTION REQUIRED ===")
            print("Please follow these steps:")
            print("1. Open your browser and go to: https://admin.magnusgroup.biz/admin-login.php")
            print("2. Login with your credentials")
            print("3. Navigate to: https://admin.magnusgroup.biz/view-all-abstracts.php")
            print("4. Save the page as HTML (Ctrl+S) and name it 'abstracts_manual.html'")
            print("5. Place the file in this directory and press Enter")

            input("Press Enter when you have saved the HTML file...")

            # Look for manually saved HTML file
            manual_files = ['abstracts_manual.html', 'abstracts_data.html']
            for filename in manual_files:
                if os.path.exists(filename):
                    with open(filename, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    print(f"Found manual HTML file: {filename}")
                    break

            if not html_content:
                print("No manual HTML file found. Please save the page and try again.")
                return None

        if html_content:
            # Extract data from HTML
            raw_data = self.extract_from_html_content(html_content)

            if raw_data:
                # Clean and structure data
                cleaned_records = self.clean_and_structure_data(raw_data)

                if cleaned_records:
                    # Save data
                    json_file, csv_file = self.save_data(cleaned_records)

                    # Print summary
                    print(f"\n=== EXTRACTION SUMMARY ===")
                    print(f"Total records extracted: {len(cleaned_records)}")

                    with_email = sum(1 for r in cleaned_records if r.get('email'))
                    with_title = sum(1 for r in cleaned_records if r.get('abstract_title'))
                    with_downloads = sum(1 for r in cleaned_records if r.get('download_links'))

                    print(f"Records with email: {with_email}")
                    print(f"Records with abstract title: {with_title}")
                    print(f"Records with download links: {with_downloads}")

                    return cleaned_records
                else:
                    print("No data could be extracted from the HTML")
                    return None
            else:
                print("Failed to extract data from HTML content")
                return None
        else:
            print("No HTML content available for processing")
            return None

def main():
    extractor = MagnusDataExtractor()

    # Credentials
    username = "<EMAIL>"
    password = "Magnus@38"

    # Try extraction
    results = extractor.run_extraction(username, password, method="auto")

    if results:
        print(f"\nExtraction completed successfully!")
        print(f"You can now use the generated CSV and JSON files for further analysis.")
    else:
        print("\nExtraction failed. Please try the manual method.")

if __name__ == "__main__":
    main()
