"""
Complete Magnus Group Pagination Scraper
Handles dropdown selection, pagination, and detailed data extraction
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
import time
import json
import csv
import re
from datetime import datetime

class CompleteMagnusScraper:
    def __init__(self, headless=False):
        self.chrome_options = Options()
        if headless:
            self.chrome_options.add_argument("--headless")
        self.chrome_options.add_argument("--no-sandbox")
        self.chrome_options.add_argument("--disable-dev-shm-usage")
        self.chrome_options.add_argument("--disable-gpu")
        self.chrome_options.add_argument("--window-size=1920,1080")
        
        self.driver = None
        self.login_url = "https://admin.magnusgroup.biz/admin-login.php"
        self.abstracts_url = "https://admin.magnusgroup.biz/view-all-abstracts.php"
        self.all_records = []
        
    def start_driver(self):
        """Initialize the Chrome driver"""
        try:
            print("Setting up ChromeDriver...")
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.chrome_options)
            print("Chrome driver started successfully")
            return True
        except Exception as e:
            print(f"Failed to start Chrome driver: {e}")
            print("Error details:", str(e))
            return False
    
    def login(self, username, password):
        """Login to the admin panel"""
        if not self.driver:
            return False
            
        print("Navigating to login page...")
        self.driver.get(self.login_url)
        
        try:
            # Wait for login form
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "name"))
            )
            
            # Fill login form
            username_field = self.driver.find_element(By.NAME, "name")
            password_field = self.driver.find_element(By.NAME, "password")
            
            username_field.clear()
            username_field.send_keys(username)
            
            password_field.clear()
            password_field.send_keys(password)
            
            # Submit form
            submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
            submit_button.click()
            
            # Wait for redirect
            time.sleep(3)
            
            current_url = self.driver.current_url
            print(f"Current URL after login: {current_url}")
            
            if "admin-login.php" not in current_url:
                print("Login successful!")
                return True
            else:
                print("Login may have failed")
                return False
                
        except Exception as e:
            print(f"Error during login: {e}")
            return False
    
    def navigate_to_abstracts_and_set_dropdown(self):
        """Navigate to abstracts page and set dropdown to 'All Conferences'"""
        print("Navigating to abstracts page...")
        self.driver.get(self.abstracts_url)
        
        try:
            # Wait for page to load
            time.sleep(3)
            
            # Look for dropdown - it might be a select element or custom dropdown
            print("Looking for conference dropdown...")
            
            # Try to find select dropdown first
            try:
                select_element = self.driver.find_element(By.CSS_SELECTOR, "select")
                select = Select(select_element)
                
                # Look for "All Conferences" option
                options = select.options
                print(f"Found dropdown with {len(options)} options:")
                for option in options:
                    print(f"  - {option.text}")
                
                # Try to select "All Conferences"
                for option in options:
                    if "all" in option.text.lower() and "conference" in option.text.lower():
                        select.select_by_visible_text(option.text)
                        print(f"Selected: {option.text}")
                        time.sleep(2)
                        return True
                        
            except Exception as e:
                print(f"No select dropdown found: {e}")
            
            # Try to find custom dropdown or button
            try:
                dropdown_buttons = self.driver.find_elements(By.CSS_SELECTOR, "button, .dropdown-toggle, [data-toggle='dropdown']")
                for button in dropdown_buttons:
                    if "conference" in button.text.lower():
                        print(f"Found dropdown button: {button.text}")
                        button.click()
                        time.sleep(1)
                        
                        # Look for "All Conferences" option
                        options = self.driver.find_elements(By.CSS_SELECTOR, ".dropdown-menu a, .dropdown-menu li")
                        for option in options:
                            if "all" in option.text.lower() and "conference" in option.text.lower():
                                option.click()
                                print(f"Selected: {option.text}")
                                time.sleep(2)
                                return True
                                
            except Exception as e:
                print(f"No custom dropdown found: {e}")
            
            print("Could not find conference dropdown - proceeding with current selection")
            return True
            
        except Exception as e:
            print(f"Error setting dropdown: {e}")
            return False
    
    def extract_page_data(self, page_num):
        """Extract data from current page"""
        print(f"Extracting data from page {page_num}...")

        # Wait for table to load
        time.sleep(2)

        page_source = self.driver.page_source

        # Save page source for debugging
        debug_filename = f"debug_page_{page_num}.html"
        with open(debug_filename, 'w', encoding='utf-8') as f:
            f.write(page_source)
        print(f"Page source saved to {debug_filename} for debugging")

        soup = BeautifulSoup(page_source, 'html.parser')

        # Find the main table
        tables = soup.find_all('table')
        if not tables:
            print("No tables found on page")
            return []

        print(f"Found {len(tables)} table(s) on page")

        # Try each table to find the data table
        for table_idx, table in enumerate(tables):
            print(f"Checking table {table_idx + 1}...")
            rows = table.find_all('tr')

            if len(rows) <= 1:  # Only header row or empty
                print(f"Table {table_idx + 1}: No data rows found")
                continue

            print(f"Table {table_idx + 1}: Found {len(rows)} rows (including header)")

            # Check if this looks like the main data table
            first_data_row = rows[1] if len(rows) > 1 else None
            if first_data_row:
                cells = first_data_row.find_all(['td', 'th'])
                print(f"First data row has {len(cells)} cells")

                # Print first few cell contents for debugging
                for i, cell in enumerate(cells[:5]):
                    cell_text = cell.get_text(strip=True)[:100]  # First 100 chars
                    print(f"  Cell {i+1}: {cell_text}")

            page_records = []

            # Extract data from each row (skip header)
            for i, row in enumerate(rows[1:], 1):
                try:
                    record = self.extract_record_from_row(row, page_num, i)
                    if record:
                        page_records.append(record)
                except Exception as e:
                    print(f"Error extracting record {i} on page {page_num}: {e}")

            if page_records:
                print(f"Successfully extracted {len(page_records)} records from table {table_idx + 1}")
                return page_records

        print("No valid data found in any table")
        return []
    
    def extract_record_from_row(self, row, page_num, row_num):
        """Extract a single record from a table row"""
        cells = row.find_all(['td', 'th'])

        if len(cells) < 2:  # Need at least 2 columns
            return None

        record = {
            'page_number': page_num,
            'row_number': row_num
        }

        # Print cell contents for debugging
        print(f"Row {row_num} has {len(cells)} cells:")
        for i, cell in enumerate(cells):
            cell_text = cell.get_text(strip=True)
            print(f"  Cell {i+1}: {cell_text[:200]}...")  # First 200 chars

        # Extract S.No (usually first column)
        if len(cells) > 0:
            record['s_no'] = cells[0].get_text(strip=True)

        # Extract Conference (column 2 based on debug output)
        if len(cells) > 1:
            conference_text = cells[1].get_text(strip=True)
            record['conference'] = conference_text
            print(f"Conference: {conference_text}")

        # Extract Details (column 3 based on debug output)
        if len(cells) > 2:
            details_text = cells[2].get_text(strip=True)
            print(f"Using cell 3 as details column")
            parsed_details = self.parse_details_field(details_text)
            record.update(parsed_details)
        else:
            # Fallback: try to extract from all remaining cells
            all_text = " ".join([cell.get_text(strip=True) for cell in cells[1:]])
            parsed_details = self.parse_details_field(all_text)
            record.update(parsed_details)

        # Look for View/Edit links to get Abstract Type
        view_edit_links = row.find_all('a', href=True)
        record['view_edit_available'] = False
        for link in view_edit_links:
            link_text = link.text.lower()
            if 'view' in link_text or 'edit' in link_text:
                record['view_edit_available'] = True
                break

        # Extract Abstract Type from column 5 (Notes/Status column) - it's often visible there!
        record['abstract_type'] = ""
        if len(cells) > 4:  # Column 5 (index 4)
            notes_cell = cells[4]
            notes_text = notes_cell.get_text(strip=True)

            # Look for presentation types in the notes column
            presentation_patterns = [
                r'(Oral Presentation \([^)]+\))',
                r'(Poster Presentation \([^)]+\))',
                r'(Keynote \([^)]+\))',
                r'(Workshop \([^)]+\))',
                r'(Panel Discussion \([^)]+\))'
            ]

            for pattern in presentation_patterns:
                match = re.search(pattern, notes_text, re.IGNORECASE)
                if match:
                    record['abstract_type'] = match.group(1)
                    print(f"Found Abstract Type in notes column: {record['abstract_type']}")
                    break

        # Skip modal extraction completely for now (performance optimization)
        if not record['abstract_type']:
            print(f"Abstract Type not found in notes for record {record.get('s_no')} - skipping modal extraction for speed")
            record['abstract_type'] = ""  # Set empty string instead of calling modal extraction

        return record

    def parse_details_field(self, details_text):
        """Parse the Details field to extract name, email, title, submission date"""
        parsed = {}

        # Clean up the text first - remove common artifacts
        clean_text = details_text.replace('Set Reminder', '').replace('Add Notes', '')

        # Extract email addresses first to help with parsing
        # First, let's handle the country+email concatenation issue
        # Common countries that get concatenated with emails
        countries = ['India', 'China', 'USA', 'Canada', 'Australia', 'UK', 'Germany', 'France', 'Italy', 'Spain',
                    'Japan', 'Brazil', 'Mexico', 'Turkey', 'Egypt', 'Nigeria', 'Kenya', 'Qatar', 'Emirates',
                    'Kingdom', 'States', 'Sudan', 'Yemen', 'Mali', 'Algeria', 'Singapore', 'Turkmenistan']

        # Fix concatenated country+email patterns - be more aggressive
        for country in countries:
            # Replace patterns like "<EMAIL>" with "<EMAIL>"
            pattern = country + r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
            clean_text = re.sub(pattern, r'\1', clean_text, flags=re.IGNORECASE)

            # Also handle patterns like "Italy <EMAIL>"
            pattern2 = country + r'\s+([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
            clean_text = re.sub(pattern2, r'\1', clean_text, flags=re.IGNORECASE)

        # Extract all email addresses
        email_matches = re.findall(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', clean_text)
        emails_found = []

        if email_matches:
            for email in email_matches:
                # Clean email - remove any trailing artifacts
                clean_email = re.sub(r'(Set|Add|Note|Abstract|Reminder|Notes|com[a-zA-Z]+).*$', '', email).strip()
                # Remove any leading artifacts
                clean_email = re.sub(r'^[^a-zA-Z0-9]*', '', clean_email)

                if clean_email and '@' in clean_email and len(clean_email) > 5 and '.' in clean_email.split('@')[1]:
                    emails_found.append(clean_email)

            # Remove duplicates while preserving order
            unique_emails = []
            for email in emails_found:
                if email not in unique_emails:
                    unique_emails.append(email)

            if unique_emails:
                parsed['email_primary'] = unique_emails[0]
                if len(unique_emails) > 1:
                    parsed['email_secondary'] = unique_emails[1]

        # Extract name - improved patterns to handle country prefixes
        # Remove emails from text first to avoid confusion
        text_without_emails = clean_text
        for email in (emails_found if 'emails_found' in locals() else []):
            text_without_emails = text_without_emails.replace(email, '')

        # Remove country names from the beginning
        for country in countries:
            text_without_emails = re.sub(f'^{country}\\s*', '', text_without_emails, flags=re.IGNORECASE)

        # Try to extract name with improved patterns
        name_patterns = [
            r'^([A-Z][a-zA-Z\s\.]+?),',  # Name followed by comma (most common)
            r'^([A-Z][a-zA-Z\s\.]+?)(?=\s*[a-z@])',  # Name before lowercase or email
            r'^([A-Z][a-zA-Z\s\.]+?)(?=\s*Set|Add|Abstract)',  # Name before common keywords
            r'^([A-Z][a-zA-Z\s\.]+?)(?=\s*[A-Z][a-z]+\s*[a-z@])',  # Name before institution
        ]

        for pattern in name_patterns:
            name_match = re.search(pattern, text_without_emails)
            if name_match and '@' not in name_match.group(1):
                name = name_match.group(1).strip()
                # Remove common suffixes that got included
                name = re.sub(r'\s*(Set|Add|Reminder|Notes|Professor|Dr|University|Institute|Department).*$', '', name, flags=re.IGNORECASE)
                name = re.sub(r',$', '', name)  # Remove trailing comma

                # Filter out country names and ensure reasonable length
                if (len(name) > 2 and len(name) < 50 and
                    not any(word in name.lower() for word in ['abstract', 'title', 'submitted', 'reminder', 'university', 'institute']) and
                    name.lower() not in [c.lower() for c in countries]):
                    parsed['name'] = name.strip()
                    break

        # Extract abstract title (improved)
        title_patterns = [
            r'Abstract Title:\s*([^S]+?)(?:Submitted On:|$)',
            r'Abstract Title:\s*([^A]+?)(?:Add Notes|$)',
            r'Abstract Title:\s*(.+?)(?=\s*Submitted On:|\s*$)'
        ]

        for pattern in title_patterns:
            title_match = re.search(pattern, clean_text, re.DOTALL)
            if title_match:
                title = title_match.group(1).strip()
                # Clean up title
                title = re.sub(r'\s*(Set|Add|Reminder|Notes).*$', '', title)
                if len(title) > 5:  # Only if meaningful length
                    parsed['abstract_title'] = title
                    break

        # Extract submission date (improved)
        date_patterns = [
            r'Submitted On:\s*([^A-Z]+?)(?:Add|Set|$)',
            r'Submitted On:\s*(.+?)(?=\s*[A-Z]|\s*$)'
        ]

        for pattern in date_patterns:
            date_match = re.search(pattern, clean_text)
            if date_match:
                date = date_match.group(1).strip()
                # Clean up date
                date = re.sub(r'\s*(Set|Add|Reminder|Notes).*$', '', date)
                if len(date) > 5:  # Only if meaningful length
                    parsed['submitted_on'] = date
                    break

        return parsed

    def extract_conference_info(self, row):
        """Try to extract conference information from the row"""
        # Conference info is now extracted directly from column 2
        # This method is kept for compatibility but not used
        return ""

    def extract_abstract_type_from_link(self, link):
        """Try to extract abstract type from view/edit link or surrounding context"""
        # Look for abstract type in the link's parent or nearby elements
        parent = link.parent
        if parent:
            parent_text = parent.get_text(strip=True)
            # Look for common abstract types
            abstract_types = [
                'oral presentation', 'poster presentation', 'virtual presentation',
                'in-person', 'online', 'hybrid', 'keynote', 'workshop'
            ]
            for abs_type in abstract_types:
                if abs_type in parent_text.lower():
                    return parent_text
        return ""

    def go_to_next_page(self, current_page):
        """Navigate to the next page using AJAX pagination"""
        try:
            next_page = current_page + 1
            print(f"Attempting to navigate to page {next_page} using AJAX...")

            # The site uses AJAX calls like: $.post('get_all_abstracts_new.php?current_search_cid=0', {'page' : 10,...})
            # Method 1: Try to find and click pagination links with page numbers
            pagination_selectors = [
                f"//a[contains(@onclick, 'page') and contains(text(), '{next_page}')]",
                f"//a[text()='{next_page}']",
                "//a[contains(text(), 'Next')]",
                "//a[contains(@onclick, 'page')]"
            ]

            for selector in pagination_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        element = elements[0]
                        onclick_attr = element.get_attribute('onclick') or ''
                        print(f"Found pagination element: {element.text} - {onclick_attr[:100]}")

                        # Scroll to element and click
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                        time.sleep(1)

                        # Try multiple click methods
                        click_methods = [
                            lambda: element.click(),
                            lambda: self.driver.execute_script("arguments[0].click();", element),
                            lambda: self.driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('click', {bubbles: true}));", element)
                        ]

                        for i, click_method in enumerate(click_methods):
                            try:
                                click_method()
                                print(f"Successfully clicked pagination using method {i+1}")
                                time.sleep(4)  # Wait for AJAX to complete

                                # Verify that we actually moved to the next page
                                if self.verify_page_change(next_page):
                                    return True
                                else:
                                    print(f"Page change verification failed for method {i+1}")
                                    continue

                            except Exception as e:
                                print(f"Click method {i+1} failed: {str(e)[:50]}")
                                continue

                except Exception as e:
                    continue

            print(f"Could not navigate to page {next_page}")
            return False

        except Exception as e:
            print(f"Error navigating to next page: {e}")
            return False

    def has_more_pages(self, current_page):
        """Check if there are more pages to process"""
        try:
            # Method 1: Look for pagination info like "Showing 1 to 10 of 3572"
            pagination_selectors = [
                "//div[contains(text(), 'Showing')]",
                "//*[contains(text(), 'Showing')]",
                "//span[contains(text(), 'Showing')]",
                "//*[contains(text(), 'of')]"
            ]

            for selector in pagination_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        pagination_text = elements[0].text
                        print(f"Pagination info: {pagination_text}")

                        # Extract current page info
                        import re
                        match = re.search(r'Showing (\d+) to (\d+) of (\d+)', pagination_text)
                        if match:
                            start, end, total = map(int, match.groups())
                            print(f"Current: {start}-{end}, Total: {total}")
                            return end < total

                except Exception as e:
                    continue

            # Method 2: Look for pagination links with higher page numbers
            try:
                next_page = current_page + 1
                next_page_selectors = [
                    f"//a[contains(@onclick, 'page') and contains(text(), '{next_page}')]",
                    f"//a[text()='{next_page}']",
                    "//a[contains(text(), 'Next')]"
                ]

                for selector in next_page_selectors:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        # Check if the element is not disabled
                        element = elements[0]
                        classes = element.get_attribute('class') or ''
                        if 'disabled' not in classes.lower():
                            print(f"Found next page link: {element.text}")
                            return True

            except Exception as e:
                pass

            # Method 3: Conservative approach - assume we have more pages unless we're sure we don't
            # Since we know there are 357 pages total, we can use this as a fallback
            if current_page < 357:
                print(f"Current page {current_page} < 357, assuming more pages exist")
                return True

            print(f"Reached page {current_page}, no more pages detected")
            return False

        except Exception as e:
            print(f"Error checking for more pages: {e}")
            # Conservative fallback
            return current_page < 357

    def verify_page_change(self, expected_page):
        """Verify that we've successfully moved to the expected page"""
        try:
            # Wait a moment for page to load
            time.sleep(2)

            # Look for pagination info to confirm current page
            pagination_selectors = [
                "//div[contains(text(), 'Showing')]",
                "//*[contains(text(), 'Showing')]"
            ]

            for selector in pagination_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        pagination_text = elements[0].text
                        print(f"Page verification - Pagination info: {pagination_text}")

                        # Extract current page info
                        import re
                        match = re.search(r'Showing (\d+) to (\d+) of (\d+)', pagination_text)
                        if match:
                            start, end, total = map(int, match.groups())
                            # Calculate expected start for the page
                            expected_start = (expected_page - 1) * 10 + 1
                            expected_end = min(expected_page * 10, total)

                            if start == expected_start and end == expected_end:
                                print(f"✅ Successfully moved to page {expected_page}")
                                return True
                            else:
                                print(f"❌ Page mismatch - Expected: {expected_start}-{expected_end}, Got: {start}-{end}")
                                return False

                except Exception as e:
                    continue

            # Fallback: Check if we have different data than before
            try:
                # Get first record's S.No to verify page change
                first_row = self.driver.find_element(By.XPATH, "//table//tr[2]/td[1]")
                s_no = first_row.text.strip()
                expected_first_s_no = str((expected_page - 1) * 10 + 1)

                if s_no == expected_first_s_no:
                    print(f"✅ Page verified by first record S.No: {s_no}")
                    return True
                else:
                    print(f"❌ S.No mismatch - Expected: {expected_first_s_no}, Got: {s_no}")
                    return False

            except Exception as e:
                print(f"Could not verify page change: {e}")
                return False

        except Exception as e:
            print(f"Error verifying page change: {e}")
            return False

    def extract_abstract_type_from_details(self, record):
        """DISABLED: Extract abstract type by clicking view/edit if available"""
        print(f"Modal extraction is DISABLED for performance - skipping record {record.get('s_no', 'unknown')}")
        # Keep existing abstract_type if found in notes column, otherwise set empty
        if not record.get('abstract_type'):
            record['abstract_type'] = ""
        return record



    def scrape_all_pages(self):
        """Main method to scrape all pages"""
        print("Starting complete pagination scrape...")

        page_num = 1
        total_records = 0

        while True:
            print(f"\n=== Processing Page {page_num} ===")

            # Extract data from current page
            page_records = self.extract_page_data(page_num)

            if not page_records:
                print("No records found on this page - stopping")
                break

            # Skip modal extraction completely for now (focus on core data quality)
            for record in page_records:
                if record.get('abstract_type'):
                    print(f"Abstract Type already found for record {record.get('s_no')}: {record.get('abstract_type')}")
                else:
                    print(f"Abstract Type not found in notes for record {record.get('s_no')} - skipping modal extraction for speed")

            self.all_records.extend(page_records)
            total_records += len(page_records)

            print(f"Total records so far: {total_records}")

            # Check if there are more pages
            if not self.has_more_pages(page_num):
                print("No more pages found - scraping complete")
                break

            # Go to next page
            if not self.go_to_next_page(page_num):
                print("Could not navigate to next page - stopping")
                break

            page_num += 1

            # Progress update every 10 pages
            if page_num % 10 == 0:
                print(f"\n🔄 PROGRESS UPDATE: Completed {page_num-1} pages, {total_records} records extracted")

            # Safety limit to prevent infinite loops (we know there are 357 pages)
            if page_num > 357:
                print("Reached maximum expected pages (357) - stopping")
                break

        print(f"\n=== Scraping Complete ===")
        print(f"Total pages processed: {page_num}")
        print(f"Total records extracted: {total_records}")

        return self.all_records

    def save_data(self, records, filename_base="magnus_all_conferences"):
        """Save the extracted data"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save to CSV
        csv_filename = f"{filename_base}_{timestamp}.csv"

        if records:
            # Define the fieldnames in the order you requested (core fields only for now)
            fieldnames = [
                's_no', 'conference', 'name', 'email_primary', 'email_secondary',
                'abstract_title', 'submitted_on', 'abstract_type',
                # Metadata fields
                'page_number', 'row_number', 'view_edit_available'
            ]

            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for record in records:
                    # Create a clean record with only the fields we want
                    clean_record = {}
                    for field in fieldnames:
                        clean_record[field] = record.get(field, '')
                    writer.writerow(clean_record)

        # Save to JSON
        json_filename = f"{filename_base}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(records, f, indent=2, ensure_ascii=False)

        print(f"\nData saved to:")
        print(f"  - {csv_filename}")
        print(f"  - {json_filename}")

        return csv_filename, json_filename

    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            print("Browser closed")

    def run_complete_extraction(self, username, password):
        """Run the complete extraction process"""
        print("=== MAGNUS GROUP COMPLETE PAGINATION SCRAPER ===")

        try:
            # Start browser
            if not self.start_driver():
                return None

            # Login
            if not self.login(username, password):
                print("Login failed")
                return None

            # Navigate and set dropdown
            if not self.navigate_to_abstracts_and_set_dropdown():
                print("Failed to set dropdown")
                return None

            # Scrape all pages
            all_records = self.scrape_all_pages()

            if all_records:
                # Save data
                csv_file, json_file = self.save_data(all_records)

                # Print summary
                print(f"\n=== EXTRACTION SUMMARY ===")
                print(f"Total records: {len(all_records)}")

                # Count records with different data
                with_name = sum(1 for r in all_records if r.get('name'))
                with_email = sum(1 for r in all_records if r.get('email_primary'))
                with_title = sum(1 for r in all_records if r.get('abstract_title'))
                with_date = sum(1 for r in all_records if r.get('submitted_on'))

                print(f"Records with name: {with_name}")
                print(f"Records with email: {with_email}")
                print(f"Records with abstract title: {with_title}")
                print(f"Records with submission date: {with_date}")

                return all_records
            else:
                print("No records extracted")
                return None

        except Exception as e:
            print(f"Error during extraction: {e}")
            return None
        finally:
            # Always close browser
            self.close()

def main():
    # Create scraper instance
    scraper = CompleteMagnusScraper(headless=False)  # Set to True for headless mode

    # Credentials
    username = "<EMAIL>"
    password = "Magnus@38"

    # Run extraction
    results = scraper.run_complete_extraction(username, password)

    if results:
        print(f"\n✅ Extraction completed successfully!")
        print(f"Extracted {len(results)} total records from all pages")
        print(f"Data saved to CSV and JSON files")

        # Show sample record
        if results:
            print(f"\n=== SAMPLE RECORD ===")
            sample = results[0]
            for key, value in sample.items():
                print(f"{key}: {value}")
    else:
        print("\n❌ Extraction failed")

if __name__ == "__main__":
    main()
