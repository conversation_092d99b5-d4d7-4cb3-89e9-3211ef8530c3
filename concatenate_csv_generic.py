import os
import glob
import pandas as pd
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

def create_gradient_progress():
    """Create a progress bar with gradient colors"""
    return Progress(
        TextColumn("[bold blue]{task.description}"),
        BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="bright_green"),
        "[progress.percentage]{task.percentage:>3.0f}%",
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        console=Console()
    )

def extract_generic_emails():
    """
    Extract emails with generic domains (gmail, yahoo, etc.) from CSV files
    """
    console = Console()

    # Display header
    header_text = Text("Generic Email Domains Extractor", style="bold magenta")
    console.print(Panel(header_text, expand=False))

    # Define generic email domains
    generic_domains = [
        'gmail.com', 'googlemail.com', 'yahoo.com', 'yahoo.co.uk', 'yahoo.in',
        'yahoo.co.in', 'ymail.com', 'hotmail.com', 'hotmail.co.uk', 'outlook.com',
        'live.com', 'msn.com', 'aol.com', 'rediffmail.com', 'rediff.com',
        'icloud.com', 'me.com', 'mac.com', 'protonmail.com', 'mail.com',
        'zoho.com', 'gmx.com', 'gmx.net', 'yandex.com', 'inbox.com',
        'mail.ru', 'qq.com', '163.com', '126.com', 'sina.com'
    ]

    console.print("\n[cyan]Generic domains being filtered:[/cyan]")
    console.print(", ".join(generic_domains))
    
    # Get directory path from user
    directory_path = input("\nEnter the directory path containing CSV files: ").strip('"\'')

    # Check if directory exists
    if not os.path.exists(directory_path):
        console.print(f"[red]Error: Directory not found: {directory_path}[/red]")
        return

    # Change to the directory
    original_dir = os.getcwd()
    os.chdir(directory_path)

    try:
        # Find all CSV files
        csv_files = glob.glob('*.csv')

        if not csv_files:
            console.print("[red]No CSV files found in the directory.[/red]")
            return

        console.print(f"\n[green]Found {len(csv_files)} CSV files:[/green]")
        for file in csv_files:
            console.print(f"  • {file}")

        # Ask for output filename prefix
        output_prefix = input("\nEnter output filename prefix (default: 'generic_emails'): ").strip()
        if not output_prefix:
            output_prefix = "generic_emails"

        # Initialize list to store dataframes
        all_dfs = []
        total_rows_read = 0

        # Create progress bar
        with create_gradient_progress() as progress:
            task = progress.add_task("Reading CSV files...", total=len(csv_files))

            for file in csv_files:
                try:
                    # Read CSV file
                    df = pd.read_csv(file, encoding='utf-8-sig', on_bad_lines='skip', low_memory=False)

                    # Standardize common column names
                    column_mapping = {
                        'Email ID': 'Email',
                        'email': 'Email',
                        'EMAIL': 'Email',
                        'Author Name': 'Name',
                        'name': 'Name',
                        'NAME': 'Name'
                    }

                    for old_col, new_col in column_mapping.items():
                        if old_col in df.columns and new_col not in df.columns:
                            df.rename(columns={old_col: new_col}, inplace=True)

                    all_dfs.append(df)
                    total_rows_read += len(df)

                    progress.console.print(f"  ✓ Read {file}: {len(df)} rows")

                except Exception as e:
                    progress.console.print(f"  ✗ Error reading {file}: {str(e)}")

                progress.advance(task)

        if not all_dfs:
            console.print("[red]No data could be read from any CSV files.[/red]")
            return

        # Concatenate all dataframes
        console.print("\n[yellow]Concatenating data...[/yellow]")
        combined_df = pd.concat(all_dfs, ignore_index=True)
        console.print(f"[green]Total rows before filtering: {len(combined_df)}[/green]")

        # Check if Email column exists
        if 'Email' not in combined_df.columns:
            console.print("[red]Error: No 'Email' column found in the CSV files.[/red]")
            console.print(f"[yellow]Available columns: {', '.join(combined_df.columns.tolist())}[/yellow]")
            return

        # Filter for generic email domains
        console.print("\n[yellow]Filtering for generic email domains...[/yellow]")

        # Create a filter for generic domains
        generic_filter = combined_df['Email'].str.lower().str.contains(
            '|'.join([f'@{domain}' for domain in generic_domains]),
            na=False,
            regex=True
        )

        generic_emails_df = combined_df[generic_filter].copy()

        console.print(f"[green]Generic emails found: {len(generic_emails_df)}[/green]")

        if len(generic_emails_df) == 0:
            console.print("[yellow]No generic email addresses found. No output file created.[/yellow]")
            return

        # Create output directory if it doesn't exist
        output_dir = "generic_emails"
        os.makedirs(output_dir, exist_ok=True)

        # Create output filename
        output_filename = os.path.join(output_dir, f"{output_prefix}.csv")

        # Save the filtered file
        console.print(f"\n[yellow]Saving generic emails to output folder...[/yellow]")
        generic_emails_df.to_csv(output_filename, index=False, encoding='utf-8-sig')

        # Count emails by domain
        domain_counts = {}
        for domain in generic_domains:
            count = generic_emails_df['Email'].str.lower().str.contains(f'@{domain}', na=False).sum()
            if count > 0:
                domain_counts[domain] = count

        # Sort by count
        sorted_domains = sorted(domain_counts.items(), key=lambda x: x[1], reverse=True)

        domain_breakdown = "\n".join([f"  • {domain}: {count}" for domain, count in sorted_domains[:10]])

        # Display summary
        summary_text = f"""
[bold green]Generic Email Extraction Complete![/bold green]

[bold]Summary:[/bold]
• Files processed: {len(csv_files)}
• Total rows read: {total_rows_read}
• Generic emails found: {len(generic_emails_df)}
• Output file: {output_filename}

[bold]Top domains found:[/bold]
{domain_breakdown}

[bold]Columns in output:[/bold]
{', '.join(generic_emails_df.columns.tolist())}
        """

        console.print(Panel(summary_text, title="Results", expand=False))
        
    except Exception as e:
        console.print(f"[red]An error occurred: {str(e)}[/red]")
    
    finally:
        # Change back to original directory
        os.chdir(original_dir)

if __name__ == "__main__":
    extract_generic_emails()
