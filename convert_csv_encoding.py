import os
import pandas as pd
import glob
import re
from pathlib import Path
from rich.console import Console
from rich.progress import Progress
import unicodedata

try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False

def transliterate(text):
    """
    Transliterate Unicode characters to their closest ASCII equivalents.
    For example: é -> e, ü -> u, ñ -> n, etc.
    
    Args:
        text: Text to transliterate
        
    Returns:
        Transliterated text
    """
    if not isinstance(text, str):
        return text
    
    # Normalize to decomposed form (e.g., é -> e + ´)
    normalized = unicodedata.normalize('NFKD', text)
    
    # Remove non-ASCII characters (like accents)
    result = ''.join([c for c in normalized if not unicodedata.combining(c)])
    
    # Replace common characters that don't decompose well
    replacements = {
        # Latin/Germanic/Nordic characters
        'æ': 'ae', 'Æ': 'AE',
        'œ': 'oe', 'Œ': 'OE',
        'ß': 'ss', 'ẞ': 'SS',
        'ø': 'o', 'Ø': 'O',
        'å': 'a', 'Å': 'A',
        
        # Slavic characters (Polish, Czech, Slovak, etc.)
        'ł': 'l', 'Ł': 'L',
        'ń': 'n', 'Ń': 'N',
        'ś': 's', 'Ś': 'S',
        'ź': 'z', 'Ź': 'Z',
        'ż': 'z', 'Ż': 'Z',
        'č': 'c', 'Č': 'C',
        'ě': 'e', 'Ě': 'E',
        'ř': 'r', 'Ř': 'R',
        'š': 's', 'Š': 'S',
        'ť': 't', 'Ť': 'T',
        'ů': 'u', 'Ů': 'U',
        'ž': 'z', 'Ž': 'Z',
        
        # Turkish characters
        'ı': 'i', 'İ': 'I',
        'ğ': 'g', 'Ğ': 'G',
        'ş': 's', 'Ş': 'S',
        'ç': 'c', 'Ç': 'C',
        
        # German characters (most handled by decomposition)
        'ä': 'ae', 'Ä': 'AE',
        'ö': 'oe', 'Ö': 'OE',
        'ü': 'ue', 'Ü': 'UE',
        
        # French characters (most handled by decomposition)
        'œ': 'oe', 'Œ': 'OE',
        
        # Spanish/Portuguese characters
        'ñ': 'n', 'Ñ': 'N',
        'ç': 'c', 'Ç': 'C',
        
        # Hungarian characters
        'ő': 'o', 'Ő': 'O',
        'ű': 'u', 'Ű': 'U',
        
        # Romanian characters
        'ă': 'a', 'Ă': 'A',
        'ș': 's', 'Ș': 'S',
        'ț': 't', 'Ț': 'T',
        
        # Baltic characters
        'ą': 'a', 'Ą': 'A',
        'ę': 'e', 'Ę': 'E',
        'į': 'i', 'Į': 'I',
        'ų': 'u', 'Ų': 'U',
        'ė': 'e', 'Ė': 'E',
        
        # Icelandic characters
        'þ': 'th', 'Þ': 'Th',
        'ð': 'd', 'Ð': 'D',
        
        # Croatian/Serbian/Bosnian
        'đ': 'd', 'Đ': 'D',
        
        # Cyrillic approximations (basic)
        'а': 'a', 'А': 'A',
        'б': 'b', 'Б': 'B',
        'в': 'v', 'В': 'V',
        'г': 'g', 'Г': 'G',
        'д': 'd', 'Д': 'D',
        'е': 'e', 'Е': 'E',
        'ж': 'zh', 'Ж': 'Zh',
        'з': 'z', 'З': 'Z',
        'и': 'i', 'И': 'I',
        'й': 'y', 'Й': 'Y',
        'к': 'k', 'К': 'K',
        'л': 'l', 'Л': 'L',
        'м': 'm', 'М': 'M',
        'н': 'n', 'Н': 'N',
        'о': 'o', 'О': 'O',
        'п': 'p', 'П': 'P',
        'р': 'r', 'Р': 'R',
        'с': 's', 'С': 'S',
        'т': 't', 'Т': 'T',
        'у': 'u', 'У': 'U',
        'ф': 'f', 'Ф': 'F',
        'х': 'kh', 'Х': 'Kh',
        'ц': 'ts', 'Ц': 'Ts',
        'ч': 'ch', 'Ч': 'Ch',
        'ш': 'sh', 'Ш': 'Sh',
        'щ': 'shch', 'Щ': 'Shch',
        'ъ': '', 'Ъ': '',
        'ы': 'y', 'Ы': 'Y',
        'ь': '', 'Ь': '',
        'э': 'e', 'Э': 'E',
        'ю': 'yu', 'Ю': 'Yu',
        'я': 'ya', 'Я': 'Ya'
    }
    
    for old, new in replacements.items():
        result = result.replace(old, new)
    
    return result

def fix_character_encoding(text):
    """
    Fix common character encoding issues while preserving special characters.
    This function repairs encoding corruption without transliterating.

    Args:
        text: Text string to fix

    Returns:
        Fixed text string with proper characters preserved
    """
    if not isinstance(text, str) or text in ['nan', 'None', '']:
        return text

    # Comprehensive character replacement mapping for encoding fixes
    replacements = {
        # Common punctuation issues
        'â€™': "'",      # Right single quotation mark
        'â€œ': '"',      # Left double quotation mark
        'â€': '"',       # Right double quotation mark
        'â€"': '–',      # En dash
        'â€"': '—',      # Em dash
        'â€¦': '…',      # Horizontal ellipsis

        # Latin characters with diacritics (Ã encoding issues)
        'Ã¼': 'ü',       # u with diaeresis
        'Ã¡': 'á',       # a with acute
        'Ã©': 'é',       # e with acute
        'Ã­': 'í',       # i with acute
        'Ã³': 'ó',       # o with acute
        'Ãº': 'ú',       # u with acute
        'Ã±': 'ñ',       # n with tilde
        'Ã§': 'ç',       # c with cedilla
        'Ã ': 'à',       # a with grave
        'Ã¨': 'è',       # e with grave
        'Ã¬': 'ì',       # i with grave
        'Ã²': 'ò',       # o with grave
        'Ã¹': 'ù',       # u with grave
        'Ã¢': 'â',       # a with circumflex
        'Ãª': 'ê',       # e with circumflex
        'Ã®': 'î',       # i with circumflex
        'Ã´': 'ô',       # o with circumflex
        'Ã»': 'û',       # u with circumflex
        'Ã¤': 'ä',       # a with diaeresis
        'Ã«': 'ë',       # e with diaeresis
        'Ã¯': 'ï',       # i with diaeresis
        'Ã¶': 'ö',       # o with diaeresis
        'Ã¿': 'ÿ',       # y with diaeresis
        'Ã…': 'Å',       # A with ring above
        'Ã¥': 'å',       # a with ring above
        'Ã†': 'Æ',       # AE ligature
        'Ã¦': 'æ',       # ae ligature
        'Ã˜': 'Ø',       # O with stroke
        'Ã¸': 'ø',       # o with stroke
        'Ã': 'Ý',        # Y with acute
        'Ã½': 'ý',       # y with acute
        'Ãƒ': 'Ã',       # A with tilde
        'Ã£': 'ã',       # a with tilde
        'Ã•': 'Õ',       # O with tilde
        'Ãµ': 'õ',       # o with tilde

        # Fraction character encoding issues (1⁄4 = ¼, etc.)
        'A1⁄4': 'Ä',     # A with diaeresis
        'O1⁄4': 'Ö',     # O with diaeresis
        'U1⁄4': 'Ü',     # U with diaeresis
        'a1⁄4': 'ä',     # a with diaeresis
        'o1⁄4': 'ö',     # o with diaeresis
        'u1⁄4': 'ü',     # u with diaeresis
        'ss1⁄4': 'ß',    # German sharp s

        # Specific name fixes (common patterns)
        'TA1⁄4lay': 'Tülay',      # Turkish name
        'KA1⁄4hnel': 'Kühnel',    # German name
        'MA1⁄4ller': 'Müller',    # German name
        'BA1⁄4rger': 'Bürger',    # German name
        'GA1⁄4nther': 'Günther',  # German name
        'HA1⁄4bner': 'Hübner',    # German name
        'KA1⁄4hn': 'Kühn',        # German name
        'LA1⁄4tke': 'Lütke',      # German name
        'SA1⁄4ss': 'Süß',         # German name
        'WA1⁄4rth': 'Würth',      # German name

        # Cyrillic characters (common encoding issues)
        'Ð°': 'а',       # Cyrillic small letter a
        'Ð±': 'б',       # Cyrillic small letter be
        'Ð²': 'в',       # Cyrillic small letter ve
        'Ð³': 'г',       # Cyrillic small letter ghe
        'Ð´': 'д',       # Cyrillic small letter de
        'Ðµ': 'е',       # Cyrillic small letter ie
        'Ð¶': 'ж',       # Cyrillic small letter zhe
        'Ð·': 'з',       # Cyrillic small letter ze
        'Ð¸': 'и',       # Cyrillic small letter i
        'Ð¹': 'й',       # Cyrillic small letter short i
        'Ðº': 'к',       # Cyrillic small letter ka
        'Ð»': 'л',       # Cyrillic small letter el
        'Ð¼': 'м',       # Cyrillic small letter em
        'Ð½': 'н',       # Cyrillic small letter en
        'Ð¾': 'о',       # Cyrillic small letter o
        'Ð¿': 'п',       # Cyrillic small letter pe
        'Ñ€': 'р',       # Cyrillic small letter er
        'Ñ': 'с',        # Cyrillic small letter es
        'Ñ‚': 'т',       # Cyrillic small letter te
        'Ñƒ': 'у',       # Cyrillic small letter u
        'Ñ„': 'ф',       # Cyrillic small letter ef
        'Ñ…': 'х',       # Cyrillic small letter ha
        'Ñ†': 'ц',       # Cyrillic small letter tse
        'Ñ‡': 'ч',       # Cyrillic small letter che
        'Ñˆ': 'ш',       # Cyrillic small letter sha
        'Ñ‰': 'щ',       # Cyrillic small letter shcha
        'ÑŠ': 'ъ',       # Cyrillic hard sign
        'Ñ‹': 'ы',       # Cyrillic small letter yeru
        'ÑŒ': 'ь',       # Cyrillic soft sign
        'Ñ': 'э',        # Cyrillic small letter e
        'ÑŽ': 'ю',       # Cyrillic small letter yu
        'Ñ': 'я',        # Cyrillic small letter ya

        # Greek characters (common encoding issues)
        'Î±': 'α',       # Greek small letter alpha
        'Î²': 'β',       # Greek small letter beta
        'Î³': 'γ',       # Greek small letter gamma
        'Î´': 'δ',       # Greek small letter delta
        'Îµ': 'ε',       # Greek small letter epsilon
        'Î¶': 'ζ',       # Greek small letter zeta
        'Î·': 'η',       # Greek small letter eta
        'Î¸': 'θ',       # Greek small letter theta
        'Î¹': 'ι',       # Greek small letter iota
        'Îº': 'κ',       # Greek small letter kappa
        'Î»': 'λ',       # Greek small letter lamda
        'Î¼': 'μ',       # Greek small letter mu
        'Î½': 'ν',       # Greek small letter nu
        'Î¾': 'ξ',       # Greek small letter xi
        'Î¿': 'ο',       # Greek small letter omicron
        'Ï€': 'π',       # Greek small letter pi
        'Ï': 'ρ',        # Greek small letter rho
        'Ï‚': 'ς',       # Greek small letter final sigma
        'Ïƒ': 'σ',       # Greek small letter sigma
        'Ï„': 'τ',       # Greek small letter tau
        'Ï…': 'υ',       # Greek small letter upsilon
        'Ï†': 'φ',       # Greek small letter phi
        'Ï‡': 'χ',       # Greek small letter chi
        'Ïˆ': 'ψ',       # Greek small letter psi
        'Ï‰': 'ω',       # Greek small letter omega

        # Additional common patterns
        '1⁄4': 'ü',      # Generic fraction to ü replacement
        '1⁄2': 'ö',      # Generic fraction to ö replacement
        '3⁄4': 'ä',      # Generic fraction to ä replacement

        # Clean up pandas artifacts
        'nan': '',
        'None': '',
        'NaN': ''
    }

    # Apply direct replacements
    for old, new in replacements.items():
        text = text.replace(old, new)

    # Apply regex-based pattern matching for remaining fraction character issues
    text = re.sub(r'([A-Za-z])1⁄4', r'\1ü', text)
    text = re.sub(r'([A-Za-z])1⁄2', r'\1ö', text)
    text = re.sub(r'([A-Za-z])3⁄4', r'\1ä', text)

    # Normalize Unicode characters
    try:
        text = unicodedata.normalize('NFC', text)
    except:
        pass

    return text

def detect_file_encoding(file_path):
    """
    Detect the encoding of a file using chardet and validation.
    """
    if not CHARDET_AVAILABLE:
        return 'latin-1'  # Default fallback

    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(50000)  # Read more data for better detection
            result = chardet.detect(raw_data)
            encoding = result['encoding']
            confidence = result['confidence']

            # Enhanced encoding preference list
            preferred_encodings = [
                'utf-8-sig',  # UTF-8 with BOM
                'utf-8',      # UTF-8 without BOM
                'cp1252',     # Windows-1252 (Western European)
                'iso-8859-1', # Latin-1
                'iso-8859-15', # Latin-9 (includes Euro symbol)
                'cp1250',     # Windows-1250 (Central European)
                'cp1251',     # Windows-1251 (Cyrillic)
                'latin-1'     # Last resort
            ]

            # If chardet gives us a good result, try it first
            if confidence >= 0.8 and encoding:
                # Map some common detected encodings to preferred ones
                encoding_map = {
                    'ascii': 'utf-8-sig',
                    'utf-8': 'utf-8-sig',
                    'windows-1252': 'cp1252',
                    'windows-1250': 'cp1250',
                    'windows-1251': 'cp1251',
                    'iso-8859-1': 'iso-8859-1'
                }

                mapped_encoding = encoding_map.get(encoding.lower(), encoding)

                # Test the detected/mapped encoding first
                try:
                    with open(file_path, 'r', encoding=mapped_encoding) as test_file:
                        content = test_file.read(5000)
                        # Check for common problematic character sequences
                        if not any(seq in content for seq in ['â€™', 'â€œ', 'â€', 'Ã¼', 'Ã¡', 'Ã©', 'Ã­', 'Ã³', 'Ãº']):
                            return mapped_encoding
                except:
                    pass

            # Try preferred encodings in order
            for enc in preferred_encodings:
                try:
                    with open(file_path, 'r', encoding=enc) as test_file:
                        content = test_file.read(5000)
                        # Check for problematic character sequences
                        if not any(seq in content for seq in ['â€™', 'â€œ', 'â€', 'Ã¼', 'Ã¡', 'Ã©', 'Ã­', 'Ã³', 'Ãº']):
                            return enc
                except:
                    continue

            return 'latin-1'  # Final fallback
    except Exception as e:
        return 'latin-1'  # Default fallback

def convert_csv_encoding(directory_path, preserve_characters=True, transliterate_mode=False):
    """
    Converts all CSV files in a directory to utf-8-sig encoding with options for
    character preservation or transliteration.

    Args:
        directory_path (str): Path to the directory containing CSV files
        preserve_characters (bool): If True, preserve special characters; if False, transliterate
        transliterate_mode (bool): If True, force transliteration mode (overrides preserve_characters)
    """
    console = Console()

    if transliterate_mode:
        console.print(f"\n[bold blue]Converting CSV files to utf-8-sig and transliterating characters to ASCII[/bold blue]")
        preserve_characters = False
    elif preserve_characters:
        console.print(f"\n[bold blue]Converting CSV files to utf-8-sig with character preservation[/bold blue]")
    else:
        console.print(f"\n[bold blue]Converting CSV files to utf-8-sig and transliterating characters[/bold blue]")
    
    # Get all CSV files in the directory
    csv_files = list(Path(directory_path).glob("*.csv"))
    
    if not csv_files:
        console.print(f"[yellow]No CSV files found in {directory_path}[/yellow]")
        return
    
    console.print(f"[green]Found {len(csv_files)} CSV files to convert[/green]")
    
    # Create backup directory
    backup_dir = os.path.join(directory_path, "original_encoding_backup")
    os.makedirs(backup_dir, exist_ok=True)
    console.print(f"[blue]Created backup directory: {backup_dir}[/blue]")
    
    # Process each file with progress bar
    with Progress() as progress:
        task = progress.add_task("[cyan]Converting files...", total=len(csv_files))
        
        for csv_file in csv_files:
            try:
                # Detect the file encoding
                detected_encoding = detect_file_encoding(csv_file)
                console.print(f"[cyan]Processing {csv_file.name} (detected: {detected_encoding})[/cyan]")

                # Try to read the file with detected encoding
                try:
                    df = pd.read_csv(
                        csv_file,
                        encoding=detected_encoding,
                        dtype=str,
                        keep_default_na=False,
                        na_filter=False
                    )
                except Exception as read_error:
                    # Try fallback encodings
                    fallback_encodings = ['latin-1', 'utf-8', 'cp1252', 'iso-8859-1']
                    df = None
                    for fallback_enc in fallback_encodings:
                        try:
                            df = pd.read_csv(
                                csv_file,
                                encoding=fallback_enc,
                                dtype=str,
                                keep_default_na=False,
                                na_filter=False
                            )
                            console.print(f"[yellow]Used fallback encoding: {fallback_enc}[/yellow]")
                            break
                        except:
                            continue

                    if df is None:
                        raise Exception(f"Could not read file with any encoding")

                # Create backup of original file
                backup_path = os.path.join(backup_dir, csv_file.name)
                with open(csv_file, 'rb') as f:
                    content = f.read()
                with open(backup_path, 'wb') as f:
                    f.write(content)

                # Fix column names with BOM characters and apply character processing
                new_columns = {}
                for col in df.columns:
                    # Check for BOM characters in column names
                    if re.match(r'ï»¿.*Author Name.*', col) or col.startswith('\ufeff'):
                        new_columns[col] = "Author Name"
                    elif "Author Name" in col and col != "Author Name":
                        new_columns[col] = "Author Name"
                    else:
                        if preserve_characters:
                            # Fix encoding issues but preserve special characters
                            fixed_col = fix_character_encoding(col)
                            if fixed_col != col:
                                new_columns[col] = fixed_col
                        else:
                            # Transliterate column names to ASCII
                            transliterated = transliterate(col)
                            if transliterated != col:
                                new_columns[col] = transliterated

                # Rename columns if needed
                if new_columns:
                    df = df.rename(columns=new_columns)
                    console.print(f"[green]Fixed column headers in {csv_file.name}[/green]")

                # Process string data in all columns
                for column in df.columns:
                    if df[column].dtype == 'object':  # Only process string columns
                        if preserve_characters:
                            # Fix encoding issues but preserve special characters
                            df[column] = df[column].apply(fix_character_encoding)
                        else:
                            # Transliterate to ASCII
                            df[column] = df[column].apply(transliterate)

                # Write with utf-8-sig encoding
                df.to_csv(csv_file, index=False, encoding='utf-8-sig', errors='replace')

                mode_text = "preserved" if preserve_characters else "transliterated"
                progress.update(task, advance=1, description=f"Converted: {csv_file.name} ({mode_text})")

            except Exception as e:
                progress.update(task, advance=1, description=f"Error with {csv_file.name}")
                console.print(f"[red]Error converting {csv_file.name}: {str(e)}[/red]")
    
    mode_text = "with character preservation" if preserve_characters else "with transliteration to ASCII"
    console.print(f"[bold green]Conversion complete {mode_text}! Original files backed up to {backup_dir}[/bold green]")

def read_directories_from_file(file_path):
    """
    Read directory paths from a text file.

    Args:
        file_path (str): Path to the text file containing directory paths

    Returns:
        list: List of valid directory paths
    """
    console = Console()
    directories = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # Skip empty lines and comments
                    if os.path.exists(line) and os.path.isdir(line):
                        directories.append(line)
                        console.print(f"[green]✓ Valid directory: {line}[/green]")
                    else:
                        console.print(f"[yellow]⚠ Line {line_num}: Directory not found or invalid: {line}[/yellow]")
    except FileNotFoundError:
        console.print(f"[red]Error: File not found: {file_path}[/red]")
        return []
    except Exception as e:
        console.print(f"[red]Error reading file {file_path}: {str(e)}[/red]")
        return []

    return directories

def batch_convert_csv_encoding(directories_file, preserve_characters=True, transliterate_mode=False):
    """
    Batch convert CSV files in multiple directories specified in a text file.

    Args:
        directories_file (str): Path to text file containing directory paths
        preserve_characters (bool): If True, preserve special characters; if False, transliterate
        transliterate_mode (bool): If True, force transliteration mode (overrides preserve_characters)
    """
    console = Console()

    console.print(f"\n[bold blue]Starting batch CSV encoding conversion[/bold blue]")
    console.print(f"[cyan]Reading directories from: {directories_file}[/cyan]")

    directories = read_directories_from_file(directories_file)

    if not directories:
        console.print("[red]No valid directories found. Exiting.[/red]")
        return

    console.print(f"[green]Found {len(directories)} valid directories to process[/green]")

    total_processed = 0
    successful_dirs = 0

    for i, directory in enumerate(directories, 1):
        console.print(f"\n[bold yellow]Processing directory {i}/{len(directories)}: {directory}[/bold yellow]")

        try:
            # Count CSV files before processing
            csv_files = list(Path(directory).glob("*.csv"))
            if csv_files:
                convert_csv_encoding(directory, preserve_characters, transliterate_mode)
                total_processed += len(csv_files)
                successful_dirs += 1
            else:
                console.print(f"[yellow]No CSV files found in {directory}[/yellow]")
        except Exception as e:
            console.print(f"[red]Error processing directory {directory}: {str(e)}[/red]")

    # Summary
    console.print(f"\n[bold green]Batch processing complete![/bold green]")
    console.print(f"[cyan]Directories processed successfully: {successful_dirs}/{len(directories)}[/cyan]")
    console.print(f"[cyan]Total CSV files processed: {total_processed}[/cyan]")

if __name__ == "__main__":
    console = Console()

    console.print("[bold blue]CSV Encoding Converter[/bold blue]")
    console.print("=" * 50)

    # Ask user for batch or single directory mode
    console.print("\n[bold yellow]Choose operation mode:[/bold yellow]")
    console.print("[cyan]1. Single directory - Convert CSV files in one directory[/cyan]")
    console.print("[cyan]2. Batch processing - Convert CSV files in multiple directories from a text file[/cyan]")

    while True:
        mode_choice = input("\nEnter your choice (1 or 2): ").strip()
        if mode_choice in ["1", "2"]:
            break
        else:
            console.print("[red]Please enter 1 or 2[/red]")

    # Ask user for processing mode
    console.print("\n[bold yellow]Choose character processing mode:[/bold yellow]")
    console.print("[cyan]1. Preserve special characters (recommended for international names)[/cyan]")
    console.print("[cyan]2. Transliterate to ASCII (convert ü→ue, ñ→n, etc.)[/cyan]")

    while True:
        choice = input("\nEnter your choice (1 or 2): ").strip()
        if choice == "1":
            preserve_characters = True
            transliterate_mode = False
            break
        elif choice == "2":
            preserve_characters = False
            transliterate_mode = True
            break
        else:
            console.print("[red]Please enter 1 or 2[/red]")

    if mode_choice == "1":
        # Single directory mode
        directory = input("\nEnter the directory path containing CSV files: ")
        convert_csv_encoding(directory, preserve_characters, transliterate_mode)
    else:
        # Batch processing mode
        directories_file = input("\nEnter the path to the text file containing directory paths: ")
        batch_convert_csv_encoding(directories_file, preserve_characters, transliterate_mode)







