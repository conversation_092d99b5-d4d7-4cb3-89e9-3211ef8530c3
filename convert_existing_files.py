#!/usr/bin/env python3
"""
<PERSON>ript to convert existing unsubscribe CSV files to contain only Email column
and save both CSV and XLSX versions.
"""

import pandas as pd
import os
from pathlib import Path

def convert_files(directory_path):
    """
    Convert existing CSV files to contain only Email column and save both CSV and XLSX versions.
    
    Args:
        directory_path (str): Directory containing the CSV files to convert
    """
    
    print(f"Processing files in: {directory_path}")
    
    # Find all CSV files that end with '_Unsubscribes.csv'
    csv_files = []
    for file in os.listdir(directory_path):
        if file.endswith('_Unsubscribes.csv'):
            csv_files.append(file)
    
    print(f"Found {len(csv_files)} files to convert")
    
    if not csv_files:
        print("No files found with '_Unsubscribes.csv' pattern")
        return
    
    for csv_file in csv_files:
        try:
            print(f"\nProcessing: {csv_file}")
            
            # Read the CSV file
            file_path = os.path.join(directory_path, csv_file)
            df = pd.read_csv(file_path)
            
            # Check if email column exists
            if 'email' not in df.columns:
                print(f"  Warning: 'email' column not found in {csv_file}")
                print(f"  Available columns: {list(df.columns)}")
                continue
            
            # Select only email column and rename it to 'Email'
            email_only_df = df[['email']].copy()
            email_only_df.rename(columns={'email': 'Email'}, inplace=True)
            
            # Remove duplicates
            original_count = len(email_only_df)
            email_only_df.drop_duplicates(inplace=True)
            final_count = len(email_only_df)
            duplicates_removed = original_count - final_count
            
            # Create new filenames (overwrite existing)
            csv_path = file_path  # Same CSV file
            xlsx_filename = csv_file.replace('.csv', '.xlsx')
            xlsx_path = os.path.join(directory_path, xlsx_filename)
            
            # Save to CSV (overwrite)
            email_only_df.to_csv(csv_path, index=False)
            
            # Save to XLSX
            email_only_df.to_excel(xlsx_path, index=False)
            
            print(f"  -> Converted {final_count} unique emails")
            if duplicates_removed > 0:
                print(f"  -> Removed {duplicates_removed} duplicate emails")
            print(f"  -> Updated: {csv_file}")
            print(f"  -> Created: {xlsx_filename}")
            
        except Exception as e:
            print(f"  Error processing {csv_file}: {str(e)}")
    
    print(f"\nConversion complete!")

def main():
    # Define the directory path
    directory = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs"
    
    print("=== CSV to Email-Only Converter ===")
    print(f"Directory: {directory}")
    print("-" * 50)
    
    # Check if directory exists
    if not os.path.exists(directory):
        print(f"Error: Directory does not exist - {directory}")
        return
    
    # Run the conversion
    convert_files(directory)

if __name__ == "__main__":
    main()
