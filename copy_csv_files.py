#!/usr/bin/env python3
"""
CSV Files Batch Copy Script

Copies all CSV files from multiple conference directories with "cluster\sorted" structure
to a single destination directory.

Expected structure:
H:\mailwizz\2026\March\
├── CIOC-2026\cluster\sorted\*.csv
├── Conference2-2026\cluster\sorted\*.csv
└── Conference3-2026\cluster\sorted\*.csv

Author: AI Assistant
Date: 2025-11-27
"""

import os
import shutil
import glob
from pathlib import Path
from datetime import datetime

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"  {timestamp}")
    print(f"{'='*60}")

def print_section(title):
    """Print a section header."""
    print(f"\n>> {title}")
    print("-" * (len(title) + 4))

def copy_csv_files_from_conferences(base_path, destination_path):
    """
    Copy all CSV files from conference directories with cluster\sorted structure.
    
    Args:
        base_path (str): Base path containing conference directories (e.g., "H:\mailwizz\2026\March")
        destination_path (str): Destination directory to copy all CSV files
    """
    
    print_header("CSV Files Batch Copy Tool")
    
    # Validate base path
    if not os.path.exists(base_path):
        print(f"❌ Error: Base path '{base_path}' does not exist!")
        return
    
    print_section("Scanning Conference Directories")
    print(f"Base path: {base_path}")
    
    # Find all conference directories
    conference_dirs = []
    for item in os.listdir(base_path):
        item_path = os.path.join(base_path, item)
        if os.path.isdir(item_path):
            # Check if it has cluster\sorted structure
            sorted_path = os.path.join(item_path, "cluster", "sorted")
            if os.path.exists(sorted_path):
                conference_dirs.append((item, sorted_path))
                print(f"✓ Found: {item}")
            else:
                print(f"⚠ Skipped: {item} (no cluster\\sorted directory)")
    
    if not conference_dirs:
        print("❌ No conference directories with 'cluster\\sorted' structure found!")
        return
    
    print(f"\n✓ Found {len(conference_dirs)} conference directories with CSV files")
    
    # Create destination directory if it doesn't exist
    print_section("Preparing Destination Directory")
    os.makedirs(destination_path, exist_ok=True)
    print(f"✓ Destination directory ready: {destination_path}")
    
    # Copy CSV files from each conference
    print_section("Copying CSV Files")
    total_files_copied = 0
    total_files_skipped = 0
    
    for conf_name, sorted_path in conference_dirs:
        print(f"\n📁 Processing: {conf_name}")
        
        # Find all CSV files in the sorted directory
        csv_pattern = os.path.join(sorted_path, "*.csv")
        csv_files = glob.glob(csv_pattern)
        
        if not csv_files:
            print(f"   ⚠ No CSV files found in {conf_name}")
            continue
        
        print(f"   📄 Found {len(csv_files)} CSV files")
        
        # Copy each CSV file
        for csv_file in csv_files:
            filename = os.path.basename(csv_file)
            destination_file = os.path.join(destination_path, filename)
            
            try:
                # Check if file already exists in destination
                if os.path.exists(destination_file):
                    print(f"   ⚠ Skipped: {filename} (already exists)")
                    total_files_skipped += 1
                else:
                    shutil.copy2(csv_file, destination_file)
                    print(f"   ✓ Copied: {filename}")
                    total_files_copied += 1
                    
            except Exception as e:
                print(f"   ❌ Error copying {filename}: {str(e)}")
    
    # Print summary
    print_header("Copy Operation Complete!")
    print(f"📊 Summary:")
    print(f"   • Conference directories processed: {len(conference_dirs)}")
    print(f"   • Files successfully copied: {total_files_copied}")
    print(f"   • Files skipped (already exist): {total_files_skipped}")
    print(f"   • Destination directory: {destination_path}")
    
    if total_files_copied > 0:
        print(f"\n✅ Successfully copied {total_files_copied} CSV files!")
    else:
        print(f"\n⚠ No new files were copied.")

def main():
    """Main function to get user input and execute the copy operation."""
    
    print_header("CSV Files Batch Copy Tool")
    
    # Get base path from user
    print_section("Input Configuration")
    base_path = input("Enter the base path (e.g., H:\\mailwizz\\2026\\March): ").strip()
    
    if not base_path:
        print("❌ Error: Base path cannot be empty!")
        return
    
    # Get destination path from user
    destination_path = input("Enter the destination directory path: ").strip()
    
    if not destination_path:
        print("❌ Error: Destination path cannot be empty!")
        return
    
    # Confirm the operation
    print(f"\n📋 Configuration:")
    print(f"   Source: {base_path}")
    print(f"   Destination: {destination_path}")
    
    confirm = input("\nProceed with copying CSV files? (y/n): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        copy_csv_files_from_conferences(base_path, destination_path)
    else:
        print("❌ Operation cancelled by user.")

if __name__ == "__main__":
    main()
