import os
import pandas as pd
from pathlib import Path
from docx import Document

def csv_to_pubmed_query():
    """
    Convert CSV keywords to PubMed search query format
    """
    # Get directory path from user
    print("📁 Enter the directory path containing your CSV files:")
    print("   Example: H:\\Keywords or C:\\Users\\<USER>\\Documents\\Keywords")
    keywords_dir = input("Directory path: ").strip()

    # Remove quotes if user added them
    keywords_dir = keywords_dir.strip('"').strip("'")

    # Check if directory exists
    if not os.path.exists(keywords_dir):
        print(f"❌ Directory not found: {keywords_dir}")
        print("Please make sure the directory exists and try again.")
        return

    # Change to the keywords directory
    os.chdir(keywords_dir)
    print(f"✅ Working in directory: {keywords_dir}")
    
    # List available CSV files
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
    
    if not csv_files:
        print("❌ No CSV files found in the directory.")
        return
    
    print("📁 Available CSV files:")
    for i, file in enumerate(csv_files, 1):
        print(f"  {i}. {file}")
    
    # Get filename from user
    if len(csv_files) == 1:
        filename = csv_files[0]
        print(f"\n✅ Using: {filename}")
    else:
        try:
            choice = int(input("\nEnter file number: ")) - 1
            filename = csv_files[choice]
        except (ValueError, IndexError):
            print("❌ Invalid selection.")
            return
    
    try:
        # Read the CSV file with multiple encoding attempts
        print(f"\n📖 Reading {filename}...")

        # Try different encodings with priority for Unicode support
        encodings = [
            'utf-8-sig',    # UTF-8 with BOM (best for preserving all characters)
            'utf-8',        # Standard UTF-8
            'utf-16',       # UTF-16 (good for international characters)
            'cp1252',       # Windows-1252 (common for Windows Excel exports)
            'latin1',       # ISO 8859-1 (fallback)
            'iso-8859-1'    # Alternative Latin-1
        ]
        df = None
        used_encoding = None

        for encoding in encodings:
            try:
                print(f"  🔄 Trying encoding: {encoding}")
                df = pd.read_csv(filename, encoding=encoding)
                used_encoding = encoding
                print(f"  ✅ Successfully read with {encoding} encoding")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"  ⚠️ Error with {encoding}: {str(e)}")
                continue

        if df is None:
            print("❌ Could not read the file with any supported encoding.")
            return

        # Check if 'Keywords' column exists
        if 'Keywords' not in df.columns:
            print(f"❌ 'Keywords' column not found. Available columns: {list(df.columns)}")
            return
        
        # Extract keywords and remove empty/null values
        keywords = df['Keywords'].dropna().astype(str).tolist()

        if not keywords:
            print("❌ No keywords found in the file.")
            return

        print(f"✅ Found {len(keywords)} keywords")

        # Format keywords for PubMed query with character preservation
        formatted_keywords = []
        preserved_chars = 0
        special_chars_found = set()

        for keyword in keywords:
            # Only strip leading/trailing whitespace, preserve all other characters
            clean_keyword = keyword.strip()
            if clean_keyword:
                # Check for special characters for reporting
                for char in clean_keyword:
                    if not char.isalnum() and char not in [' ', '-', '_']:
                        special_chars_found.add(char)
                        preserved_chars += 1

                # Preserve all characters including apostrophes, accents, etc.
                formatted_keywords.append(f'"{clean_keyword}"[Title/Abstract]')

        # Report special characters found
        if special_chars_found:
            print(f"✅ Preserved {preserved_chars} special characters: {', '.join(sorted(special_chars_found))}")

        # Split keywords into chunks of 200
        chunk_size = 200
        keyword_chunks = [formatted_keywords[i:i + chunk_size] for i in range(0, len(formatted_keywords), chunk_size)]

        print(f"📊 Total keywords: {len(formatted_keywords)}")
        print(f"📄 Creating {len(keyword_chunks)} document(s) with max {chunk_size} keywords each")

        # Create base filename
        base_name = Path(filename).stem
        created_files = []

        # Process each chunk
        for chunk_index, chunk in enumerate(keyword_chunks, 1):
            # Join keywords in this chunk with OR operator
            pubmed_query = " OR ".join(chunk)
            final_query = f"({pubmed_query})"

            # Create output filename for this chunk
            if len(keyword_chunks) == 1:
                output_filename = f"{base_name}_pubmed_query.docx"
            else:
                output_filename = f"{base_name}_pubmed_query_part{chunk_index}.docx"

            # Create Word document
            doc = Document()

            # # Add title
            # if len(keyword_chunks) == 1:
            #     title = doc.add_heading('PubMed Search Query', 0)
            # else:
            #     title = doc.add_heading(f'PubMed Search Query - Part {chunk_index}', 0)

            # # Add metadata
            # doc.add_heading('Query Details:', level=1)
            # details_para = doc.add_paragraph()
            # details_para.add_run(f"Source File: ").bold = True
            # details_para.add_run(f"{filename}\n")
            # details_para.add_run(f"Total Keywords in File: ").bold = True
            # details_para.add_run(f"{len(formatted_keywords)}\n")
            # details_para.add_run(f"Keywords in This Part: ").bold = True
            # details_para.add_run(f"{len(chunk)}\n")
            # if len(keyword_chunks) > 1:
            #     details_para.add_run(f"Part: ").bold = True
            #     details_para.add_run(f"{chunk_index} of {len(keyword_chunks)}\n")
            # details_para.add_run(f"Generated: ").bold = True
            # details_para.add_run(f"{pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # # Add query section
            # doc.add_heading('Search Query:', level=1)

            # Add query directly without any metadata or stats
            query_para = doc.add_paragraph(final_query)

            # Save the document with UTF-8 encoding preservation
            try:
                doc.save(output_filename)
                created_files.append(output_filename)
                print(f"  ✅ Created: {output_filename} ({len(chunk)} keywords)")
            except Exception as e:
                print(f"  ❌ Error saving {output_filename}: {str(e)}")
                continue

        print(f"\n🎉 Success!")
        print(f"📄 Created {len(created_files)} document(s):")
        for file in created_files:
            print(f"  • {file}")
        print(f"📊 Total keywords processed: {len(formatted_keywords)}")

        # Display preview of first chunk
        if keyword_chunks:
            first_query = " OR ".join(keyword_chunks[0])
            preview = f"({first_query})"
            print(f"\n📋 Preview of Part 1 (first 200 characters):")
            preview_text = preview[:200] + "..." if len(preview) > 200 else preview
            print(preview_text)
        
    except Exception as e:
        print(f"❌ Error processing file: {str(e)}")

if __name__ == "__main__":
    print("🔍 CSV to PubMed Query Converter")
    print("=" * 40)
    csv_to_pubmed_query()
