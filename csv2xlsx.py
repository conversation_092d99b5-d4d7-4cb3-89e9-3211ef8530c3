import pandas as pd
import glob
import os
import warnings

# Suppress pandas warnings - compatible with both old and new pandas versions
try:
    # Try the new location first (pandas >= 1.5.0)
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        # Try the old location (pandas < 1.5.0)
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        # If neither works, just ignore pandas warnings in general
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")
pd.io.formats.excel.ExcelFormatter.header_style = None


# Change this to the directory where your CSV files are located
csv_file_directory = input('path_to_your_csv_files:')

for csvfile in glob.glob(os.path.join(csv_file_directory, '*.csv')):
    df = pd.read_csv(csvfile)
    df.to_excel(csvfile[:-4] + '.xlsx', index=False)
