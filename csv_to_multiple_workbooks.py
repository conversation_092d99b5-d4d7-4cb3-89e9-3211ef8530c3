import pandas as pd
import os
import glob
from pathlib import Path
import shutil
from datetime import datetime
import re


def cleanup_sit_folder(sit_path):
    """
    Remove timestamped Excel files from sit folder, keeping only the original template.
    Timestamped files follow pattern: filename_YYYYMMDD_HHMMSS_N.xlsx

    Args:
        sit_path (str): Path to the sit folder
    """
    if not os.path.exists(sit_path):
        return

    # Pattern to match timestamped files: *_YYYYMMDD_HHMMSS_*.xlsx
    timestamp_pattern = re.compile(r'.*_\d{8}_\d{6}_\d+\.xlsx$')

    excel_files = glob.glob(os.path.join(sit_path, "*.xlsx"))
    removed_count = 0

    for excel_file in excel_files:
        filename = os.path.basename(excel_file)
        if timestamp_pattern.match(filename):
            try:
                os.remove(excel_file)
                removed_count += 1
                print(f"  Removed: {filename}")
            except Exception as e:
                print(f"  Warning: Could not remove {filename}: {e}")

    if removed_count > 0:
        print(f"  ✓ Cleaned up {removed_count} timestamped file(s) from sit folder")
    else:
        print(f"  ✓ No timestamped files to clean up in sit folder")


def copy_csv_to_multiple_workbooks(csv_file_path, excel_template_path, max_rows_per_workbook=10000):
    """
    Copy data from CSV file to multiple Excel workbooks with row limit per workbook.
    Each workbook will have the same Worksheet 2 (configuration) from the template.
    
    Args:
        csv_file_path (str): Path to the CSV file
        excel_template_path (str): Path to the template Excel file
        max_rows_per_workbook (int): Maximum rows per workbook (default: 10000)
    """
    try:
        # Read the CSV file
        print(f"Reading CSV file: {csv_file_path}")
        df_csv = pd.read_csv(csv_file_path)
        print(f"CSV contains {len(df_csv)} rows and {len(df_csv.columns)} columns")
        print(f"Columns: {list(df_csv.columns)}")
        
        # Check if template Excel file exists
        if not os.path.exists(excel_template_path):
            print(f"Error: Template Excel file not found: {excel_template_path}")
            return False
        
        # Read the template Excel file to get all sheets
        print(f"Reading template Excel file: {excel_template_path}")
        xl_file = pd.ExcelFile(excel_template_path)

        # Read all sheets from template to preserve them
        template_sheets = {}
        for sheet_name in xl_file.sheet_names:
            template_sheets[sheet_name] = pd.read_excel(excel_template_path, sheet_name=sheet_name)
            print(f"Read template sheet '{sheet_name}' with shape {template_sheets[sheet_name].shape}")

        # Get the headers from Worksheet 1 in template (should be 'Name', 'Email')
        worksheet1_template = template_sheets.get('Worksheet 1', pd.DataFrame())
        if not worksheet1_template.empty:
            original_headers = list(worksheet1_template.columns)
            print(f"Template headers: {original_headers}")
        else:
            original_headers = ['Name', 'Email']  # Default fallback
            print(f"Using default headers: {original_headers}")

        # Map CSV data to template structure
        # Assume first 2 columns of CSV map to Name, Email
        csv_columns = list(df_csv.columns)
        if len(csv_columns) >= 2 and len(original_headers) >= 2:
            # Create new dataframe with template headers
            df_csv_mapped = pd.DataFrame()
            df_csv_mapped[original_headers[0]] = df_csv.iloc[:, 0]  # First CSV column -> Name
            df_csv_mapped[original_headers[1]] = df_csv.iloc[:, 1]  # Second CSV column -> Email

            # Replace blank/empty Name values with 'Colleague'
            name_col = original_headers[0]
            blank_count = df_csv_mapped[name_col].isna().sum() + (df_csv_mapped[name_col] == '').sum()
            if blank_count > 0:
                df_csv_mapped[name_col] = df_csv_mapped[name_col].fillna('Colleague')
                df_csv_mapped[name_col] = df_csv_mapped[name_col].replace('', 'Colleague')
                df_csv_mapped[name_col] = df_csv_mapped[name_col].replace(r'^\s*$', 'Colleague', regex=True)
                print(f"Replaced {blank_count} blank '{name_col}' values with 'Colleague'")

            print(f"Mapped CSV columns [{csv_columns[0]}, {csv_columns[1]}] to template headers {original_headers[:2]}")
        else:
            print("Warning: Column mapping issue, using CSV as-is")
            df_csv_mapped = df_csv
        
        # Calculate number of workbooks needed
        total_rows = len(df_csv_mapped)
        num_workbooks = (total_rows + max_rows_per_workbook - 1) // max_rows_per_workbook
        print(f"Will create {num_workbooks} workbook(s) for {total_rows} rows")
        
        # Get the directory and base name of the template file
        template_dir = os.path.dirname(excel_template_path)
        template_name = os.path.splitext(os.path.basename(excel_template_path))[0]

        # Get current date and time in format: YYYYMMDD_HHMMSS
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        created_files = []

        for workbook_num in range(num_workbooks):
            # Calculate row range for this workbook
            start_row = workbook_num * max_rows_per_workbook
            end_row = min(start_row + max_rows_per_workbook, total_rows)

            # Create filename for this workbook with date and time
            output_file = os.path.join(template_dir, f"{template_name}_{timestamp}_{workbook_num + 1}.xlsx")
            
            print(f"\nCreating workbook {workbook_num + 1}: {output_file}")
            print(f"Rows {start_row + 1} to {end_row} (total: {end_row - start_row} rows)")
            
            # Get data slice for this workbook
            data_slice = df_csv_mapped.iloc[start_row:end_row]

            # Create the workbook using pandas only (preserves original formatting)
            with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:

                # Write data to Worksheet 1
                data_slice.to_excel(writer, sheet_name='Worksheet 1', index=False)
                print(f"  Written {len(data_slice)} rows to 'Worksheet 1'")

                # Copy other sheets from template
                for sheet_name, sheet_data in template_sheets.items():
                    if sheet_name != 'Worksheet 1':
                        sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"  Copied '{sheet_name}' from template")

                # Create empty Worksheet 3 with same headers if it doesn't exist
                if 'Worksheet 3' not in template_sheets:
                    empty_df = pd.DataFrame(columns=original_headers[:2])
                    empty_df.to_excel(writer, sheet_name='Worksheet 3', index=False)
                    print(f"  Created empty 'Worksheet 3'")

                # Create empty Worksheet if it doesn't exist
                if 'Worksheet' not in template_sheets:
                    empty_df_blank = pd.DataFrame()
                    empty_df_blank.to_excel(writer, sheet_name='Worksheet', index=False)
                    print(f"  Created empty 'Worksheet'")

            created_files.append(output_file)
            print(f"  Successfully created: {output_file}")

        print(f"\nSuccessfully created {len(created_files)} workbook(s):")
        for i, file_path in enumerate(created_files, 1):
            print(f"  {i}. {file_path}")

        return True
        
    except Exception as e:
        print(f"Error processing files: {e}")
        import traceback
        traceback.print_exc()
        return False

def process_directory_pattern(base_path="I:\\My Drive", pattern="*-20*"):
    """
    Process directories matching the pattern (e.g., CardioSummit-2026, NeoCare-2026, etc.)
    Looks for CSV files in the 'output' subfolder and Excel templates in the 'sit' subfolder.

    Args:
        base_path (str): Base directory path
        pattern (str): Directory pattern to match
    """
    try:
        # Find directories matching the pattern
        search_pattern = os.path.join(base_path, pattern)
        matching_dirs = glob.glob(search_pattern)

        print(f"Found {len(matching_dirs)} directories matching pattern '{pattern}':")
        for dir_path in matching_dirs:
            print(f"  - {dir_path}")

        if not matching_dirs:
            print(f"No directories found matching pattern '{pattern}' in '{base_path}'")
            return

        for dir_path in matching_dirs:
            print(f"\n{'='*60}")
            print(f"Processing directory: {os.path.basename(dir_path)}")
            print(f"{'='*60}")

            # Check for sit folder
            sit_path = os.path.join(dir_path, "sit")
            if not os.path.exists(sit_path):
                print(f"  ⚠️  'sit' folder not found in {os.path.basename(dir_path)}, skipping...")
                continue

            # Clean up timestamped files from sit folder
            print(f"  Cleaning up sit folder...")
            cleanup_sit_folder(sit_path)

            # Find Excel template in sit folder
            excel_files = glob.glob(os.path.join(sit_path, "*.xlsx"))
            if not excel_files:
                print(f"  ⚠️  No Excel template found in sit folder, skipping...")
                continue

            # Get directory name for pattern matching
            dir_name = os.path.basename(dir_path)

            # Try to find Excel file matching the directory name pattern
            excel_file = None
            matching_files = []
            for ef in excel_files:
                excel_basename = os.path.splitext(os.path.basename(ef))[0].lower()
                if dir_name.lower() in excel_basename or excel_basename in dir_name.lower():
                    matching_files.append(ef)

            if matching_files:
                excel_file = matching_files[0]
            elif len(excel_files) == 1:
                excel_file = excel_files[0]
            else:
                excel_file = excel_files[0]  # Use first one if multiple found

            print(f"  ✓ Found template: {os.path.basename(excel_file)}")

            # Look for CSV files in output subfolder
            output_path = os.path.join(dir_path, "output")
            if not os.path.exists(output_path):
                print(f"  ⚠️  'output' folder not found, skipping...")
                continue

            # Create 'sent' subfolder if it doesn't exist
            sent_path = os.path.join(output_path, "sent")
            os.makedirs(sent_path, exist_ok=True)

            csv_files = glob.glob(os.path.join(output_path, "*.csv"))

            if csv_files:
                # Sort to get consistent ordering (alphabetical)
                csv_files.sort()

                # Process only the first CSV file
                csv_file = csv_files[0]
                print(f"  ✓ Found {len(csv_files)} CSV file(s) in output folder")
                print(f"  → Processing first file: {os.path.basename(csv_file)}")

                success = copy_csv_to_multiple_workbooks(csv_file, excel_file)

                if success:
                    # Move processed CSV to 'sent' folder
                    sent_file_path = os.path.join(sent_path, os.path.basename(csv_file))
                    try:
                        shutil.move(csv_file, sent_file_path)
                        print(f"  ✓ Successfully processed and moved to sent folder: {os.path.basename(csv_file)}")
                    except Exception as move_error:
                        print(f"  ⚠️  Processed successfully but failed to move file: {move_error}")
                else:
                    print(f"  ✗ Failed to process {os.path.basename(csv_file)}")
            else:
                print(f"  ⚠️  No CSV files found in output folder")

    except Exception as e:
        print(f"Error processing directories: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("=" * 60)
    print("CSV to Multiple Excel Workbooks Converter")
    print("=" * 60)
    print()

    # Ask user for mode selection
    print("Select mode:")
    print("1. Process single CSV file with template")
    print("2. Auto-discover directories (pattern-based)")
    print()

    mode = input("Enter your choice (1 or 2): ").strip()

    if mode == "2":
        # Auto-discover mode
        print()
        print("Auto-Discovery Mode")
        print("-" * 60)

        base_path = input("Enter base path [default: I:\\My Drive]: ").strip()
        if not base_path:
            base_path = "I:\\My Drive"

        pattern = input("Enter directory pattern [default: *-2026]: ").strip()
        if not pattern:
            pattern = "*-2026"

        print()
        print(f"Auto-discovering directories in '{base_path}' with pattern '{pattern}'...")
        process_directory_pattern(base_path, pattern)

    elif mode == "1":
        # Single file mode
        print()
        print("Single File Processing Mode")
        print("-" * 60)

        csv_input = input("Enter path to folder containing CSV files: ").strip()
        if not csv_input:
            print("Error: Folder path is required")
            return

        # Remove quotes if user pasted path with quotes
        csv_input = csv_input.strip('"').strip("'")

        if not os.path.exists(csv_input):
            print(f"Error: Path not found: {csv_input}")
            return

        # Check if input is a directory
        if not os.path.isdir(csv_input):
            print(f"Error: Path is not a directory: {csv_input}")
            return

        # Find CSV files in the folder
        csv_files = glob.glob(os.path.join(csv_input, "*.csv"))

        if not csv_files:
            print(f"Error: No CSV files found in {csv_input}")
            return

        # Sort and pick the first CSV file
        csv_files.sort()
        csv_file = csv_files[0]

        print(f"\n✓ Found {len(csv_files)} CSV file(s) in folder")
        print(f"→ Processing first file: {os.path.basename(csv_file)}")

        # Auto-discover Excel template in sit folder
        csv_dir = os.path.dirname(csv_file)

        # Try to find sit folder in current directory or parent directory
        sit_path = os.path.join(csv_dir, "sit")
        search_dir = csv_dir

        if not os.path.exists(sit_path):
            # Try parent directory
            parent_dir = os.path.dirname(csv_dir)
            sit_path = os.path.join(parent_dir, "sit")
            search_dir = parent_dir

        # Get the directory name for pattern matching
        csv_parent_name = os.path.basename(search_dir)  # e.g., "CardioSummit-2026"

        excel_file = None
        if os.path.exists(sit_path):
            # Clean up timestamped files from sit folder
            print("\nCleaning up sit folder...")
            cleanup_sit_folder(sit_path)
            # Find Excel files in sit folder
            excel_files = glob.glob(os.path.join(sit_path, "*.xlsx"))

            if excel_files:
                # Try to find Excel file matching the directory name pattern
                matching_files = []
                for ef in excel_files:
                    excel_basename = os.path.splitext(os.path.basename(ef))[0].lower()
                    if csv_parent_name.lower() in excel_basename or excel_basename in csv_parent_name.lower():
                        matching_files.append(ef)

                if matching_files:
                    excel_file = matching_files[0]
                    print(f"\n✓ Auto-discovered template: {excel_file}")
                elif len(excel_files) == 1:
                    # Only one Excel file, use it
                    excel_file = excel_files[0]
                    print(f"\n✓ Auto-discovered template: {excel_file}")
                else:
                    # Multiple files but no pattern match, let user choose
                    print(f"\nFound {len(excel_files)} Excel files in sit folder:")
                    for i, ef in enumerate(excel_files, 1):
                        print(f"  {i}. {os.path.basename(ef)}")
                    print(f"  {len(excel_files) + 1}. Enter custom path")

                    choice = input(f"\nSelect template (1-{len(excel_files) + 1}): ").strip()
                    try:
                        choice_num = int(choice)
                        if 1 <= choice_num <= len(excel_files):
                            excel_file = excel_files[choice_num - 1]
                            print(f"Selected: {os.path.basename(excel_file)}")
                    except ValueError:
                        pass

        # If no auto-discovered file, ask for manual input
        if not excel_file:
            print("\nNo template auto-discovered in sit folder.")
            excel_file = input("Enter path to template Excel file: ").strip()
            if not excel_file:
                print("Error: Excel template file path is required")
                return

            # Remove quotes if user pasted path with quotes
            excel_file = excel_file.strip('"').strip("'")

            if not os.path.exists(excel_file):
                print(f"Error: Excel template file not found: {excel_file}")
                return

        max_rows_input = input("Enter maximum rows per workbook [default: 10000]: ").strip()
        if max_rows_input:
            try:
                max_rows = int(max_rows_input)
            except ValueError:
                print("Invalid number, using default: 10000")
                max_rows = 10000
        else:
            max_rows = 10000

        print()
        print("Processing single CSV to Excel workbooks...")
        success = copy_csv_to_multiple_workbooks(csv_file, excel_file, max_rows)

        if success:
            # Move processed CSV to 'sent' folder
            csv_dir = os.path.dirname(csv_file)
            sent_path = os.path.join(csv_dir, "sent")

            # Create 'sent' subfolder if it doesn't exist
            os.makedirs(sent_path, exist_ok=True)

            sent_file_path = os.path.join(sent_path, os.path.basename(csv_file))
            try:
                shutil.move(csv_file, sent_file_path)
                print()
                print("=" * 60)
                print("Processing completed successfully!")
                print(f"CSV file moved to: {sent_file_path}")
                print("=" * 60)
            except Exception as move_error:
                print()
                print("=" * 60)
                print("Processing completed successfully!")
                print(f"⚠️  Warning: Could not move CSV file to sent folder: {move_error}")
                print("=" * 60)
        else:
            print()
            print("=" * 60)
            print("Processing failed!")
            print("=" * 60)
    else:
        print("Invalid choice. Please run the script again and select 1 or 2.")

if __name__ == "__main__":
    main()
