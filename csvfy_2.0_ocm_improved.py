#!/usr/bin/env python3
"""
OCM (Organizing Committee Member) CSV Processing Script

This script processes CSV files for conference data, extracting conference segment names,
filtering data, and preparing output files for OCM (Organizing Committee Member) campaigns.
"""

import os
import glob
import pandas as pd
import numpy as np
import re
import random
import warnings
import logging

# Import the rich progress module
from rich_progress import print_status, create_progress_bar, RICH_AVAILABLE

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Suppress pandas warnings - compatible with both old and new pandas versions
try:
    # Try the new location first (pandas >= 1.5.0)
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        # Try the old location (pandas < 1.5.0)
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        # If neither works, just ignore pandas warnings in general
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

# Base paths
MASTER_DIR = r"H:/Master Bounces and Unsubs"
POSTPANEL_DIR = os.path.join(MASTER_DIR, "Postpanel Unsubs")
MASTER_BOUNCES_DIR = os.path.join(MASTER_DIR, "Master Bounces and Unsubs")
REPLIED_DIR = os.path.join(MASTER_DIR, "Replied Ext/Prev_Rep_bounces_csv")


def extract_conference_name(path: str) -> str:
    """
    Extract conference segment name from a path using regex.

    Args:
        path: Path string to extract from

    Returns:
        Conference name or prompts for manual input if not found
    """
    # Define a regex pattern to match the desired segment, including spaces
    pattern = r"\\([\w\s-]+\d{4})\\?"

    # Search for the pattern in the path
    match = re.search(pattern, path)

    if match:
        csn = match.group(1)
        logger.info(f"Conference Segment Name (CSN): {csn}")
        print_status(f"Found conference segment: {csn}", "success")
        return csn
    else:
        logger.warning("Desired segment not found in path.")
        print_status(f"Desired segment not found in path: {path}", "warning")

        # Prompt for manual input
        print_status("Please enter the conference segment name manually", "info")
        csn = input("Conference segment name (e.g., 'Conference 2023'): ").strip()

        if csn:
            logger.info(f"Using manually entered segment: {csn}")
            print_status(f"Using manually entered segment: {csn}", "success")
            return csn
        else:
            logger.error("No segment name provided.")
            print_status("No segment name provided. Cannot proceed without a conference segment name.", "error")
            raise ValueError("No conference segment name provided")


def process_mw_unsubs() -> None:
    """Process MailWizz unsubscribers and save to CSV."""
    logger.info("Processing MailWizz unsubscribers...")

    try:
        # Process MW unsubscribers
        os.chdir(os.path.join(POSTPANEL_DIR, "mw"))

        # List all CSV files and concatenate them
        csv_files = glob.glob('*.csv')
        if not csv_files:
            logger.warning("No CSV files found in MW directory")
            return

        # Concatenate all CSV files
        df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip',
                                          usecols=['email', 'status', 'date_added'])
                              for f in csv_files], ignore_index=True)

        # Filter for unsubscribed status
        status = ["unsubscribed"]
        unsub_df = df_concat[df_concat['status'].isin(status)]

        # Remove duplicates
        unsub_df1 = unsub_df.drop_duplicates(subset='email')

        # Save intermediate file
        unsub_df1.to_csv('mw_list_unsubscriber.csv', encoding='utf-8-sig', index=False)

        # Read back and rename columns
        unsub_df2 = pd.read_csv('mw_list_unsubscriber.csv')
        unsub_df2.rename(columns={'email': 'Email', 'status': 'Conference Name',
                                 'date_added': 'DateTime Info'}, inplace=True)

        # Replace values
        unsub_dfg = unsub_df2.apply(lambda x: x.str.replace('unsubscribed', 'Global Unsubscriber')
                                   if x.name == 'Conference Name' else x)

        # Save to Postpanel Unsubs directory
        os.chdir(POSTPANEL_DIR)
        unsub_dfg.to_csv('mw_unsubscribers.csv', index=False)
        logger.info("MailWizz unsubscribers processed successfully")

    except Exception as e:
        logger.error(f"Error processing MW unsubscribers: {str(e)}")


def process_port1_unsubs() -> None:
    """Process Port1 unsubscribers and save to CSV."""
    logger.info("Processing Port1 unsubscribers...")

    try:
        os.chdir(os.path.join(POSTPANEL_DIR, "port1"))

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            logger.warning("No CSV files found in Port1 directory")
            return

        # Concatenate all CSV files
        unsub_mgport = pd.concat([pd.read_csv(f, on_bad_lines='skip', encoding='latin')
                                 for f in csv_files], ignore_index=True)

        # Rename columns
        unsub_mgport.rename(columns={'Email ID': 'Email', 'To': 'Conference Name',
                                    'Date Info': 'DateTime Info'}, inplace=True)

        # Replace values using regex
        unsub_mgportg = unsub_mgport.replace(to_replace=r".*\(.+?\)", value='Global Unsubscriber', regex=True)
        unsub_mgportg['Conference Name'] = unsub_mgportg['Conference Name'].fillna('Global Unsubscriber')

        # Remove duplicates
        unsub_mgportg.drop_duplicates(subset='Email', inplace=True)

        # Save to Postpanel Unsubs directory
        os.chdir(POSTPANEL_DIR)
        unsub_mgportg.to_csv('unsubcriber_sheet.csv', mode='w+', index=False)
        logger.info("Port1 unsubscribers processed successfully")

    except Exception as e:
        logger.error(f"Error processing Port1 unsubscribers: {str(e)}")


def process_global_unsubs(sp_filter: str) -> None:
    """
    Process global unsubscribers and save to CSV.

    Args:
        sp_filter: Conference segment name for filtering
    """
    logger.info("Processing global unsubscribers...")

    try:
        os.chdir(POSTPANEL_DIR)

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            logger.warning("No CSV files found in Postpanel Unsubs directory")
            return

        # Concatenate all CSV files
        glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)

        # Clean data - apply string operations only to string columns
        for col in glob_unsubs.select_dtypes(include=['object']).columns:
            glob_unsubs[col] = glob_unsubs[col].str.replace(' - ', '-', regex=False)

        # Drop DateTime Info column if it exists
        if 'DateTime Info' in glob_unsubs.columns:
            glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)

        # Filter by conference name
        options = ['Global Unsubscriber', sp_filter]
        unsub_df3 = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]

        # Remove duplicates and convert emails to lowercase
        unsub_df3.drop_duplicates(subset='Email', inplace=True, ignore_index=True)
        unsub_df3.drop(['Conference Name'], axis=1, inplace=True)

        # Convert emails to lowercase
        unsub_df3['Email'] = unsub_df3['Email'].str.lower()

        # Save to Master Bounces and Unsubs directory
        output_path = os.path.join(MASTER_BOUNCES_DIR, "PP_Global_Unsubscribers.csv")
        unsub_df3.to_csv(output_path, index=False)
        logger.info(f"Global unsubscribers saved to {output_path}")

    except Exception as e:
        logger.error(f"Error processing global unsubscribers: {str(e)}")


def clean_name(name: str) -> str:
    """
    Clean name by removing text after the first comma.
    
    Args:
        name: Name string to clean
        
    Returns:
        Cleaned name string
    """
    return str(name).split(',')[0].strip()


def process_conference_data(path: str, csn: str) -> pd.DataFrame:
    """
    Process conference data from CSV files.

    Args:
        path: Path to the directory containing CSV files
        csn: Conference segment name

    Returns:
        Processed DataFrame
    """
    logger.info(f"Processing conference data for {csn}...")

    try:
        os.chdir(path)

        # Create processing directory
        process_dir = os.path.join(path, "processing")
        os.makedirs(process_dir, exist_ok=True)

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            logger.warning(f"No CSV files found in {path}")
            print_status(f"No CSV files found in {path}", "warning")
            return pd.DataFrame()

        # Create a progress bar for reading CSV files
        print_status(f"Reading {len(csv_files)} CSV files...", "info")
        progress, update_progress = create_progress_bar(len(csv_files), "Reading CSV files", "green")

        try:
            # Read each CSV file with progress bar
            dfs = []
            for i, f in enumerate(csv_files):
                update_progress(0, f"Reading file {i+1}/{len(csv_files)}: {f}")
                df = pd.read_csv(f, low_memory=False, encoding='utf-8-sig', on_bad_lines='skip')
                dfs.append(df)
                update_progress(1)

            # Close the progress bar if using Rich
            if RICH_AVAILABLE:
                progress.stop()

            # Concatenate all dataframes
            print_status("Concatenating files...", "info")
            df_concat = pd.concat(dfs, ignore_index=True)
            print_status(f"Successfully read {len(df_concat)} records from {len(csv_files)} files", "success")

        except Exception as e:
            # Close the progress bar if using Rich
            if RICH_AVAILABLE:
                progress.stop()
            logger.error(f"Error reading CSV files: {str(e)}")
            print_status(f"Error reading CSV files: {str(e)}", "error")
            raise e

        # Standardize column names
        df_concat.rename(columns={'Author Name': 'Name', 'col1': 'Name',
                                 'col2': 'Article Title', 'email': 'Email',
                                 'name': 'Name'}, inplace=True)

        # Clean names by removing text after comma
        df_concat['Name'] = df_concat['Name'].apply(clean_name)

        # Remove rows with empty Email
        df_concat.dropna(subset='Email', inplace=True)

        # Save unfiltered data
        unfiltered_path = os.path.join(process_dir, f"{csn}-merged_unfiltered.csv")
        df_concat.to_csv(unfiltered_path, mode='w+', encoding='utf-8-sig', index=False)

        # Load master files for filtering
        df_hb = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "Master_Hardbounces.csv"),
                           on_bad_lines='skip')
        df_unsubs = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "Master_Unsubscribes.csv"))
        df_rep = pd.read_csv(os.path.join(REPLIED_DIR, f"{csn}_replied_bouncers.csv"))
        df_pp_gl_unsubs = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "PP_Global_Unsubscribers.csv"))

        # Combine unsubscribers and convert emails to lowercase
        df_concat_unsubs = pd.concat([df_unsubs, df_pp_gl_unsubs])
        df_concat_unsubs['Email'] = df_concat_unsubs['Email'].str.lower()

        # Read back the unfiltered data
        df_concat = pd.read_csv(unfiltered_path, low_memory=False)

        # Filter out hard bounces, unsubscribers, and replied
        df_hb_filtered = df_concat[~(df_concat.Email.isin(df_hb.Email))]
        df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered.Email.isin(df_concat_unsubs.Email))]
        df = df_unsubs_filtered[~(df_unsubs_filtered.Email.isin(df_rep.Email))]

        # Remove Article Title column if it exists
        if 'Article Title' not in df.columns:
            df['Article Title'] = 0
        df.drop(['Article Title'], axis=1, inplace=True)

        # Save deduped data
        deduped_path = os.path.join(process_dir, f"{csn}-merged_deduped.csv")
        df.to_csv(deduped_path, mode='w+', encoding='utf-8-sig', index=False)

        # Extract only Name and Email columns
        df = pd.read_csv(deduped_path, low_memory=False, usecols=["Name", "Email"])

        # Clean data
        df1 = df.replace(r'^\s*$', np.nan, regex=True)
        df2 = df1.fillna('Colleague')

        # Remove duplicates
        result = df2.drop_duplicates(subset='Email')

        # Save processed data
        process_path = os.path.join(process_dir, f"{csn}-merged.csv")
        result.to_csv(process_path, mode='w+', encoding='utf-8-sig', index=False)

        # Read back the processed data
        df = pd.read_csv(process_path)
        logger.info(f"Processed {len(df)} records for {csn}")

        # Clean up temporary files
        os.remove(deduped_path)
        os.remove(unfiltered_path)
        os.remove(process_path)
        os.rmdir(process_dir)

        return df

    except Exception as e:
        logger.error(f"Error processing conference data: {str(e)}")
        return pd.DataFrame()


def generate_subject_lines(df: pd.DataFrame, csn: str) -> pd.DataFrame:
    """
    Generate random subject lines for each record.

    Args:
        df: DataFrame with records
        csn: Conference segment name

    Returns:
        DataFrame with added subject lines
    """
    logger.info("Generating subject lines...")

    # List of subject line templates for OCM
    subj_list = [
        "Join the Inner Circle: Organizing Committee Member Invitation for a Game-Changing [CCT_CSNAME] Conference",
        "Make a Difference: Become a Committee Member for a Groundbreaking [CCT_CSNAME] Conference",
        "Calling All Experts! Join our Organizing Committee for a Leading [CCT_CSNAME] Conference",
        "Join the Organizing Committee for our Cutting-Edge [CCT_CSNAME] Event",
        "Contribute to the Success of a Premier [CCT_CSNAME] Conference as a Committee Member",
        "Your Expertise Needed: Join the Committee for a Game-Changing [CCT_CSNAME] gathering",
        "Join the Brainpower Behind our Revolutionary [CCT_CSNAME] Conference!",
        "Be Part of Our Organizing Committee for a Prestigious [CCT_CSNAME]!",
        "Your Expertise Needed: Invitation to Join the Organizing Committee for a Premier [CCT_CSNAME] Congress",
        "Be a Key Player: Join the Organizing Committee for a Dynamic [CCT_CSNAME] Congress",
        "Join us as a Committee Member for our Prestigious [CCT_CSNAME]!",
        "Become an Organizing Committee Member for an Exciting [CCT_CSNAME]!",
        "Shape the Future: Join the Organizing Committee for an Innovative Scientific Conference",
        "Help us Engineer Success as an Organizing Committee Member for a Scientific Conference"
    ]

    # Replace placeholder with actual conference name
    formatted_subj_list = [subject.replace("[CCT_CSNAME]", csn) for subject in subj_list]

    # Create a progress bar for generating subject lines
    print_status(f"Generating subject lines for {len(df)} records...", "info")

    # For large datasets, show a progress bar
    if len(df) > 1000:
        progress, update_progress = create_progress_bar(1, "Generating subject lines", "purple")

        try:
            # Assign random subject lines
            update_progress(0, f"Assigning random subject lines to {len(df)} records")
            df["Subject"] = pd.Series(
                random.choices(formatted_subj_list, k=len(df)),
                index=df.index
            )
            update_progress(1)

            # Close the progress bar if using Rich
            if RICH_AVAILABLE:
                progress.stop()

        except Exception as e:
            # Close the progress bar if using Rich
            if RICH_AVAILABLE:
                progress.stop()
            logger.error(f"Error generating subject lines: {str(e)}")
            print_status(f"Error generating subject lines: {str(e)}", "error")
            raise e
    else:
        # For smaller datasets, just assign without progress bar
        df["Subject"] = pd.Series(
            random.choices(formatted_subj_list, k=len(df)),
            index=df.index
        )

    return df


def save_output_files(df: pd.DataFrame, csn: str, n_temps: int = 1, include_subject: bool = True) -> None:
    """
    Save output files, splitting the data if needed.

    Args:
        df: DataFrame with records
        csn: Conference segment name
        n_temps: Number of templates/chunks to split into
        include_subject: Whether to include the Subject column in the output
    """
    logger.info("Saving output files...")
    print_status("Saving output files...", "info")

    try:
        # Create result directory
        result_dir = os.path.join(os.getcwd(), "result")
        os.makedirs(result_dir, exist_ok=True)

        # Remove Subject column if not needed
        if not include_subject and 'Subject' in df.columns:
            print_status("Removing Subject column from output...", "info")
            df = df.drop(columns=['Subject'])

        # Get the number of rows in the dataframe
        n_rows = len(df)

        # Calculate the size of each chunk
        chunk_size = n_rows // n_temps

        # Create a progress bar for the file saving process
        progress, update_progress = create_progress_bar(n_temps, "Saving files", "cyan")

        try:
            # Split the dataframe into chunks and save them as separate csv files
            for i in range(n_temps):
                # Update progress description
                update_progress(0, f"Saving file {i+1}/{n_temps}")

                start = i * chunk_size
                end = (i + 1) * chunk_size if i < n_temps - 1 else n_rows
                chunk = df[start:end]
                output_path = os.path.join(result_dir, f"{csn}-{i+1}_OCM.csv")
                chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                # Update progress
                update_progress(1)
                logger.info(f"Saved {len(chunk)} records to {output_path}")
                print_status(f"Saved {len(chunk)} records to {output_path}", "success")

            # Close the progress bar if using Rich
            if RICH_AVAILABLE:
                progress.stop()

            # Show what columns were included
            columns_info = ", ".join(df.columns.tolist())
            print_status(f"Columns included: {columns_info}", "info")
            logger.info(f"Successfully saved {n_rows} records to {n_temps} files")
            print_status(f"Successfully saved {n_rows} records to {n_temps} files", "header")

        except Exception as e:
            # Close the progress bar if using Rich
            if RICH_AVAILABLE:
                progress.stop()
            raise e

    except Exception as e:
        logger.error(f"Error saving output files: {str(e)}")
        print_status(f"Error saving output files: {str(e)}", "error")


def get_yes_no_input(prompt: str) -> bool:
    """
    Get a yes/no input from the user.

    Args:
        prompt: The prompt to display to the user

    Returns:
        True for yes, False for no
    """
    while True:
        response = input(f"{prompt} (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        else:
            print_status("Please enter 'y' or 'n'.", "warning")


def main():
    """Main function to run the script."""
    try:
        # Print header
        print_status("OCM CSV Processing Script", "header")

        # Get input path
        path = input("Loc: ")

        # Extract conference segment name
        print_status("Extracting conference segment name...", "info")
        try:
            csn = extract_conference_name(path)
        except ValueError as e:
            print_status(f"Error: {str(e)}", "error")
            logger.error(f"Failed to get conference segment name: {str(e)}")
            return

        # Process unsubscribers
        print_status("Processing unsubscribers...", "info")
        process_mw_unsubs()
        process_port1_unsubs()
        process_global_unsubs(csn)
        print_status("Unsubscribers processed successfully", "success")

        # Process conference data
        print_status(f"Processing conference data for {csn}...", "info")
        df = process_conference_data(path, csn)

        if not df.empty:
            print_status(f"Found {len(df)} records to process", "success")

            # Default values - bypass user input
            include_subject = True  # Default: 'y' - include subject lines
            n_temps = 1            # Default: 1 template/chunk

            print_status("Using default settings:", "info")
            print_status("- Include subject lines: Yes", "info")
            print_status("- Number of templates/chunks: 1", "info")

            # Generate subject lines (always included with defaults)
            print_status("Generating subject lines...", "info")
            df = generate_subject_lines(df, csn)
            print_status("Subject lines generated successfully", "success")

            # Save output files
            save_output_files(df, csn, n_temps, include_subject)

            logger.info(f"Total records processed: {len(df)}")
            print_status(f"Total records processed: {len(df)}", "success")
        else:
            print_status("No records found to process", "warning")

        logger.info("Completed!")
        print_status("Process completed successfully!", "header")

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        logger.error("Process terminated with errors.")
        print_status(f"Error: {str(e)}", "error")
        print_status("Process terminated with errors.", "error")


if __name__ == "__main__":
    main()

