import pandas as pd
import requests
from rich.console import Console
from rich.table import Table
import datetime
import os

console = Console()

def fetch_dbms_stats():
    """
    Fetch DBMS statistics tables and save them to Excel files
    """
    console.print("[bold green]DBMS Statistics Extractor[/bold green]")
    console.print("=" * 50)
    console.print(f"[dim]Started at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}[/dim]")
    console.print()

    # URL of the page containing the tables
    url = 'https://e1.dbms.org.in/dbms/stats/'

    try:
        console.print(f"[cyan]Fetching data from:[/cyan] {url}")

        # Send a GET request to fetch the page content
        response = requests.get(url)
        response.raise_for_status()

        # Use Pandas to read the HTML content and extract tables
        tables = pd.read_html(response.text)
        console.print(f"[green]✓ Found {len(tables)} tables[/green]")

        # Define table configurations
        table_configs = [
            {"index": 1, "filename": "magnus_conferences_clusters.xlsx", "name": "Magnus Conferences Clusters"},
            {"index": 2, "filename": "mathews_clusters.xlsx", "name": "Mathews Clusters"},
            {"index": 5, "filename": "magnus_sources_clusters.xlsx", "name": "Magnus Sources Clusters"}
        ]

        # Process each table
        successful_saves = 0
        for config in table_configs:
            try:
                if config["index"] >= len(tables):
                    console.print(f"[red]✗ Table index {config['index']} out of range for {config['name']} (only {len(tables)} tables found)[/red]")
                    continue

                table = tables[config["index"]]

                # Check if table is empty
                if table.empty:
                    console.print(f"[yellow]⚠ Table {config['index']} is empty for {config['name']}[/yellow]")
                    continue

                # Save to Excel with UTF-8 encoding
                table.to_excel(config["filename"], index=False, engine='openpyxl')

                # Verify file was created
                if os.path.exists(config["filename"]):
                    file_size = os.path.getsize(config["filename"])
                    console.print(f"[green]✓ Saved {config['name']} to {config['filename']} ({file_size:,} bytes)[/green]")
                    console.print(f"[yellow]Rows: {len(table)}, Columns: {len(table.columns)}[/yellow]")
                    successful_saves += 1

                    # Display table preview
                    console.print(f"\n[cyan]{config['name']} Preview:[/cyan]")
                    print(table.head())
                    print()
                else:
                    console.print(f"[red]✗ Failed to create file {config['filename']}[/red]")

            except IndexError:
                console.print(f"[red]✗ Table {config['index']} not found for {config['name']}[/red]")
            except Exception as e:
                console.print(f"[red]✗ Error processing {config['name']}: {str(e)}[/red]")

        console.print(f"[bold green]✓ Successfully saved {successful_saves} out of {len(table_configs)} files[/bold green]")

        console.print(f"[dim]Completed at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}[/dim]")
        console.print("[bold green]✓ DBMS statistics extraction completed![/bold green]")

    except requests.RequestException as e:
        console.print(f"[red]✗ Error fetching data: {str(e)}[/red]")
    except Exception as e:
        console.print(f"[red]✗ Unexpected error: {str(e)}[/red]")

if __name__ == "__main__":
    fetch_dbms_stats()
