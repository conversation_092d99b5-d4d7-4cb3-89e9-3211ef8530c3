# Batch Processing Directory List
# Lines starting with # are comments and will be ignored
# Each line should contain a full path to a directory containing CSV/Excel files
# Empty lines are also ignored

# Example directory paths (replace with your actual paths):
# C:\Users\<USER>\OneDrive\My Files\data\folder1
# C:\Users\<USER>\OneDrive\My Files\data\folder2
# C:\Users\<USER>\OneDrive\My Files\data\folder3

# Instructions:
# 1. Replace the example paths above with your actual directory paths
# 2. Each directory should contain CSV files (for convert_csv_encoding.py) 
#    or CSV/Excel files (for professional_data_merger.py)
# 3. Save this file and provide its path when prompted by the scripts
# 4. The scripts will process each directory listed here

# Sample format:
# /path/to/directory1
# /path/to/directory2
# /path/to/directory3
