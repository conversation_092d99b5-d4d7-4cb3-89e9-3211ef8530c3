#!/usr/bin/env python3
"""
Email Filter - Global Unsubscribers
Filters out emails that are present in the Global_Unsubscriber.csv file from all CSV/Excel files in a directory.
"""

import os
import glob
import pandas as pd
import sys
import chardet
import re
from datetime import datetime

# Import rich_progress for gradient progress bars
try:
    import rich_progress
    HAS_RICH = True
except ImportError:
    HAS_RICH = False
    print("Note: rich_progress not available. Using basic progress indicators.")

def print_status(message, status_type="info"):
    """Print status message with or without rich formatting."""
    if HAS_RICH:
        rich_progress.print_status(message, status_type)
    else:
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

def print_header(title):
    """Print a header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    if HAS_RICH:
        rich_progress.print_status(f"\n{title}", "header")
        rich_progress.print_status(timestamp, "info")
        rich_progress.print_status("=" * 60, "info")
    else:
        print(f"\n{'='*60}")
        print(f"{title}")
        print(f"{timestamp}")
        print(f"{'='*60}")

def print_section(title):
    """Print a section header."""
    if HAS_RICH:
        rich_progress.print_status(f"\n>> {title}", "info")
        rich_progress.print_status("-" * (len(title) + 4), "info")
    else:
        print(f"\n>> {title}")
        print("-" * (len(title) + 4))

def normalize_whitespace(text):
    """
    Normalize whitespace in text by:
    1. Replacing multiple spaces with a single space
    2. Removing leading and trailing whitespace
    3. Handling other whitespace characters (tabs, newlines, etc.)
    """
    if not isinstance(text, str):
        return text
    
    # Replace all whitespace characters with a single space
    normalized = re.sub(r'\s+', ' ', text)
    # Remove leading and trailing whitespace
    normalized = normalized.strip()
    return normalized

def clean_name(name: str) -> str:
    """
    Clean name by removing text after the first comma and normalizing whitespace.
    """
    if pd.isna(name):
        return name
    return normalize_whitespace(str(name).split(',')[0])

def load_global_unsubscribers():
    """
    Load the Global_Unsubscriber.csv file and return a set of lowercase emails.
    """
    global_unsubs_path = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\postpanel\merged\separated\Global_Unsubscriber.csv"
    
    try:
        print_status(f"Loading Global Unsubscribers from: {global_unsubs_path}", "info")

        # Try UTF-8-SIG first, then fall back to other encodings
        try:
            df_global_unsubs = pd.read_csv(global_unsubs_path, encoding='utf-8-sig')
        except UnicodeDecodeError:
            try:
                df_global_unsubs = pd.read_csv(global_unsubs_path, encoding='utf-8')
            except UnicodeDecodeError:
                df_global_unsubs = pd.read_csv(global_unsubs_path, encoding='latin-1')

        # Check if Email column exists
        if 'Email' not in df_global_unsubs.columns:
            print_status("Error: 'Email' column not found in Global_Unsubscriber.csv", "error")
            return set()

        # Convert emails to lowercase and create a set for fast lookup
        global_emails = set(df_global_unsubs['Email'].astype(str).str.lower())
        print_status(f"Loaded {len(global_emails)} global unsubscriber emails", "success")
        return global_emails
        
    except FileNotFoundError:
        print_status(f"Error: Global_Unsubscriber.csv not found at {global_unsubs_path}", "error")
        return set()
    except Exception as e:
        print_status(f"Error loading Global_Unsubscriber.csv: {str(e)}", "error")
        return set()

def detect_email_column(df):
    """
    Detect the email column in the dataframe.
    """
    # Check for common email column names
    email_columns = ['Email', 'email', 'EMAIL', 'Email Address', 'email_address']
    
    for col in email_columns:
        if col in df.columns:
            return col
    
    # If no standard column found, look for columns containing email-like data
    for col in df.columns:
        if df[col].dtype == 'object':  # Only check string columns
            sample_values = df[col].dropna().head(10).astype(str)
            email_count = sum(1 for val in sample_values if '@' in val and '.' in val)
            if email_count >= 5:  # If at least half contain email-like patterns
                return col
    
    return None

def detect_name_column(df):
    """
    Detect the name column in the dataframe.
    """
    # Check for common name column names (based on user preferences)
    # Prioritize 'Name' first, then other variations
    name_columns = ['Name', 'Author Name', 'author name', 'name', 'full name', 'author', 'col1']

    for col in name_columns:
        if col in df.columns:
            return col

    return None

def process_file(file_path, global_emails, output_dir):
    """
    Process a single CSV or Excel file to filter out global unsubscribers.
    """
    try:
        print_status(f"Processing: {os.path.basename(file_path)}", "info")
        
        # Read file based on extension
        if file_path.lower().endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            # For CSV files, detect encoding
            with open(file_path, 'rb') as rawdata:
                result = chardet.detect(rawdata.read(100000))
            detected_encoding = result['encoding']
            confidence = result['confidence']
            
            if confidence > 0.7 and detected_encoding is not None:
                encoding_to_use = detected_encoding
            else:
                encoding_to_use = 'utf-8'
            
            df = pd.read_csv(file_path, encoding=encoding_to_use, on_bad_lines='skip', low_memory=False)
        
        original_count = len(df)
        
        # Detect email column
        email_col = detect_email_column(df)
        if not email_col:
            print_status(f"  Warning: No email column found in {os.path.basename(file_path)}. Skipping.", "warning")
            return None
        
        print_status(f"  Using email column: '{email_col}'", "info")
        
        # Detect name column
        name_col = detect_name_column(df)
        if name_col:
            print_status(f"  Using name column: '{name_col}'", "info")
            # Clean names
            df[name_col] = df[name_col].apply(clean_name)
            # Rename to standard 'Name' if it's not already
            if name_col != 'Name':
                df.rename(columns={name_col: 'Name'}, inplace=True)
        
        # Rename email column to standard 'Email' if it's not already
        if email_col != 'Email':
            df.rename(columns={email_col: 'Email'}, inplace=True)
        
        # Filter out global unsubscribers
        df['Email_lower'] = df['Email'].astype(str).str.lower()
        filtered_df = df[~df['Email_lower'].isin(global_emails)].copy()
        filtered_df.drop(columns=['Email_lower'], inplace=True)
        
        filtered_count = len(filtered_df)
        removed_count = original_count - filtered_count
        
        # Create output filename
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_filename = f"{base_name}_filtered.csv"
        output_path = os.path.join(output_dir, output_filename)
        
        # Save filtered data
        filtered_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        print_status(f"  ✓ Filtered: {removed_count:,} emails removed, {filtered_count:,} emails remaining", "success")
        
        return {
            'original_count': original_count,
            'filtered_count': filtered_count,
            'removed_count': removed_count,
            'output_file': output_filename
        }
        
    except Exception as e:
        print_status(f"  Error processing {os.path.basename(file_path)}: {str(e)}", "error")
        return None

def main():
    """Main function."""
    print_header("Email Filter - Global Unsubscribers")
    
    # Get input directory
    if len(sys.argv) > 1:
        input_dir = sys.argv[1]
    else:
        input_dir = input("Enter the directory path containing files to filter: ").strip().strip('"\'')
    
    if not os.path.exists(input_dir):
        print_status(f"Error: Directory '{input_dir}' does not exist.", "error")
        return
    
    # Change to input directory
    original_dir = os.getcwd()
    os.chdir(input_dir)
    print_status(f"Working directory: {input_dir}", "info")
    
    # Load global unsubscribers
    print_section("Loading Global Unsubscribers")
    global_emails = load_global_unsubscribers()
    if not global_emails:
        print_status("No global unsubscribers loaded. Exiting.", "error")
        return
    
    # Find files to process
    print_section("Discovering Files")
    csv_files = glob.glob('*.csv')
    excel_files = glob.glob('*.xlsx') + glob.glob('*.xls')
    all_files = csv_files + excel_files
    
    if not all_files:
        print_status("No CSV or Excel files found in the directory.", "error")
        return
    
    print_status(f"Found {len(csv_files)} CSV files and {len(excel_files)} Excel files", "success")
    
    # Create output directory
    output_dir = os.path.join(input_dir, "filtered")
    os.makedirs(output_dir, exist_ok=True)
    print_status(f"Output directory: {output_dir}", "info")
    
    # Process files
    print_section("Processing Files")
    
    total_stats = {
        'files_processed': 0,
        'files_with_errors': 0,
        'total_original': 0,
        'total_filtered': 0,
        'total_removed': 0
    }
    
    for i, file_path in enumerate(all_files, 1):
        print_status(f"File {i}/{len(all_files)}", "info")
        result = process_file(file_path, global_emails, output_dir)
        
        if result:
            total_stats['files_processed'] += 1
            total_stats['total_original'] += result['original_count']
            total_stats['total_filtered'] += result['filtered_count']
            total_stats['total_removed'] += result['removed_count']
        else:
            total_stats['files_with_errors'] += 1
    
    # Print final results
    print_header("Processing Complete!")
    print_section("Final Statistics")
    print_status(f"Files processed successfully: {total_stats['files_processed']}", "success")
    print_status(f"Files with errors: {total_stats['files_with_errors']}", "info")
    print_status(f"Total original records: {total_stats['total_original']:,}", "info")
    print_status(f"Total filtered records: {total_stats['total_filtered']:,}", "success")
    print_status(f"Total emails removed: {total_stats['total_removed']:,}", "info")
    
    if total_stats['total_original'] > 0:
        percentage_removed = (total_stats['total_removed'] / total_stats['total_original']) * 100
        print_status(f"Percentage of emails filtered out: {percentage_removed:.2f}%", "info")
    
    print_status(f"Filtered files saved to: {output_dir}", "info")
    
    # Change back to original directory
    os.chdir(original_dir)

if __name__ == "__main__":
    main()
    print("\nPress Enter to exit...")
    input()
