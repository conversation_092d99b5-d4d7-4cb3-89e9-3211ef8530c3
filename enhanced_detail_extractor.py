"""
Enhanced Magnus Group Data Extractor
Extracts detailed information from the View/Edit forms including Abstract Type, Track, etc.
"""

from bs4 import BeautifulSoup
import json
import csv
import re
from datetime import datetime

class EnhancedMagnusExtractor:
    def __init__(self):
        self.detailed_fields = [
            'First Name', 'Last Name', 'Full Name', 'Email Address', 
            'Alternate Email Address', 'Country', 'Phone', 'Abstract Title',
            'Abstract Type', 'Track', 'University/Institute/Industry with Country'
        ]
    
    def extract_detailed_records(self, html_file):
        """Extract detailed information from the HTML file"""
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # Find all the detailed form sections
        # These are typically in modal dialogs or expandable sections
        detailed_records = []
        
        # Look for form groups that contain our target fields
        form_groups = soup.find_all('div', class_='form-group')
        
        # Group form fields by their parent containers
        record_containers = self.group_form_fields(form_groups)
        
        for container in record_containers:
            record = self.extract_record_from_container(container)
            if record and any(record.values()):  # Only add if record has some data
                detailed_records.append(record)
        
        return detailed_records
    
    def group_form_fields(self, form_groups):
        """Group form fields that belong to the same record"""
        containers = []
        current_container = []
        
        for group in form_groups:
            label = group.find('label')
            if label:
                label_text = label.get_text(strip=True)
                
                # If we find a "First Name" field, start a new container
                if label_text == 'First Name':
                    if current_container:
                        containers.append(current_container)
                    current_container = [group]
                elif label_text in self.detailed_fields:
                    current_container.append(group)
        
        # Add the last container
        if current_container:
            containers.append(current_container)
        
        return containers
    
    def extract_record_from_container(self, container):
        """Extract a single record from a container of form groups"""
        record = {}
        
        for form_group in container:
            label = form_group.find('label')
            if not label:
                continue
                
            label_text = label.get_text(strip=True)
            
            # Find the input field
            input_field = form_group.find('input')
            select_field = form_group.find('select')
            textarea_field = form_group.find('textarea')
            
            value = ""
            if input_field:
                value = input_field.get('value', '')
            elif select_field:
                selected_option = select_field.find('option', selected=True)
                if selected_option:
                    value = selected_option.get_text(strip=True)
            elif textarea_field:
                value = textarea_field.get_text(strip=True)
            
            if value and value.strip():
                record[label_text] = value.strip()
        
        return record
    
    def extract_basic_table_data(self, html_file):
        """Extract basic table data as before"""
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        tables = soup.find_all('table')
        
        if not tables:
            return []
        
        table = tables[0]  # Assume first table contains the main data
        rows = table.find_all('tr')
        
        if not rows:
            return []
        
        # Extract headers
        header_row = rows[0]
        headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
        
        # Extract basic data
        basic_records = []
        for i, row in enumerate(rows[1:], 1):
            cells = row.find_all(['td', 'th'])
            record = {'record_id': i}
            
            for j, cell in enumerate(cells):
                if j < len(headers):
                    text = cell.get_text(strip=True)
                    record[headers[j]] = text
            
            basic_records.append(record)
        
        return basic_records
    
    def merge_basic_and_detailed_data(self, basic_records, detailed_records):
        """Merge basic table data with detailed form data"""
        merged_records = []
        
        # Try to match records based on email or other identifiers
        for i, basic in enumerate(basic_records):
            merged = basic.copy()
            
            # Try to find matching detailed record
            if i < len(detailed_records):
                detailed = detailed_records[i]
                merged.update(detailed)
            
            merged_records.append(merged)
        
        return merged_records
    
    def save_enhanced_data(self, records, base_filename="enhanced_abstracts"):
        """Save the enhanced data"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save to JSON
        json_filename = f"{base_filename}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(records, f, indent=2, ensure_ascii=False)
        
        # Save to CSV
        csv_filename = f"{base_filename}_{timestamp}.csv"
        if records:
            # Get all possible fieldnames
            all_fields = set()
            for record in records:
                all_fields.update(record.keys())
            
            fieldnames = sorted(list(all_fields))
            
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(records)
        
        print(f"Enhanced data saved to:")
        print(f"  - {json_filename}")
        print(f"  - {csv_filename}")
        
        return json_filename, csv_filename

def main():
    print("=== ENHANCED MAGNUS GROUP DATA EXTRACTOR ===")
    
    # Look for HTML files
    html_files = ['abstracts_page_selenium.html', 'abstracts_manual.html', 'abstracts_data.html']
    
    found_file = None
    for filename in html_files:
        try:
            with open(filename, 'r'):
                found_file = filename
                break
        except FileNotFoundError:
            continue
    
    if not found_file:
        print("No HTML file found. Please ensure you have one of these files:")
        for filename in html_files:
            print(f"  - {filename}")
        return
    
    print(f"Processing: {found_file}")
    
    extractor = EnhancedMagnusExtractor()
    
    # Extract basic table data
    print("Extracting basic table data...")
    basic_records = extractor.extract_basic_table_data(found_file)
    print(f"Found {len(basic_records)} basic records")
    
    # Extract detailed form data
    print("Extracting detailed form data...")
    detailed_records = extractor.extract_detailed_records(found_file)
    print(f"Found {len(detailed_records)} detailed records")
    
    # Show sample detailed record
    if detailed_records:
        print("\n=== SAMPLE DETAILED RECORD ===")
        sample = detailed_records[0]
        for key, value in sample.items():
            print(f"{key}: {value}")
    
    # Merge data
    print("\nMerging basic and detailed data...")
    merged_records = extractor.merge_basic_and_detailed_data(basic_records, detailed_records)
    
    # Save enhanced data
    json_file, csv_file = extractor.save_enhanced_data(merged_records)
    
    print(f"\n=== EXTRACTION SUMMARY ===")
    print(f"Total merged records: {len(merged_records)}")
    
    # Show available fields
    if merged_records:
        all_fields = set()
        for record in merged_records:
            all_fields.update(record.keys())
        
        print(f"\nAvailable fields ({len(all_fields)}):")
        for field in sorted(all_fields):
            print(f"  - {field}")

if __name__ == "__main__":
    main()
