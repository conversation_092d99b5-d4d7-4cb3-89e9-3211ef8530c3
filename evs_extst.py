#evs

import os
import glob
import pandas as pd
import rich_progress
import re
from datetime import datetime

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def extract_segment_from_path(path):
    """Extract conference segment name from path.

    Args:
        path (str): The file path to extract segment from

    Returns:
        str or None: The extracted segment name or None if not found
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        segment = match.group(1)
        rich_progress.print_status(f"Found segment: {segment}", "success")
        return segment
    else:
        rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
        # Prompt for manual input
        segment = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
        if segment.strip():
            rich_progress.print_status(f"Using manually entered segment: {segment}", "info")
            return segment
        else:
            rich_progress.print_status("No segment name provided", "error")
            return None

# Print welcome header
print_header("EVS Email Validation Processor")

# Get the path from the user
print_section("Input Path")
path = input("Enter loc: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")
csn = extract_segment_from_path(path)
if not csn:
    rich_progress.print_status("Could not determine conference segment name. Exiting.", "error")
    exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Initialize progress bar with gradient colors
print_section("Processing Data")
total_steps = 6  # Total number of steps in the process
progress_bar, update_progress = rich_progress.create_progress_bar(
    total=total_steps,
    description="Processing EVS data",
    color_scheme="blue"
)

# Step 1: Read and concatenate all CSV files
rich_progress.print_status("Reading and concatenating CSV files...", "info")
df_hbs = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)
update_progress(1, "Read CSV files")

# Step 2: Rename the 'email' column to 'Email'
rich_progress.print_status("Standardizing column names...", "info")
df_hbs.rename(columns={'email': 'Email'}, inplace=True)
update_progress(1, "Standardized columns")

# Step 3: Filter data for spam traps, invalid, high risk, low risk, and valid results
rich_progress.print_status("Filtering data by validation results...", "info")
df_st = df_hbs[df_hbs['reasons'].str.contains("PotentialSpamTrap|RoleAccount", na=False)]
df_invalid = df_hbs[df_hbs['result'].str.contains("Invalid", na=False)]
df_highrisk = df_hbs[df_hbs['status'].str.contains("HighRisk", na=False)]
df_lowrisk = df_hbs[df_hbs['status'].str.contains("LowRisk", na=False)]
df_valid = df_hbs[df_hbs['result'].str.contains("Valid", na=False)]

# Print summary of filtered data
rich_progress.print_status(f"Spam traps: {len(df_st)} emails", "info")
rich_progress.print_status(f"Invalid: {len(df_invalid)} emails", "info")
rich_progress.print_status(f"High risk: {len(df_highrisk)} emails", "info")
rich_progress.print_status(f"Low risk: {len(df_lowrisk)} emails", "info")
rich_progress.print_status(f"Valid: {len(df_valid)} emails", "info")

update_progress(1, "Filtered data")

# Step 4: Create 'invalid' and 'valid' folders if they don't exist and save filtered data there
rich_progress.print_status("Saving filtered data to CSV files...", "info")
invalid_folder = os.path.join(os.getcwd(), 'invalid')
valid_folder = os.path.join(os.getcwd(), 'valid')
os.makedirs(invalid_folder, exist_ok=True)
os.makedirs(valid_folder, exist_ok=True)

# Save invalid filtered data to individual CSV files with EVS suffix and conference segment name
spam_traps_file = os.path.join(invalid_folder, f'{csn}_EVS_SpamTraps.csv')
df_st.Email.to_csv(spam_traps_file, index=False)
rich_progress.print_status(f"Saved {len(df_st)} spam traps to {spam_traps_file}", "success")

invalid_file = os.path.join(invalid_folder, f'{csn}_EVS_Invalid.csv')
df_invalid.Email.to_csv(invalid_file, index=False)
rich_progress.print_status(f"Saved {len(df_invalid)} invalid emails to {invalid_file}", "success")

# Save valid filtered data to individual CSV files with EVS suffix and conference segment name
valid_file = os.path.join(valid_folder, f'{csn}_EVS_Valid.csv')
df_valid.Email.to_csv(valid_file, index=False)
rich_progress.print_status(f"Saved {len(df_valid)} valid emails to {valid_file}", "success")

lowrisk_file = os.path.join(valid_folder, f'{csn}_EVS_LowRisk.csv')
df_lowrisk.Email.to_csv(lowrisk_file, index=False)
rich_progress.print_status(f"Saved {len(df_lowrisk)} low risk emails to {lowrisk_file}", "success")

# Save combined valid and low-risk data
combined_valid_file = os.path.join(valid_folder, f'{csn}_EVS_AllValid.csv')
df_concat_valids = pd.concat([df_valid, df_lowrisk], axis=0)
df_concat_valids.Email.to_csv(combined_valid_file, index=False)
rich_progress.print_status(f"Saved {len(df_concat_valids)} combined valid emails to {combined_valid_file}", "success")

update_progress(1, "Saved filtered data")

# Step 5: Concatenate invalid and spam trap data with reason columns
rich_progress.print_status("Updating master hardbounces file...", "info")

# Add reason column to invalid emails
df_invalid_with_reason = df_invalid[['Email']].copy()
df_invalid_with_reason['Reason'] = 'invalid/hardbounce'

# Add reason column to spam trap emails
df_st_with_reason = df_st[['Email']].copy()
df_st_with_reason['Reason'] = 'spamtrap/abuse'

# Concatenate invalid and spam trap data with reasons
df_concat_invalid = pd.concat([df_invalid_with_reason, df_st_with_reason], axis=0)
rich_progress.print_status(f"Combined {len(df_concat_invalid)} invalid and spam trap emails with reasons", "info")

master_hb_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv"
df_hb_all = pd.read_csv(master_hb_path)
rich_progress.print_status(f"Loaded {len(df_hb_all)} existing hardbounces", "info")

# Ensure existing master file has Reason column if it doesn't exist
if 'Reason' not in df_hb_all.columns:
    df_hb_all['Reason'] = 'legacy'  # Mark existing entries as legacy
    rich_progress.print_status("Added Reason column to existing master hardbounces data", "info")

# Combine and remove duplicates based on Email only (keep most recent reason)
df_concat_allinvalids = pd.concat([df_hb_all, df_concat_invalid], axis=0)
original_count = len(df_concat_allinvalids)
# Drop duplicates keeping the last occurrence (newest data with reasons)
df_concat_allinvalids.drop_duplicates(subset='Email', keep='last', inplace=True)
rich_progress.print_status(f"Removed {original_count - len(df_concat_allinvalids)} duplicate emails", "info")

# Save back to master file with both Email and Reason columns
df_concat_allinvalids[['Email', 'Reason']].to_csv(master_hb_path, mode='w', index=False)
rich_progress.print_status(f"Updated master hardbounces file with {len(df_concat_invalid)} new entries", "success")

update_progress(1, "Updated master hardbounces")

# Step 6: Final processing summary
rich_progress.print_status("Finalizing data processing...", "info")
# df_concat_valids was already created and saved in Step 4
rich_progress.print_status(f"Valid and low-risk data already processed and saved", "success")
update_progress(1, "Finalized processing")

# Close the progress bar
progress_bar.stop()

# Print completion message
print_header("Processing Completed Successfully!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(df_hbs)}", "success")
rich_progress.print_status(f"Invalid records identified: {len(df_concat_invalid)}", "info")
rich_progress.print_status(f"  - Invalid/Hardbounce: {len(df_invalid_with_reason)}", "info")
rich_progress.print_status(f"  - Spamtrap/Abuse: {len(df_st_with_reason)}", "info")
rich_progress.print_status(f"Valid records identified: {len(df_concat_valids)}", "info")
rich_progress.print_status(f"  - Valid: {len(df_valid)}", "info")
rich_progress.print_status(f"  - Low Risk: {len(df_lowrisk)}", "info")
rich_progress.print_status(f"Files saved in 'invalid' and 'valid' folders", "success")