#!/usr/bin/env python3
"""
Asia-Europe Data Extraction Script
=================================

This script extracts data from China, Japan, Korea, and all European countries
using the existing HIC filtering functionality. It combines the best features
from both hic_continents.py and hic_individual_countries.py.

Target Countries:
- China (including special Chinese domains)
- Japan
- South Korea
- All European countries

Author: Data Engineering Team
Version: 1.0.0
Date: 2026-01-30
"""

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime
from pathlib import Path
import random

# Import rich_progress for gradient progress bars
import rich_progress

# Suppress pandas warnings - compatible with both old and new pandas versions
try:
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

# TLD to Country Name Mapping (comprehensive)
tld_to_country = {
    # European countries
    '.ad': 'Andorra', '.al': 'Albania', '.at': 'Austria', '.ba': 'Bosnia and Herzegovina',
    '.be': 'Belgium', '.bg': 'Bulgaria', '.by': 'Belarus', '.ch': 'Switzerland',
    '.cy': 'Cyprus', '.cz': 'Czech Republic', '.de': 'Germany', '.dk': 'Denmark',
    '.ee': 'Estonia', '.es': 'Spain', '.eu': 'European Union', '.fi': 'Finland',
    '.fr': 'France', '.gb': 'United Kingdom', '.gr': 'Greece', '.hr': 'Croatia',
    '.hu': 'Hungary', '.ie': 'Ireland', '.is': 'Iceland', '.it': 'Italy',
    '.li': 'Liechtenstein', '.lt': 'Lithuania', '.lu': 'Luxembourg', '.lv': 'Latvia',
    '.mc': 'Monaco', '.md': 'Moldova', '.me': 'Montenegro', '.mk': 'North Macedonia',
    '.mt': 'Malta', '.nl': 'Netherlands', '.no': 'Norway', '.pl': 'Poland',
    '.pt': 'Portugal', '.ro': 'Romania', '.rs': 'Serbia', '.ru': 'Russia',
    '.se': 'Sweden', '.si': 'Slovenia', '.sk': 'Slovakia', '.sm': 'San Marino',
    '.tr': 'Turkey', '.ua': 'Ukraine', '.uk': 'United Kingdom', '.va': 'Vatican City',
    
    # Asian countries (China, Japan, Korea)
    '.cn': 'China', '.jp': 'Japan', '.kr': 'South Korea', '.kp': 'North Korea',
    '.hk': 'Hong Kong', '.mo': 'Macao', '.tw': 'Taiwan',
    
    # Special domains
    '.edu': 'United States', '.gov': 'United States', '.mil': 'United States'
}

# European country TLDs list
european_tlds = [
    '.ad', '.al', '.at', '.ba', '.be', '.bg', '.by', '.ch', '.cy', '.cz',
    '.de', '.dk', '.ee', '.es', '.eu', '.fi', '.fr', '.gb', '.gr', '.hr',
    '.hu', '.ie', '.is', '.it', '.li', '.lt', '.lu', '.lv', '.mc', '.md',
    '.me', '.mk', '.mt', '.nl', '.no', '.pl', '.pt', '.ro', '.rs', '.ru',
    '.se', '.si', '.sk', '.sm', '.tr', '.ua', '.uk', '.va'
]

# Special Chinese email domains mapping
chinese_domains = {
    '163.com': 'China', 'qq.com': 'China', '126.com': 'China', 'sina.com': 'China',
    'sohu.com': 'China', 'tom.com': 'China', 'aliyun.com': 'China', '21cn.com': 'China',
    'baidu.com': 'China', 'yeah.net': 'China', 'sogou.com': 'China', '163.net': 'China',
    'sina.net': 'China', 'chinaren.com': 'China'
}

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special Chinese domains first
    for domain, country in chinese_domains.items():
        if email.endswith(domain):
            return country

    # Check for country TLDs
    for tld, country in tld_to_country.items():
        if email.endswith(tld):
            return country

    return 'Other'

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 60, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def read_directories_from_file(file_path):
    """Read directory paths from a text file."""
    directories = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # Skip empty lines and comments
                    if os.path.exists(line) and os.path.isdir(line):
                        directories.append(line)
                        rich_progress.print_status(f"✓ Valid directory: {line}", "success")
                    else:
                        rich_progress.print_status(f"⚠ Line {line_num}: Directory not found: {line}", "warning")
    except FileNotFoundError:
        rich_progress.print_status(f"Error: File not found: {file_path}", "error")
        return []
    except Exception as e:
        rich_progress.print_status(f"Error reading file {file_path}: {str(e)}", "error")
        return []

    return directories

def extract_conference_name(path):
    """Extract conference segment name from path, looking for patterns like IDC-2026."""
    # First, try to find the specific pattern: letters-hyphen-numbers (e.g., IDC-2026)
    pattern_specific = r"([A-Za-z]+[-_][0-9]+)"

    # Search in the full path first
    matches = re.findall(pattern_specific, path)
    if matches:
        # Return the last match found (most specific)
        return matches[-1]

    # If no specific pattern found, try the original broader pattern
    pattern_broad = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern_broad, path)

    if match:
        # Check if the matched segment contains the specific pattern
        segment = match.group(1)
        specific_matches = re.findall(pattern_specific, segment)
        if specific_matches:
            return specific_matches[-1]
        return segment

    # Try to extract from directory name
    dir_name = os.path.basename(path.rstrip('\\'))
    if dir_name:
        # Check if directory name contains the specific pattern
        specific_matches = re.findall(pattern_specific, dir_name)
        if specific_matches:
            return specific_matches[-1]
        return dir_name

    return "Unknown_Conference"

def process_single_directory(directory_path):
    """Process a single directory and extract Asia-Europe data, saving results in that directory."""
    try:
        original_dir = os.getcwd()
        os.chdir(directory_path)

        # Extract conference segment name
        csn = extract_conference_name(directory_path)
        rich_progress.print_status(f"Processing: {csn}", "info")

        # Find CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            rich_progress.print_status(f"No CSV files found in {directory_path}", "warning")
            os.chdir(original_dir)
            return None

        rich_progress.print_status(f"Found {len(csv_files)} CSV files", "info")

        # Read and concatenate CSV files
        dfs = []
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file, low_memory=False)
                dfs.append(df)
            except Exception as e:
                rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")

        if not dfs:
            rich_progress.print_status(f"No valid CSV files could be read from {directory_path}", "error")
            os.chdir(original_dir)
            return None

        # Concatenate all dataframes
        d2_EVENT = pd.concat(dfs, ignore_index=True)
        rich_progress.print_status(f"Combined {len(csv_files)} files: {len(d2_EVENT)} total records", "info")

        # Add Country column
        d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)

        # Create filters for target countries
        country_data = {}

        # Filter China (including special domains)
        china_mask = pd.Series(False, index=d2_EVENT.index)
        china_mask = china_mask | d2_EVENT["Email"].str.endswith('.cn', na=False)

        for domain in chinese_domains.keys():
            china_mask = china_mask | d2_EVENT["Email"].str.endswith(domain, na=False)

        for tld in ['.hk', '.mo', '.tw']:
            china_mask = china_mask | d2_EVENT["Email"].str.endswith(tld, na=False)

        country_data['China'] = d2_EVENT[china_mask]

        # Filter Japan
        japan_mask = d2_EVENT["Email"].str.endswith('.jp', na=False)
        country_data['Japan'] = d2_EVENT[japan_mask]

        # Filter South Korea
        korea_mask = d2_EVENT["Email"].str.endswith('.kr', na=False)
        country_data['South_Korea'] = d2_EVENT[korea_mask]

        # Filter European countries
        europe_mask = pd.Series(False, index=d2_EVENT.index)
        for tld in european_tlds:
            europe_mask = europe_mask | d2_EVENT["Email"].str.endswith(tld, na=False)
        country_data['Europe'] = d2_EVENT[europe_mask]

        # Combine all target data into one dataframe
        target_data_frames = []

        # Add China data
        if len(country_data['China']) > 0:
            china_df = country_data['China'].copy()
            china_df['Target_Region'] = 'China'
            target_data_frames.append(china_df)

        # Add Japan data
        if len(country_data['Japan']) > 0:
            japan_df = country_data['Japan'].copy()
            japan_df['Target_Region'] = 'Japan'
            target_data_frames.append(japan_df)

        # Add South Korea data
        if len(country_data['South_Korea']) > 0:
            korea_df = country_data['South_Korea'].copy()
            korea_df['Target_Region'] = 'South Korea'
            target_data_frames.append(korea_df)

        # Add European data with individual country identification
        europe_df = country_data['Europe'].copy()
        if len(europe_df) > 0:
            europe_df['Target_Region'] = 'Europe'
            europe_df['European_Country'] = 'Unknown'

            # Identify specific European countries
            for tld in european_tlds:
                country_mask = europe_df["Email"].str.endswith(tld, na=False)
                if country_mask.any():
                    country_name = tld_to_country.get(tld, tld.replace('.', '').upper())
                    europe_df.loc[country_mask, 'European_Country'] = country_name

            target_data_frames.append(europe_df)

        if not target_data_frames:
            rich_progress.print_status("No target country data found in this directory", "warning")
            os.chdir(original_dir)
            return None

        # Combine all target data
        final_combined_df = pd.concat(target_data_frames, ignore_index=True)

        # Remove the Target_Region and European_Country columns from final output
        columns_to_remove = ['Target_Region', 'European_Country']
        for col in columns_to_remove:
            if col in final_combined_df.columns:
                final_combined_df = final_combined_df.drop(columns=[col])

        # Randomize the output
        final_combined_df = final_combined_df.sample(frac=1, random_state=None).reset_index(drop=True)

        # Create output directory in the current directory
        base_output_dir = os.path.join(directory_path, "asia_europe_extraction")
        os.makedirs(base_output_dir, exist_ok=True)

        # Generate output filename with the new pattern
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"{csn}_asia_europe_data_{timestamp}.csv"
        output_filepath = os.path.join(base_output_dir, output_filename)

        # Save the file
        final_combined_df.to_csv(output_filepath, encoding='utf-8-sig', index=False)

        # Create statistics
        stats = {
            'csn': csn,
            'directory': directory_path,
            'total_records': len(d2_EVENT),
            'extracted_records': len(final_combined_df),
            'china_records': len(country_data['China']),
            'japan_records': len(country_data['Japan']),
            'korea_records': len(country_data['South_Korea']),
            'europe_records': len(country_data['Europe']),
            'output_file': output_filename,
            'output_path': output_filepath
        }

        rich_progress.print_status(f"✓ Saved: {output_filename} ({len(final_combined_df)} records)", "success")

        os.chdir(original_dir)
        return stats

    except Exception as e:
        rich_progress.print_status(f"Error processing {directory_path}: {str(e)}", "error")
        try:
            os.chdir(original_dir)
        except:
            pass
        return None

# Print welcome header
print_header("Asia-Europe Data Extraction Script")
rich_progress.print_status("Extracting data from China, Japan, Korea, and European countries", "info")

# Ask user for processing mode
print_section("Processing Mode Selection")
rich_progress.print_status("Choose processing mode:", "info")
rich_progress.print_status("1. Single directory - Process one directory", "info")
rich_progress.print_status("2. Batch processing - Process multiple directories from a text file", "info")

while True:
    mode_choice = input("\nEnter your choice (1 or 2): ").strip()
    if mode_choice in ["1", "2"]:
        break
    else:
        rich_progress.print_status("Please enter 1 or 2", "error")

# Initialize statistics tracking
all_stats = []
successful_dirs = 0

if mode_choice == "1":
    # Single directory mode
    print_section("Single Directory Processing")
    path = input("Enter the directory path containing CSV files: ").strip().strip('"\'')

    if not path:
        rich_progress.print_status("Error: No directory path specified", "error")
        exit()

    stats = process_single_directory(path)
    if stats:
        all_stats.append(stats)
        successful_dirs = 1
        rich_progress.print_status(f"Successfully processed {stats['csn']}", "success")
    else:
        rich_progress.print_status("Failed to process directory. Exiting.", "error")
        exit()

else:
    # Batch processing mode
    print_section("Batch Processing")
    directories_file = input("Enter the path to the text file containing directory paths: ").strip().strip('"\'')

    if not directories_file:
        rich_progress.print_status("Error: No file path specified", "error")
        exit()

    # Read directories from file
    directories = read_directories_from_file(directories_file)

    if not directories:
        rich_progress.print_status("No valid directories found. Exiting.", "error")
        exit()

    rich_progress.print_status(f"Found {len(directories)} valid directories to process", "success")

    # Create progress bar for batch processing
    batch_bar, update_batch = rich_progress.create_progress_bar(
        total=len(directories),
        description="Processing directories",
        color_scheme="blue"
    )

    # Process each directory individually
    for i, directory in enumerate(directories, 1):
        rich_progress.print_status(f"\nProcessing directory {i}/{len(directories)}: {directory}", "info")

        stats = process_single_directory(directory)
        if stats:
            all_stats.append(stats)
            successful_dirs += 1
            rich_progress.print_status(f"✓ Completed {stats['csn']}: {stats['extracted_records']} records extracted", "success")
        else:
            rich_progress.print_status(f"✗ Failed to process {directory}", "error")

        update_batch(1, f"Processed {os.path.basename(directory)}")

    batch_bar.stop()

    if not all_stats:
        rich_progress.print_status("No directories could be processed successfully. Exiting.", "error")
        exit()

# Print completion summary
print_header("Asia-Europe Data Extraction Completed!")

# Print processing mode information
if mode_choice == "1":
    rich_progress.print_status(f"Processing mode: Single Directory", "info")
else:
    rich_progress.print_status(f"Processing mode: Batch Processing", "info")

rich_progress.print_status(f"Directories processed: {successful_dirs}/{len(all_stats) if mode_choice == '2' else 1}", "success")

# Print detailed results for each directory
print_section("Individual Directory Results")
rich_progress.print_status("-" * 80, "info")
rich_progress.print_status(f"{'Conference':<25} {'Total':>10} {'Extracted':>10} {'China':>8} {'Japan':>8} {'Korea':>8} {'Europe':>8}", "info")
rich_progress.print_status("-" * 80, "info")

total_original = 0
total_extracted = 0
total_china = 0
total_japan = 0
total_korea = 0
total_europe = 0

for stats in all_stats:
    rich_progress.print_status(
        f"{stats['csn'][:24]:<25} {stats['total_records']:>10} {stats['extracted_records']:>10} "
        f"{stats['china_records']:>8} {stats['japan_records']:>8} {stats['korea_records']:>8} {stats['europe_records']:>8}",
        "success"
    )

    total_original += stats['total_records']
    total_extracted += stats['extracted_records']
    total_china += stats['china_records']
    total_japan += stats['japan_records']
    total_korea += stats['korea_records']
    total_europe += stats['europe_records']

rich_progress.print_status("-" * 80, "info")
rich_progress.print_status(
    f"{'TOTALS':<25} {total_original:>10} {total_extracted:>10} "
    f"{total_china:>8} {total_japan:>8} {total_korea:>8} {total_europe:>8}",
    "header"
)
rich_progress.print_status("-" * 80, "info")

# Print output file locations
print_section("Output Files Created")
for stats in all_stats:
    rich_progress.print_status(f"📁 {stats['csn']}: {stats['output_file']}", "success")
    rich_progress.print_status(f"   Location: {stats['output_path']}", "info")

# Final statistics
print_section("Final Statistics")
extraction_percentage = (total_extracted / total_original * 100) if total_original > 0 else 0
rich_progress.print_status(f"Total original records: {total_original:,}", "info")
rich_progress.print_status(f"Total records extracted: {total_extracted:,}", "success")
rich_progress.print_status(f"Overall extraction rate: {extraction_percentage:.2f}%", "success")
rich_progress.print_status(f"Directories successfully processed: {successful_dirs}", "success")

# Regional breakdown
print_section("Regional Breakdown")
if total_extracted > 0:
    china_pct = (total_china / total_extracted * 100)
    japan_pct = (total_japan / total_extracted * 100)
    korea_pct = (total_korea / total_extracted * 100)
    europe_pct = (total_europe / total_extracted * 100)

    rich_progress.print_status(f"China: {total_china:,} records ({china_pct:.1f}%)", "info")
    rich_progress.print_status(f"Japan: {total_japan:,} records ({japan_pct:.1f}%)", "info")
    rich_progress.print_status(f"South Korea: {total_korea:,} records ({korea_pct:.1f}%)", "info")
    rich_progress.print_status(f"Europe: {total_europe:,} records ({europe_pct:.1f}%)", "info")

rich_progress.print_status("\n🎉 Individual directory processing completed successfully!", "header")
rich_progress.print_status("📂 Each directory has its own 'asia_europe_extraction' folder with extracted data", "success")
rich_progress.print_status("🔀 All output files have been randomized", "success")



# Pause for user to see results
input("\nPress Enter to exit...")
