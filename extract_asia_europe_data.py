#!/usr/bin/env python3
"""
Asia-Europe Data Extraction Script
=================================

This script extracts data from China, Japan, Korea, and all European countries
using the existing HIC filtering functionality. It combines the best features
from both hic_continents.py and hic_individual_countries.py.

Target Countries:
- China (including special Chinese domains)
- Japan
- South Korea
- All European countries

Author: Data Engineering Team
Version: 1.0.0
Date: 2026-01-30
"""

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime
from pathlib import Path
import random

# Import rich_progress for gradient progress bars
import rich_progress

# Suppress pandas warnings - compatible with both old and new pandas versions
try:
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

# TLD to Country Name Mapping (comprehensive)
tld_to_country = {
    # European countries
    '.ad': 'Andorra', '.al': 'Albania', '.at': 'Austria', '.ba': 'Bosnia and Herzegovina',
    '.be': 'Belgium', '.bg': 'Bulgaria', '.by': 'Belarus', '.ch': 'Switzerland',
    '.cy': 'Cyprus', '.cz': 'Czech Republic', '.de': 'Germany', '.dk': 'Denmark',
    '.ee': 'Estonia', '.es': 'Spain', '.eu': 'European Union', '.fi': 'Finland',
    '.fr': 'France', '.gb': 'United Kingdom', '.gr': 'Greece', '.hr': 'Croatia',
    '.hu': 'Hungary', '.ie': 'Ireland', '.is': 'Iceland', '.it': 'Italy',
    '.li': 'Liechtenstein', '.lt': 'Lithuania', '.lu': 'Luxembourg', '.lv': 'Latvia',
    '.mc': 'Monaco', '.md': 'Moldova', '.me': 'Montenegro', '.mk': 'North Macedonia',
    '.mt': 'Malta', '.nl': 'Netherlands', '.no': 'Norway', '.pl': 'Poland',
    '.pt': 'Portugal', '.ro': 'Romania', '.rs': 'Serbia', '.ru': 'Russia',
    '.se': 'Sweden', '.si': 'Slovenia', '.sk': 'Slovakia', '.sm': 'San Marino',
    '.tr': 'Turkey', '.ua': 'Ukraine', '.uk': 'United Kingdom', '.va': 'Vatican City',
    
    # Asian countries (China, Japan, Korea)
    '.cn': 'China', '.jp': 'Japan', '.kr': 'South Korea', '.kp': 'North Korea',
    '.hk': 'Hong Kong', '.mo': 'Macao', '.tw': 'Taiwan',
    
    # Special domains
    '.edu': 'United States', '.gov': 'United States', '.mil': 'United States'
}

# Special Chinese email domains mapping
chinese_domains = {
    '163.com': 'China', 'qq.com': 'China', '126.com': 'China', 'sina.com': 'China',
    'sohu.com': 'China', 'tom.com': 'China', 'aliyun.com': 'China', '21cn.com': 'China',
    'baidu.com': 'China', 'yeah.net': 'China', 'sogou.com': 'China', '163.net': 'China',
    'sina.net': 'China', 'chinaren.com': 'China'
}

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special Chinese domains first
    for domain, country in chinese_domains.items():
        if email.endswith(domain):
            return country

    # Check for country TLDs
    for tld, country in tld_to_country.items():
        if email.endswith(tld):
            return country

    return 'Other'

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 60, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def read_directories_from_file(file_path):
    """Read directory paths from a text file."""
    directories = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # Skip empty lines and comments
                    if os.path.exists(line) and os.path.isdir(line):
                        directories.append(line)
                        rich_progress.print_status(f"✓ Valid directory: {line}", "success")
                    else:
                        rich_progress.print_status(f"⚠ Line {line_num}: Directory not found: {line}", "warning")
    except FileNotFoundError:
        rich_progress.print_status(f"Error: File not found: {file_path}", "error")
        return []
    except Exception as e:
        rich_progress.print_status(f"Error reading file {file_path}: {str(e)}", "error")
        return []

    return directories

def extract_conference_name(path):
    """Extract conference segment name from path."""
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)

    if match:
        return match.group(1)
    else:
        # Try to extract from directory name
        dir_name = os.path.basename(path.rstrip('\\'))
        if dir_name:
            return dir_name
        return "Unknown_Conference"

def process_directory_data(directory_path):
    """Process CSV files in a directory and return the combined dataframe."""
    try:
        original_dir = os.getcwd()
        os.chdir(directory_path)

        # Extract conference segment name
        csn = extract_conference_name(directory_path)

        # Find CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            rich_progress.print_status(f"No CSV files found in {directory_path}", "warning")
            return None, csn

        rich_progress.print_status(f"Found {len(csv_files)} CSV files in {csn}", "info")

        # Read and concatenate CSV files
        dfs = []
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file, low_memory=False)
                dfs.append(df)
            except Exception as e:
                rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")

        if not dfs:
            rich_progress.print_status(f"No valid CSV files could be read from {directory_path}", "error")
            return None, csn

        # Concatenate all dataframes
        d2_EVENT = pd.concat(dfs, ignore_index=True)

        # Add source conference column
        d2_EVENT['Source_Conference'] = csn

        os.chdir(original_dir)
        return d2_EVENT, csn

    except Exception as e:
        rich_progress.print_status(f"Error processing {directory_path}: {str(e)}", "error")
        try:
            os.chdir(original_dir)
        except:
            pass
        return None, "Unknown"

# Print welcome header
print_header("Asia-Europe Data Extraction Script")
rich_progress.print_status("Extracting data from China, Japan, Korea, and European countries", "info")

# Ask user for processing mode
print_section("Processing Mode Selection")
rich_progress.print_status("Choose processing mode:", "info")
rich_progress.print_status("1. Single directory - Process one directory", "info")
rich_progress.print_status("2. Batch processing - Process multiple directories from a text file", "info")

while True:
    mode_choice = input("\nEnter your choice (1 or 2): ").strip()
    if mode_choice in ["1", "2"]:
        break
    else:
        rich_progress.print_status("Please enter 1 or 2", "error")

# Initialize list to store all dataframes
all_dataframes = []
all_conference_names = []

if mode_choice == "1":
    # Single directory mode
    print_section("Single Directory Processing")
    path = input("Enter the directory path containing CSV files: ").strip().strip('"\'')

    if not path:
        rich_progress.print_status("Error: No directory path specified", "error")
        exit()

    df, csn = process_directory_data(path)
    if df is not None:
        all_dataframes.append(df)
        all_conference_names.append(csn)
        rich_progress.print_status(f"Successfully processed {csn}: {len(df)} records", "success")
    else:
        rich_progress.print_status("Failed to process directory. Exiting.", "error")
        exit()

else:
    # Batch processing mode
    print_section("Batch Processing")
    directories_file = input("Enter the path to the text file containing directory paths: ").strip().strip('"\'')

    if not directories_file:
        rich_progress.print_status("Error: No file path specified", "error")
        exit()

    # Read directories from file
    directories = read_directories_from_file(directories_file)

    if not directories:
        rich_progress.print_status("No valid directories found. Exiting.", "error")
        exit()

    rich_progress.print_status(f"Found {len(directories)} valid directories to process", "success")

    # Create progress bar for batch processing
    batch_bar, update_batch = rich_progress.create_progress_bar(
        total=len(directories),
        description="Processing directories",
        color_scheme="blue"
    )

    # Process each directory
    for i, directory in enumerate(directories, 1):
        rich_progress.print_status(f"Processing directory {i}/{len(directories)}: {directory}", "info")

        df, csn = process_directory_data(directory)
        if df is not None:
            all_dataframes.append(df)
            all_conference_names.append(csn)
            rich_progress.print_status(f"✓ Processed {csn}: {len(df)} records", "success")
        else:
            rich_progress.print_status(f"✗ Failed to process {directory}", "error")

        update_batch(1, f"Processed {os.path.basename(directory)}")

    batch_bar.stop()

    if not all_dataframes:
        rich_progress.print_status("No data could be processed from any directory. Exiting.", "error")
        exit()

# Combine all dataframes
print_section("Combining Data")
rich_progress.print_status("Combining data from all sources...", "info")
d2_EVENT = pd.concat(all_dataframes, ignore_index=True)
rich_progress.print_status(f"Combined {len(all_dataframes)} datasets: {len(d2_EVENT)} total records", "success")

# Generate output filename based on processing mode
if mode_choice == "1":
    output_filename = f"Asia_Europe_Extract_{all_conference_names[0]}"
else:
    output_filename = f"Asia_Europe_Extract_Batch_{len(all_conference_names)}_Conferences"

# Add Country column
print_section("Adding Country Column")
rich_progress.print_status("Extracting country information from email domains...", "info")
d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)
rich_progress.print_status(f"Successfully added Country column to {len(d2_EVENT)} records", "success")

# Define target countries and their TLDs
print_section("Defining Target Countries")

# European country TLDs
european_tlds = [
    '.ad', '.al', '.at', '.ba', '.be', '.bg', '.by', '.ch', '.cy', '.cz',
    '.de', '.dk', '.ee', '.es', '.eu', '.fi', '.fr', '.gb', '.gr', '.hr',
    '.hu', '.ie', '.is', '.it', '.li', '.lt', '.lu', '.lv', '.mc', '.md',
    '.me', '.mk', '.mt', '.nl', '.no', '.pl', '.pt', '.ro', '.rs', '.ru',
    '.se', '.si', '.sk', '.sm', '.tr', '.ua', '.uk', '.va'
]

# Asian target countries TLDs
asian_target_tlds = {
    'China': ['.cn', '.hk', '.mo', '.tw'],  # Including Hong Kong, Macao, Taiwan
    'Japan': ['.jp'],
    'South Korea': ['.kr'],
    'North Korea': ['.kp']  # Just in case
}

rich_progress.print_status(f"Targeting {len(european_tlds)} European TLDs", "info")
rich_progress.print_status(f"Targeting {sum(len(tlds) for tlds in asian_target_tlds.values())} Asian TLDs", "info")

# Create filters for target countries
print_section("Creating Country Filters")

# Initialize results dictionary
country_data = {}

# Filter China (including special domains)
print_section("Filtering China")
china_mask = pd.Series(False, index=d2_EVENT.index)

# Add .cn TLD
china_mask = china_mask | d2_EVENT["Email"].str.endswith('.cn', na=False)

# Add special Chinese domains
for domain in chinese_domains.keys():
    china_mask = china_mask | d2_EVENT["Email"].str.endswith(domain, na=False)

# Add Hong Kong, Macao, Taiwan
for tld in ['.hk', '.mo', '.tw']:
    china_mask = china_mask | d2_EVENT["Email"].str.endswith(tld, na=False)

country_data['China'] = d2_EVENT[china_mask]
rich_progress.print_status(f"China: {len(country_data['China'])} records", "success")

# Filter Japan
print_section("Filtering Japan")
japan_mask = d2_EVENT["Email"].str.endswith('.jp', na=False)
country_data['Japan'] = d2_EVENT[japan_mask]
rich_progress.print_status(f"Japan: {len(country_data['Japan'])} records", "success")

# Filter South Korea
print_section("Filtering South Korea")
korea_mask = d2_EVENT["Email"].str.endswith('.kr', na=False)
country_data['South_Korea'] = d2_EVENT[korea_mask]
rich_progress.print_status(f"South Korea: {len(country_data['South_Korea'])} records", "success")

# Filter European countries
print_section("Filtering European Countries")
europe_mask = pd.Series(False, index=d2_EVENT.index)

# Create a progress bar for European filtering
europe_bar, update_europe = rich_progress.create_progress_bar(
    total=len(european_tlds),
    description="Processing European TLDs",
    color_scheme="green"
)

for tld in european_tlds:
    europe_mask = europe_mask | d2_EVENT["Email"].str.endswith(tld, na=False)
    update_europe(1, f"Processed {tld}")

europe_bar.stop()

country_data['Europe'] = d2_EVENT[europe_mask]
rich_progress.print_status(f"Europe (combined): {len(country_data['Europe'])} records", "success")

# Combine all target data into one dataframe
print_section("Combining Target Countries Data")
target_data_frames = []

# Add China data
if len(country_data['China']) > 0:
    china_df = country_data['China'].copy()
    china_df['Target_Region'] = 'China'
    target_data_frames.append(china_df)
    rich_progress.print_status(f"Added China: {len(china_df)} records", "success")

# Add Japan data
if len(country_data['Japan']) > 0:
    japan_df = country_data['Japan'].copy()
    japan_df['Target_Region'] = 'Japan'
    target_data_frames.append(japan_df)
    rich_progress.print_status(f"Added Japan: {len(japan_df)} records", "success")

# Add South Korea data
if len(country_data['South_Korea']) > 0:
    korea_df = country_data['South_Korea'].copy()
    korea_df['Target_Region'] = 'South Korea'
    target_data_frames.append(korea_df)
    rich_progress.print_status(f"Added South Korea: {len(korea_df)} records", "success")

# Add European data with individual country identification
europe_df = country_data['Europe'].copy()
if len(europe_df) > 0:
    # Add specific European country identification
    europe_df['Target_Region'] = 'Europe'
    europe_df['European_Country'] = 'Unknown'

    # Identify specific European countries
    for tld in european_tlds:
        country_mask = europe_df["Email"].str.endswith(tld, na=False)
        if country_mask.any():
            country_name = tld_to_country.get(tld, tld.replace('.', '').upper())
            europe_df.loc[country_mask, 'European_Country'] = country_name

    target_data_frames.append(europe_df)
    rich_progress.print_status(f"Added Europe: {len(europe_df)} records", "success")

if not target_data_frames:
    rich_progress.print_status("No target country data found. Exiting.", "error")
    exit()

# Combine all target data
final_combined_df = pd.concat(target_data_frames, ignore_index=True)
rich_progress.print_status(f"Combined target data: {len(final_combined_df)} records", "success")

# Randomize the final output
print_section("Randomizing Output")
rich_progress.print_status("Shuffling records for randomized output...", "info")
final_combined_df = final_combined_df.sample(frac=1, random_state=None).reset_index(drop=True)
rich_progress.print_status(f"Successfully randomized {len(final_combined_df)} records", "success")

# Create output directory
print_section("Creating Output Directory")
base_output_dir = os.path.join(os.getcwd(), "asia_europe_extraction")
os.makedirs(base_output_dir, exist_ok=True)
rich_progress.print_status(f"Created output directory: {base_output_dir}", "info")

# Save the combined randomized file
print_section("Saving Combined Randomized Output")

# Generate timestamp for unique filename
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_filename_with_timestamp = f"{output_filename}_{timestamp}.csv"
output_filepath = os.path.join(base_output_dir, output_filename_with_timestamp)

try:
    final_combined_df.to_csv(output_filepath, encoding='utf-8-sig', index=False)
    rich_progress.print_status(f"Successfully saved combined file: {output_filename_with_timestamp}", "success")
    rich_progress.print_status(f"File location: {output_filepath}", "info")
    saved_files = 1
except Exception as e:
    rich_progress.print_status(f"Error saving combined file: {str(e)}", "error")
    saved_files = 0

# Print completion summary
print_header("Asia-Europe Data Extraction Completed!")

# Print processing mode information
if mode_choice == "1":
    rich_progress.print_status(f"Processing mode: Single Directory", "info")
    rich_progress.print_status(f"Conference processed: {all_conference_names[0]}", "success")
else:
    rich_progress.print_status(f"Processing mode: Batch Processing", "info")
    rich_progress.print_status(f"Conferences processed: {len(all_conference_names)}", "success")
    rich_progress.print_status(f"Conference list: {', '.join(all_conference_names)}", "info")

rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")
rich_progress.print_status(f"Records extracted: {len(final_combined_df)}", "success")
rich_progress.print_status(f"Output file saved: {saved_files > 0}", "success")

# Print detailed results
print_section("Extraction Results Summary")
rich_progress.print_status("-" * 60, "info")
rich_progress.print_status(f"{'Region/Country':<30} {'Records':>15} {'Percentage':>15}", "info")
rich_progress.print_status("-" * 60, "info")

# Count records by target region
region_counts = final_combined_df['Target_Region'].value_counts()

for region in ['China', 'Japan', 'South Korea', 'Europe']:
    count = region_counts.get(region, 0)
    percentage = (count / len(final_combined_df) * 100) if len(final_combined_df) > 0 else 0
    rich_progress.print_status(f"{region:<30} {count:>15} {percentage:>13.1f}%", "success")

rich_progress.print_status("-" * 60, "info")

# Top European countries if Europe data exists
if 'Europe' in region_counts and region_counts['Europe'] > 0:
    print_section("Top 10 European Countries")
    europe_data = final_combined_df[final_combined_df['Target_Region'] == 'Europe']
    european_country_counts = europe_data['European_Country'].value_counts().head(10)

    rich_progress.print_status("-" * 50, "info")
    rich_progress.print_status(f"{'Country':<30} {'Records':>15}", "info")
    rich_progress.print_status("-" * 50, "info")

    for country, count in european_country_counts.items():
        if country != 'Unknown':
            rich_progress.print_status(f"{country:<30} {count:>15}", "info")

    rich_progress.print_status("-" * 50, "info")

# Print output file information
print_section("Output File Information")
rich_progress.print_status(f"Output directory: {base_output_dir}", "info")
rich_progress.print_status(f"Output file: {output_filename_with_timestamp}", "success")
rich_progress.print_status(f"Full path: {output_filepath}", "info")
rich_progress.print_status(f"File size: {len(final_combined_df)} records", "info")
rich_progress.print_status(f"Data randomized: Yes", "success")

# Final statistics
print_section("Final Statistics")
extraction_percentage = (len(final_combined_df) / len(d2_EVENT) * 100) if len(d2_EVENT) > 0 else 0
rich_progress.print_status(f"Total original records: {len(d2_EVENT)}", "info")
rich_progress.print_status(f"Records extracted: {len(final_combined_df)}", "success")
rich_progress.print_status(f"Extraction percentage: {extraction_percentage:.2f}%", "success")
rich_progress.print_status(f"Data sources: {len(all_conference_names)} conference(s)", "info")

# Column information
print_section("Output File Columns")
rich_progress.print_status("The output file contains the following columns:", "info")
for col in final_combined_df.columns:
    rich_progress.print_status(f"  - {col}", "info")

rich_progress.print_status("\n🎉 Data extraction completed successfully!", "header")
rich_progress.print_status(f"📁 Output file: {output_filepath}", "success")

# Pause for user to see results
input("\nPress Enter to exit...")
