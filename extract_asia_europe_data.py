#!/usr/bin/env python3
"""
Asia-Europe Data Extraction Script
=================================

This script extracts data from China, Japan, Korea, and all European countries
using the existing HIC filtering functionality. It combines the best features
from both hic_continents.py and hic_individual_countries.py.

Target Countries:
- China (including special Chinese domains)
- Japan
- South Korea
- All European countries

Author: Data Engineering Team
Version: 1.0.0
Date: 2026-01-30
"""

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime
from pathlib import Path

# Import rich_progress for gradient progress bars
import rich_progress

# Suppress pandas warnings - compatible with both old and new pandas versions
try:
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

# TLD to Country Name Mapping (comprehensive)
tld_to_country = {
    # European countries
    '.ad': 'Andorra', '.al': 'Albania', '.at': 'Austria', '.ba': 'Bosnia and Herzegovina',
    '.be': 'Belgium', '.bg': 'Bulgaria', '.by': 'Belarus', '.ch': 'Switzerland',
    '.cy': 'Cyprus', '.cz': 'Czech Republic', '.de': 'Germany', '.dk': 'Denmark',
    '.ee': 'Estonia', '.es': 'Spain', '.eu': 'European Union', '.fi': 'Finland',
    '.fr': 'France', '.gb': 'United Kingdom', '.gr': 'Greece', '.hr': 'Croatia',
    '.hu': 'Hungary', '.ie': 'Ireland', '.is': 'Iceland', '.it': 'Italy',
    '.li': 'Liechtenstein', '.lt': 'Lithuania', '.lu': 'Luxembourg', '.lv': 'Latvia',
    '.mc': 'Monaco', '.md': 'Moldova', '.me': 'Montenegro', '.mk': 'North Macedonia',
    '.mt': 'Malta', '.nl': 'Netherlands', '.no': 'Norway', '.pl': 'Poland',
    '.pt': 'Portugal', '.ro': 'Romania', '.rs': 'Serbia', '.ru': 'Russia',
    '.se': 'Sweden', '.si': 'Slovenia', '.sk': 'Slovakia', '.sm': 'San Marino',
    '.tr': 'Turkey', '.ua': 'Ukraine', '.uk': 'United Kingdom', '.va': 'Vatican City',
    
    # Asian countries (China, Japan, Korea)
    '.cn': 'China', '.jp': 'Japan', '.kr': 'South Korea', '.kp': 'North Korea',
    '.hk': 'Hong Kong', '.mo': 'Macao', '.tw': 'Taiwan',
    
    # Special domains
    '.edu': 'United States', '.gov': 'United States', '.mil': 'United States'
}

# Special Chinese email domains mapping
chinese_domains = {
    '163.com': 'China', 'qq.com': 'China', '126.com': 'China', 'sina.com': 'China',
    'sohu.com': 'China', 'tom.com': 'China', 'aliyun.com': 'China', '21cn.com': 'China',
    'baidu.com': 'China', 'yeah.net': 'China', 'sogou.com': 'China', '163.net': 'China',
    'sina.net': 'China', 'chinaren.com': 'China'
}

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special Chinese domains first
    for domain, country in chinese_domains.items():
        if email.endswith(domain):
            return country

    # Check for country TLDs
    for tld, country in tld_to_country.items():
        if email.endswith(tld):
            return country

    return 'Other'

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 60, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("Asia-Europe Data Extraction Script")
rich_progress.print_status("Extracting data from China, Japan, Korea, and European countries", "info")

# Get the path from the user
print_section("Input Path")
path = input("Enter the directory path containing CSV files: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    csn = match.group(1)
    rich_progress.print_status(f"Found segment: {csn}", "success")
else:
    rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
    # Prompt for manual input
    csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
    if csn.strip():
        rich_progress.print_status(f"Using manually entered segment: {csn}", "info")
    else:
        rich_progress.print_status("No segment name provided. Exiting.", "error")
        exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Read and concatenate CSV files with progress bar
print_section("Reading CSV Files")
rich_progress.print_status(f"Reading {len(csv_files)} CSV files...", "info")

# Create a progress bar for reading CSV files
read_bar, update_read = rich_progress.create_progress_bar(
    total=len(csv_files),
    description="Reading CSV files",
    color_scheme="blue"
)

# Read each CSV file with progress tracking
dfs = []
for csv_file in csv_files:
    try:
        df = pd.read_csv(csv_file, low_memory=False)
        dfs.append(df)
        update_read(1, f"Read {csv_file}")
    except Exception as e:
        rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")
        update_read(1, f"Error with {csv_file}")

# Stop the progress bar
read_bar.stop()

# Concatenate all dataframes
rich_progress.print_status("Concatenating files...", "info")
d2_EVENT = pd.concat(dfs, ignore_index=True)
rich_progress.print_status(f"Successfully read {len(d2_EVENT)} records from {len(csv_files)} files", "success")

# Add Country column
print_section("Adding Country Column")
rich_progress.print_status("Extracting country information from email domains...", "info")
d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)
rich_progress.print_status(f"Successfully added Country column to {len(d2_EVENT)} records", "success")

# Define target countries and their TLDs
print_section("Defining Target Countries")

# European country TLDs
european_tlds = [
    '.ad', '.al', '.at', '.ba', '.be', '.bg', '.by', '.ch', '.cy', '.cz',
    '.de', '.dk', '.ee', '.es', '.eu', '.fi', '.fr', '.gb', '.gr', '.hr',
    '.hu', '.ie', '.is', '.it', '.li', '.lt', '.lu', '.lv', '.mc', '.md',
    '.me', '.mk', '.mt', '.nl', '.no', '.pl', '.pt', '.ro', '.rs', '.ru',
    '.se', '.si', '.sk', '.sm', '.tr', '.ua', '.uk', '.va'
]

# Asian target countries TLDs
asian_target_tlds = {
    'China': ['.cn', '.hk', '.mo', '.tw'],  # Including Hong Kong, Macao, Taiwan
    'Japan': ['.jp'],
    'South Korea': ['.kr'],
    'North Korea': ['.kp']  # Just in case
}

rich_progress.print_status(f"Targeting {len(european_tlds)} European TLDs", "info")
rich_progress.print_status(f"Targeting {sum(len(tlds) for tlds in asian_target_tlds.values())} Asian TLDs", "info")

# Create filters for target countries
print_section("Creating Country Filters")

# Initialize results dictionary
country_data = {}

# Filter China (including special domains)
print_section("Filtering China")
china_mask = pd.Series(False, index=d2_EVENT.index)

# Add .cn TLD
china_mask = china_mask | d2_EVENT["Email"].str.endswith('.cn', na=False)

# Add special Chinese domains
for domain in chinese_domains.keys():
    china_mask = china_mask | d2_EVENT["Email"].str.endswith(domain, na=False)

# Add Hong Kong, Macao, Taiwan
for tld in ['.hk', '.mo', '.tw']:
    china_mask = china_mask | d2_EVENT["Email"].str.endswith(tld, na=False)

country_data['China'] = d2_EVENT[china_mask]
rich_progress.print_status(f"China: {len(country_data['China'])} records", "success")

# Filter Japan
print_section("Filtering Japan")
japan_mask = d2_EVENT["Email"].str.endswith('.jp', na=False)
country_data['Japan'] = d2_EVENT[japan_mask]
rich_progress.print_status(f"Japan: {len(country_data['Japan'])} records", "success")

# Filter South Korea
print_section("Filtering South Korea")
korea_mask = d2_EVENT["Email"].str.endswith('.kr', na=False)
country_data['South_Korea'] = d2_EVENT[korea_mask]
rich_progress.print_status(f"South Korea: {len(country_data['South_Korea'])} records", "success")

# Filter European countries
print_section("Filtering European Countries")
europe_mask = pd.Series(False, index=d2_EVENT.index)

# Create a progress bar for European filtering
europe_bar, update_europe = rich_progress.create_progress_bar(
    total=len(european_tlds),
    description="Processing European TLDs",
    color_scheme="green"
)

for tld in european_tlds:
    europe_mask = europe_mask | d2_EVENT["Email"].str.endswith(tld, na=False)
    update_europe(1, f"Processed {tld}")

europe_bar.stop()

country_data['Europe'] = d2_EVENT[europe_mask]
rich_progress.print_status(f"Europe (combined): {len(country_data['Europe'])} records", "success")

# Filter individual European countries
print_section("Filtering Individual European Countries")
individual_european_countries = {}

# Create a progress bar for individual European country filtering
individual_bar, update_individual = rich_progress.create_progress_bar(
    total=len(european_tlds),
    description="Processing individual European countries",
    color_scheme="purple"
)

for tld in european_tlds:
    country_mask = d2_EVENT["Email"].str.endswith(tld, na=False)
    country_df = d2_EVENT[country_mask]

    if len(country_df) > 0:  # Only save countries with data
        country_name = tld_to_country.get(tld, tld.replace('.', '').upper())
        individual_european_countries[country_name] = country_df
        update_individual(1, f"Processed {country_name} ({len(country_df)} records)")
    else:
        update_individual(1, f"Skipped {tld} (no records)")

individual_bar.stop()

rich_progress.print_status(f"Found data for {len(individual_european_countries)} European countries", "success")

# Create output directories
print_section("Creating Output Directories")
base_output_dir = os.path.join(os.getcwd(), "asia_europe_extraction")
os.makedirs(base_output_dir, exist_ok=True)

# Create subdirectories
asia_dir = os.path.join(base_output_dir, "asia")
europe_dir = os.path.join(base_output_dir, "europe")
individual_europe_dir = os.path.join(base_output_dir, "individual_european_countries")

os.makedirs(asia_dir, exist_ok=True)
os.makedirs(europe_dir, exist_ok=True)
os.makedirs(individual_europe_dir, exist_ok=True)

rich_progress.print_status(f"Created output directories:", "info")
rich_progress.print_status(f"  - Asia: {asia_dir}", "info")
rich_progress.print_status(f"  - Europe: {europe_dir}", "info")
rich_progress.print_status(f"  - Individual European: {individual_europe_dir}", "info")

# Save files
print_section("Saving Extracted Data")

files_to_save = []

# Add Asian countries
files_to_save.append((country_data['China'], os.path.join(asia_dir, f"{csn}_China.csv"), "China"))
files_to_save.append((country_data['Japan'], os.path.join(asia_dir, f"{csn}_Japan.csv"), "Japan"))
files_to_save.append((country_data['South_Korea'], os.path.join(asia_dir, f"{csn}_South_Korea.csv"), "South Korea"))

# Add combined Europe
files_to_save.append((country_data['Europe'], os.path.join(europe_dir, f"{csn}_Europe_Combined.csv"), "Europe Combined"))

# Add individual European countries
for country_name, df in individual_european_countries.items():
    safe_name = country_name.replace(' ', '_').replace('&', 'and')
    filename = f"{csn}_{safe_name}.csv"
    files_to_save.append((df, os.path.join(individual_europe_dir, filename), country_name))

# Create a progress bar for saving files
save_bar, update_save = rich_progress.create_progress_bar(
    total=len(files_to_save),
    description="Saving files",
    color_scheme="green"
)

# Save each file with progress tracking
saved_files = 0
for df, filepath, display_name in files_to_save:
    try:
        if len(df) > 0:  # Only save non-empty dataframes
            df.to_csv(filepath, encoding='utf-8-sig', index=False)
            saved_files += 1
            update_save(1, f"Saved {display_name} ({len(df)} records)")
        else:
            update_save(1, f"Skipped {display_name} (no records)")
    except Exception as e:
        rich_progress.print_status(f"Error saving {display_name}: {str(e)}", "error")
        update_save(1, f"Error with {display_name}")

save_bar.stop()

# Print completion summary
print_header("Asia-Europe Data Extraction Completed!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")
rich_progress.print_status(f"Total files saved: {saved_files}", "success")

# Print detailed results
print_section("Extraction Results Summary")
rich_progress.print_status("-" * 60, "info")
rich_progress.print_status(f"{'Region/Country':<30} {'Records':>15} {'Status':>15}", "info")
rich_progress.print_status("-" * 60, "info")

# Asian countries
rich_progress.print_status(f"{'China':<30} {len(country_data['China']):>15} {'✓ Saved':>15}", "success")
rich_progress.print_status(f"{'Japan':<30} {len(country_data['Japan']):>15} {'✓ Saved':>15}", "success")
rich_progress.print_status(f"{'South Korea':<30} {len(country_data['South_Korea']):>15} {'✓ Saved':>15}", "success")

# Europe combined
rich_progress.print_status(f"{'Europe (Combined)':<30} {len(country_data['Europe']):>15} {'✓ Saved':>15}", "success")

rich_progress.print_status("-" * 60, "info")

# Top 10 individual European countries
print_section("Top 10 European Countries by Record Count")
european_sorted = sorted(individual_european_countries.items(), key=lambda x: len(x[1]), reverse=True)[:10]

rich_progress.print_status("-" * 50, "info")
rich_progress.print_status(f"{'Country':<30} {'Records':>15}", "info")
rich_progress.print_status("-" * 50, "info")

for country, df in european_sorted:
    rich_progress.print_status(f"{country:<30} {len(df):>15}", "info")

rich_progress.print_status("-" * 50, "info")

# Print directory information
print_section("Output Directory Structure")
rich_progress.print_status(f"Main output directory: {base_output_dir}", "info")
rich_progress.print_status("", "info")
rich_progress.print_status("Directory structure:", "info")
rich_progress.print_status(f"├── asia/", "info")
rich_progress.print_status(f"│   ├── {csn}_China.csv", "info")
rich_progress.print_status(f"│   ├── {csn}_Japan.csv", "info")
rich_progress.print_status(f"│   └── {csn}_South_Korea.csv", "info")
rich_progress.print_status(f"├── europe/", "info")
rich_progress.print_status(f"│   └── {csn}_Europe_Combined.csv", "info")
rich_progress.print_status(f"└── individual_european_countries/", "info")
rich_progress.print_status(f"    └── {len(individual_european_countries)} individual country files", "info")

# Final statistics
print_section("Final Statistics")
total_extracted = (len(country_data['China']) + len(country_data['Japan']) +
                  len(country_data['South_Korea']) + len(country_data['Europe']))

# Note: Europe combined includes overlapping records, so we calculate unique records
unique_records = len(pd.concat([
    country_data['China'],
    country_data['Japan'],
    country_data['South_Korea'],
    country_data['Europe']
]).drop_duplicates())

rich_progress.print_status(f"Total records extracted: {total_extracted}", "success")
rich_progress.print_status(f"Unique records (no duplicates): {unique_records}", "success")
rich_progress.print_status(f"Extraction percentage: {(unique_records/len(d2_EVENT)*100):.2f}%", "success")

rich_progress.print_status("\n🎉 Data extraction completed successfully!", "header")
rich_progress.print_status(f"📁 Check the output directory: {base_output_dir}", "info")

# Pause for user to see results
input("\nPress Enter to exit...")
