"""
Final Clean Magnus Group Data Extractor
Extracts and cleans all available data including Abstract Type, Track, etc.
"""

import json
import csv
from datetime import datetime
import re

def clean_and_extract_key_fields(input_csv):
    """Extract and clean the key fields from the enhanced CSV"""
    
    clean_records = []
    
    with open(input_csv, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            clean_record = {}
            
            # Basic identification
            clean_record['Serial_No'] = row.get('S.No', '').strip()
            clean_record['Record_ID'] = row.get('record_id', '').strip()
            
            # Personal information
            clean_record['First_Name'] = row.get('First Name', '').strip()
            clean_record['Full_Name'] = row.get('Full Name', '').strip()
            clean_record['Email_Address'] = row.get('Email Address', '').strip()
            clean_record['Alternate_Email'] = row.get('Alternate Email Address', '').strip()
            clean_record['Phone'] = row.get('Phone', '').strip()
            
            # Location
            country = row.get('Country', '').strip()
            if country and country != '-Please choose a country-':
                clean_record['Country'] = country
            else:
                clean_record['Country'] = ''
            
            # Abstract information
            clean_record['Abstract_Title'] = row.get('Abstract Title', '').strip()
            clean_record['Abstract_Type'] = row.get('Abstract Type', '').strip()
            clean_record['Track'] = row.get('Track', '').strip()
            
            # Extract submission date from Details field
            details = row.get('Details', '')
            date_match = re.search(r'Submitted On:([^$]+)', details)
            if date_match:
                clean_record['Submission_Date'] = date_match.group(1).strip()
            else:
                clean_record['Submission_Date'] = ''
            
            # Admin information
            clean_record['Admin_Notes'] = row.get('Notes', '').replace('Add Notes', '').strip()
            
            # Contact labels - extract key status indicators
            contact_labels = row.get('Contact Label', '')
            status_indicators = []
            
            key_statuses = [
                'Positive', 'Full Waiver', 'Unable to Attend', 'Confirmed OCM',
                'Abstract Submitted', 'Registered', 'Registration Tried'
            ]
            
            for status in key_statuses:
                if status in contact_labels:
                    status_indicators.append(status)
            
            clean_record['Status_Labels'] = '; '.join(status_indicators)
            
            # Extract download links from Actions
            actions = row.get('Actions', '')
            download_links = re.findall(r'https://[^\s]+\.(?:pdf|doc|docx)', actions)
            clean_record['Download_Links'] = '; '.join(download_links)
            
            clean_records.append(clean_record)
    
    return clean_records

def save_final_clean_data(clean_records):
    """Save the final clean data"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save to CSV
    csv_filename = f"magnus_abstracts_final_{timestamp}.csv"
    
    if clean_records:
        fieldnames = [
            'Serial_No', 'Record_ID', 'First_Name', 'Full_Name', 
            'Email_Address', 'Alternate_Email', 'Phone', 'Country',
            'Abstract_Title', 'Abstract_Type', 'Track', 'Submission_Date',
            'Admin_Notes', 'Status_Labels', 'Download_Links'
        ]
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(clean_records)
    
    # Save to JSON
    json_filename = f"magnus_abstracts_final_{timestamp}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(clean_records, f, indent=2, ensure_ascii=False)
    
    print(f"Final clean data saved to:")
    print(f"  - {csv_filename}")
    print(f"  - {json_filename}")
    
    return csv_filename, json_filename

def analyze_data(clean_records):
    """Analyze the extracted data"""
    print(f"\n=== DATA ANALYSIS ===")
    print(f"Total records: {len(clean_records)}")
    
    # Count records with different data
    with_email = sum(1 for r in clean_records if r.get('Email_Address'))
    with_name = sum(1 for r in clean_records if r.get('First_Name'))
    with_title = sum(1 for r in clean_records if r.get('Abstract_Title'))
    with_type = sum(1 for r in clean_records if r.get('Abstract_Type'))
    with_track = sum(1 for r in clean_records if r.get('Track'))
    with_country = sum(1 for r in clean_records if r.get('Country'))
    with_downloads = sum(1 for r in clean_records if r.get('Download_Links'))
    
    print(f"Records with email: {with_email}")
    print(f"Records with name: {with_name}")
    print(f"Records with abstract title: {with_title}")
    print(f"Records with abstract type: {with_type}")
    print(f"Records with track: {with_track}")
    print(f"Records with country: {with_country}")
    print(f"Records with download links: {with_downloads}")
    
    # Show abstract types
    abstract_types = [r.get('Abstract_Type') for r in clean_records if r.get('Abstract_Type')]
    unique_types = list(set(abstract_types))
    print(f"\nAbstract Types found:")
    for atype in unique_types:
        count = abstract_types.count(atype)
        print(f"  - {atype}: {count} records")
    
    # Show tracks
    tracks = [r.get('Track') for r in clean_records if r.get('Track')]
    unique_tracks = list(set(tracks))
    print(f"\nTracks found:")
    for track in unique_tracks:
        count = tracks.count(track)
        print(f"  - {track}: {count} records")

def main():
    print("=== FINAL CLEAN MAGNUS GROUP DATA EXTRACTOR ===")
    
    # Look for the enhanced CSV file
    import glob
    enhanced_files = glob.glob("enhanced_abstracts_*.csv")
    
    if not enhanced_files:
        print("No enhanced CSV files found. Please run enhanced_detail_extractor.py first.")
        return
    
    # Use the most recent file
    latest_file = max(enhanced_files, key=lambda x: x.split('_')[-1])
    print(f"Processing: {latest_file}")
    
    # Clean and extract key fields
    clean_records = clean_and_extract_key_fields(latest_file)
    
    # Save final clean data
    csv_file, json_file = save_final_clean_data(clean_records)
    
    # Analyze data
    analyze_data(clean_records)
    
    # Show sample record
    if clean_records:
        print(f"\n=== SAMPLE CLEAN RECORD ===")
        sample = clean_records[0]
        for key, value in sample.items():
            print(f"{key}: {value}")

if __name__ == "__main__":
    main()
