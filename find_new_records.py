#!/usr/bin/env python3
"""
New Records Finder Script
Compares CSV files between main directory and CSV_Backup directory
Removes duplicates and saves new records to "new_records" directory
"""

import os
import pandas as pd
import glob
from pathlib import Path
from datetime import datetime

def find_new_records():
    """Find new records by comparing main directory with backup directory."""
    
    # Define directories
    main_dir = r"H:\<PERSON> Bounces and Unsubs\<PERSON> Bounces and Unsubs"
    backup_dir = os.path.join(main_dir, "CSV_Backup")
    new_records_dir = os.path.join(main_dir, "new_records")
    
    # Check if directories exist
    if not os.path.exists(main_dir):
        print(f"Error: Main directory not found: {main_dir}")
        return False
    
    if not os.path.exists(backup_dir):
        print(f"Error: Backup directory not found: {backup_dir}")
        print("Please run the backup script first to create CSV_Backup directory.")
        return False
    
    # Create new_records directory
    os.makedirs(new_records_dir, exist_ok=True)
    print(f"Created/verified new_records directory: {new_records_dir}")
    
    # Get CSV files from main directory
    main_csv_files = glob.glob(os.path.join(main_dir, "*.csv"))
    
    if not main_csv_files:
        print("No CSV files found in main directory.")
        return False
    
    print(f"\nFound {len(main_csv_files)} CSV files in main directory")
    
    # Process each CSV file
    total_new_records = 0
    processed_files = 0
    
    for main_file in main_csv_files:
        file_name = os.path.basename(main_file)
        backup_file = os.path.join(backup_dir, file_name)
        
        print(f"\nProcessing: {file_name}")
        
        # Check if corresponding backup file exists
        if not os.path.exists(backup_file):
            print(f"  Warning: No backup file found for {file_name}")
            print(f"  Copying entire file as new records...")
            
            # Copy entire file to new_records directory
            try:
                df_main = pd.read_csv(main_file, encoding='utf-8-sig')
                new_records_file = os.path.join(new_records_dir, file_name)
                df_main.to_csv(new_records_file, index=False, encoding='utf-8-sig')
                print(f"  ✓ Saved {len(df_main)} records to new_records/{file_name}")
                total_new_records += len(df_main)
                processed_files += 1
            except Exception as e:
                print(f"  ✗ Error processing {file_name}: {str(e)}")
            continue
        
        try:
            # Read both files
            print(f"  Reading main file...")
            df_main = pd.read_csv(main_file, encoding='utf-8-sig')
            print(f"  Reading backup file...")
            df_backup = pd.read_csv(backup_file, encoding='utf-8-sig')
            
            print(f"  Main file records: {len(df_main)}")
            print(f"  Backup file records: {len(df_backup)}")
            
            # Check if files have the same columns
            if list(df_main.columns) != list(df_backup.columns):
                print(f"  Warning: Column mismatch between main and backup files")
                print(f"  Main columns: {list(df_main.columns)}")
                print(f"  Backup columns: {list(df_backup.columns)}")
                continue
            
            # Convert all columns to string and lowercase for comparison
            # This handles email comparisons properly
            for col in df_main.columns:
                df_main[col] = df_main[col].astype(str).str.lower().str.strip()
                df_backup[col] = df_backup[col].astype(str).str.lower().str.strip()
            
            # Remove duplicates within main file first
            df_main_clean = df_main.drop_duplicates()
            duplicates_in_main = len(df_main) - len(df_main_clean)
            if duplicates_in_main > 0:
                print(f"  Removed {duplicates_in_main} duplicates from main file")
            
            # Find new records (records in main that are not in backup)
            # Use all columns for comparison
            df_new = df_main_clean.merge(df_backup, how='left', indicator=True)
            df_new_records = df_new[df_new['_merge'] == 'left_only'].drop('_merge', axis=1)
            
            new_count = len(df_new_records)
            print(f"  New records found: {new_count}")
            
            if new_count > 0:
                # Save new records to new_records directory
                new_records_file = os.path.join(new_records_dir, file_name)
                df_new_records.to_csv(new_records_file, index=False, encoding='utf-8-sig')
                print(f"  ✓ Saved new records to new_records/{file_name}")
                total_new_records += new_count
            else:
                # Create empty CSV file with same headers when no new records found
                new_records_file = os.path.join(new_records_dir, file_name)
                empty_df = pd.DataFrame(columns=df_main.columns)
                empty_df.to_csv(new_records_file, index=False, encoding='utf-8-sig')
                print(f"  ✓ Created empty CSV file: new_records/{file_name} (no new records found)")
            
            processed_files += 1
            
        except Exception as e:
            print(f"  ✗ Error processing {file_name}: {str(e)}")
    
    # Summary
    print("\n" + "="*60)
    print("NEW RECORDS PROCESSING SUMMARY")
    print("="*60)
    print(f"Files processed: {processed_files}")
    print(f"Total new records found: {total_new_records}")
    print(f"New records saved to: {new_records_dir}")
    print("="*60)
    
    return True

def clean_new_records_directory():
    """Clean the new_records directory before processing."""
    main_dir = r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs"
    new_records_dir = os.path.join(main_dir, "new_records")
    
    if os.path.exists(new_records_dir):
        # Remove all CSV files from new_records directory
        existing_files = glob.glob(os.path.join(new_records_dir, "*.csv"))
        if existing_files:
            print(f"Cleaning {len(existing_files)} existing files from new_records directory...")
            for file in existing_files:
                try:
                    os.remove(file)
                    print(f"  Removed: {os.path.basename(file)}")
                except Exception as e:
                    print(f"  Error removing {os.path.basename(file)}: {str(e)}")

def main():
    """Main function."""
    print("="*60)
    print("NEW RECORDS FINDER")
    print("="*60)
    print("This script compares CSV files between main directory and CSV_Backup")
    print("and saves new records to the 'new_records' directory.")
    print("="*60)
    
    # Ask user if they want to clean existing new_records
    clean_choice = input("\nDo you want to clean existing files in new_records directory? (y/n): ").lower().strip()
    if clean_choice in ['y', 'yes']:
        clean_new_records_directory()
    
    print("\nStarting new records processing...")
    
    try:
        success = find_new_records()
        
        if success:
            print("\n" + "="*60)
            print("PROCESSING COMPLETED SUCCESSFULLY!")
            print("Check the 'new_records' directory for new records.")
            print("="*60)
        else:
            print("\n" + "="*60)
            print("PROCESSING FAILED - Check error messages above")
            print("="*60)
            
    except KeyboardInterrupt:
        print("\n\nProcessing interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
