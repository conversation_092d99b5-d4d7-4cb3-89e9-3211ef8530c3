@echo off
title Thematic Analyzer - Setup and Run
color 0A

echo.
echo ========================================
echo   THEMATIC ANALYZER - SETUP AND RUN
echo ========================================
echo.
echo This script will fix the package compatibility issues
echo and set up the environment properly.
echo.

echo Choose an option:
echo.
echo 1. Fix packages and run full analyzer (with pandas/numpy)
echo 2. Run simple analyzer (CSV only, no external packages)
echo 3. Exit
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto full_setup
if "%choice%"=="2" goto simple_run
if "%choice%"=="3" goto exit
goto invalid_choice

:full_setup
echo.
echo Running full environment setup...
python setup_environment.py
goto end

:simple_run
echo.
echo Running simple analyzer (CSV files only)...
echo This version doesn't require external packages.
python thematic_analyzer_simple.py
goto end

:invalid_choice
echo.
echo Invalid choice. Please enter 1, 2, or 3.
pause
goto start

:exit
echo.
echo Exiting...
goto end

:end
echo.
pause
