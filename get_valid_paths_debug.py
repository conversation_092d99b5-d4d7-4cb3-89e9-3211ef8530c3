import os
import sys

def get_paths_with_valid_folder(root_dir: str, relevant_extensions: list = None, max_depth: int = None, exclude_dirs: list = None, require_files: bool = True, debug: bool = False) -> list[str]:
    """
    Find all subdirectories that contain a 'valid' folder and return their paths.

    Args:
        root_dir (str): Root directory to start the search from
        relevant_extensions (list, optional): List of file extensions to consider (e.g., ['.csv', '.xlsx'])
        max_depth (int, optional): Maximum directory depth to search
        exclude_dirs (list, optional): List of directory names to exclude from search
        require_files (bool, optional): Whether to require relevant files to be present
        debug (bool, optional): Whether to print debug information

    Returns:
        list[str]: List of directory paths containing 'valid' folder
    """
    valid_paths = []
    
    # Set defaults
    if relevant_extensions is None:
        relevant_extensions = ['.csv', '.xlsx', '.xls']  # Default to common data file types
    if exclude_dirs is None:
        # Don't exclude any directories by default in debug mode
        exclude_dirs = []
    
    if debug:
        print(f"\nDEBUG: Starting search in {root_dir}")
        print(f"DEBUG: Extensions: {relevant_extensions}")
        print(f"DEBUG: Excluded dirs: {exclude_dirs}")
        print(f"DEBUG: Require files: {require_files}")
    
    # Walk through directory tree
    for dirpath, dirnames, filenames in os.walk(root_dir):
        if debug:
            print(f"\nDEBUG: Checking directory: {dirpath}")
            print(f"DEBUG: Contains subdirectories: {dirnames}")
            print(f"DEBUG: Contains files: {len(filenames) > 0}")
        
        # Skip the root directory itself
        if dirpath == root_dir:
            if debug:
                print("DEBUG: Skipping root directory")
            continue
        
        # Check depth if max_depth is specified
        if max_depth is not None:
            # Calculate current depth by counting path separators
            current_depth = dirpath.replace(root_dir, '').count(os.sep)
            if current_depth > max_depth:
                if debug:
                    print(f"DEBUG: Skipping - exceeds max depth {current_depth} > {max_depth}")
                # Remove directories from dirnames to prevent further traversal
                dirnames[:] = []
                continue
        
        # Skip excluded directories - only if the directory name exactly matches an excluded name
        # Get the current directory name (not the full path)
        current_dir_name = os.path.basename(dirpath).lower()
        if current_dir_name in [d.lower() for d in exclude_dirs]:
            if debug:
                print(f"DEBUG: Skipping excluded directory: {current_dir_name}")
            continue
        
        # Check if 'valid' is in the list of subdirectories
        if 'valid' in dirnames:
            if debug:
                print(f"DEBUG: Found 'valid' folder in {dirpath}")
            
            # If it has a valid folder, we'll check for files
            has_relevant_files = False
            
            # If no extensions specified, no files required, or require_files is False, consider it valid
            if not require_files or not relevant_extensions or len(relevant_extensions) == 0:
                has_relevant_files = True
                if debug:
                    print("DEBUG: No file check required, considering valid")
            else:
                # Check parent directory for relevant files
                for filename in filenames:
                    if any(filename.lower().endswith(ext) for ext in relevant_extensions):
                        has_relevant_files = True
                        if debug:
                            print(f"DEBUG: Found relevant file in parent dir: {filename}")
                        break
                
                # If no relevant files found in current directory, check the valid subdirectory
                if not has_relevant_files:
                    valid_dir_path = os.path.join(dirpath, 'valid')
                    if os.path.isdir(valid_dir_path):
                        try:
                            valid_dir_files = os.listdir(valid_dir_path)
                            if debug:
                                print(f"DEBUG: Checking files in valid dir: {valid_dir_files}")
                            for filename in valid_dir_files:
                                if os.path.isfile(os.path.join(valid_dir_path, filename)) and any(filename.lower().endswith(ext) for ext in relevant_extensions):
                                    has_relevant_files = True
                                    if debug:
                                        print(f"DEBUG: Found relevant file in valid dir: {filename}")
                                    break
                        except (PermissionError, OSError) as e:
                            # If we can't access the directory, assume it might have relevant files
                            has_relevant_files = True
                            if debug:
                                print(f"DEBUG: Error accessing valid dir: {str(e)}")
            
            # If we have a valid folder and either have relevant files or no file check is needed
            if has_relevant_files:
                # Get the full path including the 'valid' folder
                valid_folder_path = os.path.join(dirpath, 'valid')
                
                # Add the path if it's not already in our list
                if valid_folder_path not in valid_paths:
                    if debug:
                        print(f"DEBUG: Adding path to results: {valid_folder_path}")
                    valid_paths.append(valid_folder_path)
    
    if debug:
        print(f"\nDEBUG: Search complete. Found {len(valid_paths)} valid paths.")
    
    return valid_paths

def verify_paths(paths: list[str], debug: bool = False) -> list[str]:
    """
    Verify that paths exist and are valid directories.

    Args:
        paths (list[str]): List of paths to verify
        debug (bool, optional): Whether to print debug information

    Returns:
        list[str]: List of verified paths
    """
    verified_paths = []
    for path in paths:
        # Check if the path exists and is a directory
        if os.path.isdir(path):
            verified_paths.append(path)
            if debug:
                print(f"DEBUG: Verified path exists: {path}")
        else:
            if debug:
                print(f"DEBUG: Path does not exist: {path}")
    return verified_paths

def save_paths_to_file(paths: list[str], root_dir: str, no_valid_paths: bool = False):
    """
    Save the list of paths to dirs.txt in the root directory.

    Args:
        paths (list[str]): List of paths to save
        root_dir (str): Root directory where dirs.txt will be saved
        no_valid_paths (bool): Whether no directories with valid folders were found
    """
    output_file = os.path.join(root_dir, "dirs.txt")
    with open(output_file, 'w', encoding='utf-8') as f:
        if no_valid_paths:
            f.write("No directories with 'valid' folder found.\n")
        else:
            for path in paths:
                f.write(f"{path}\n")
    return output_file

def print_status(message: str, count: int = None):
    """
    Print status message with optional count.

    Args:
        message (str): Status message to print
        count (int, optional): Count to append to message
    """
    if count is not None:
        print(f"{message}: {count}")
    else:
        print(message)

def get_country_folders(root_dir: str, relevant_extensions: list = None, debug: bool = False) -> list[str]:
    """
    Find all country folders that contain files with specified extensions.

    Args:
        root_dir (str): Root directory to start the search from
        relevant_extensions (list, optional): List of file extensions to look for
        debug (bool, optional): Whether to print debug information

    Returns:
        list[str]: List of country folder paths containing relevant files
    """
    # List of known countries to search for
    countries = [
        "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Argentina", "Armenia", "Australia",
        "Austria", "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium",
        "Belize", "Benin", "Bhutan", "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei",
        "Bulgaria", "Burkina Faso", "Burundi", "Cambodia", "Cameroon", "Canada", "Chad", "Chile", "China",
        "Colombia", "Comoros", "Democratic Republic of the Congo", "Republic of the Congo", "Croatia",
        "Cuba", "Cyprus", "Czech Republic", "Denmark", "Djibouti", "Dominica", "Dominican Republic",
        "Ecuador", "Egypt", "Eritrea", "Estonia", "Eswatini", "Ethiopia", "Fiji", "Finland", "France",
        "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala", "Guinea",
        "Guinea-Bissau", "Guyana", "Haiti", "Honduras", "Hungary", "Iceland", "India", "Indonesia", "Iran",
        "Iraq", "Ireland", "Israel", "Italy", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati",
        "North Korea", "South Korea", "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon",
        "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg", "Madagascar", "Malawi",
        "Malaysia", "Maldives", "Mali", "Malta", "Mauritania", "Mauritius", "Mexico", "Micronesia",
        "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", "Myanmar", "Namibia",
        "Nauru", "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger", "Nigeria", "North Macedonia",
        "Norway", "Oman", "Pakistan", "Palau", "Palestine", "Panama", "Papua New Guinea", "Paraguay",
        "Peru", "Philippines", "Poland", "Portugal", "Qatar", "Romania", "Russia", "Rwanda", "Samoa",
        "San Marino", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore",
        "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa", "South Sudan", "Spain",
        "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland", "Syria", "Taiwan", "Tajikistan",
        "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tonga", "Trinidad and Tobago", "Tunisia",
        "Turkey", "Turkmenistan", "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom",
        "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City", "Venezuela", "Vietnam",
        "Yemen", "Zambia", "Zimbabwe"
    ]

    country_paths = []

    # Set defaults
    if relevant_extensions is None:
        relevant_extensions = ['.txt', '.csv', '.xlsx', '.xls']  # Default to common data file types

    if debug:
        print(f"\nDEBUG: Starting country folder search in {root_dir}")
        print(f"DEBUG: Extensions: {relevant_extensions}")
        print(f"DEBUG: Looking for countries: {len(countries)} countries")

    # Walk through directory tree
    for dirpath, dirnames, filenames in os.walk(root_dir):
        if debug:
            print(f"\nDEBUG: Checking directory: {dirpath}")

        # Get the current directory name
        current_dir_name = os.path.basename(dirpath)

        # Check if current directory name matches any country (case-insensitive)
        matched_country = None
        for country in countries:
            if current_dir_name.lower() == country.lower():
                matched_country = country
                break

        if matched_country:
            if debug:
                print(f"DEBUG: Found potential country folder: {current_dir_name} -> {matched_country}")

            # Check if this directory contains files with relevant extensions
            has_relevant_files = False
            relevant_files = []

            for filename in filenames:
                if any(filename.lower().endswith(ext.lower()) for ext in relevant_extensions):
                    has_relevant_files = True
                    relevant_files.append(filename)
                    if debug:
                        print(f"DEBUG: Found relevant file: {filename}")

            if has_relevant_files:
                country_paths.append(dirpath)
                if debug:
                    print(f"DEBUG: Added country path: {dirpath}")
                    print(f"DEBUG: Files found: {relevant_files}")
            elif debug:
                print(f"DEBUG: Country folder found but no relevant files: {dirpath}")

    if debug:
        print(f"\nDEBUG: Country folder search complete. Found {len(country_paths)} country folders.")

    return country_paths

def check_specific_path(path: str):
    """
    Check if a specific path exists and contains a 'valid' folder.

    Args:
        path (str): Path to check
    """
    print(f"\nChecking specific path: {path}")

    if not os.path.isdir(path):
        print(f"ERROR: The path does not exist: {path}")
        return

    print(f"Path exists: {path}")

    # List contents of the directory
    try:
        contents = os.listdir(path)
        print(f"Directory contents: {contents}")

        # Check for 'valid' folder
        if 'valid' in contents:
            valid_path = os.path.join(path, 'valid')
            if os.path.isdir(valid_path):
                print(f"FOUND: 'valid' folder exists at {valid_path}")

                # Check contents of valid folder
                try:
                    valid_contents = os.listdir(valid_path)
                    print(f"'valid' folder contents: {valid_contents}")
                except (PermissionError, OSError) as e:
                    print(f"ERROR: Could not access 'valid' folder: {str(e)}")
            else:
                print(f"ERROR: 'valid' exists but is not a directory")
        else:
            print(f"ERROR: No 'valid' folder found in {path}")

            # Check subdirectories
            subdirs = [d for d in contents if os.path.isdir(os.path.join(path, d))]
            if subdirs:
                print(f"Subdirectories found: {subdirs}")
                print("Checking first level of subdirectories for 'valid' folder...")

                for subdir in subdirs:
                    subdir_path = os.path.join(path, subdir)
                    try:
                        subdir_contents = os.listdir(subdir_path)
                        if 'valid' in subdir_contents:
                            print(f"FOUND: 'valid' folder exists in subdirectory: {os.path.join(subdir_path, 'valid')}")
                    except (PermissionError, OSError):
                        print(f"ERROR: Could not access subdirectory: {subdir_path}")

    except (PermissionError, OSError) as e:
        print(f"ERROR: Could not list directory contents: {str(e)}")

def save_country_paths_to_file(paths: list[str], root_dir: str, no_country_paths: bool = False):
    """
    Save the list of country folder paths to country_dirs.txt in the root directory.

    Args:
        paths (list[str]): List of country folder paths to save
        root_dir (str): Root directory where country_dirs.txt will be saved
        no_country_paths (bool): Whether no country folders were found
    """
    output_file = os.path.join(root_dir, "country_dirs.txt")
    with open(output_file, 'w', encoding='utf-8') as f:
        if no_country_paths:
            f.write("No country folders found.\n")
        else:
            for path in paths:
                f.write(f"{path}\n")
    return output_file

if __name__ == "__main__":
    # Get the root directory from user
    root_directory = input("Enter the root directory path: ").strip('"')  # Remove quotes if present

    # Check if we should run in debug mode
    debug_mode = input("Run in debug mode? (y/n): ").strip().lower() == 'y'

    # Choose search mode
    print("\nChoose search mode:")
    print("1. Find 'valid' folders (original functionality)")
    print("2. Find country folders (new functionality)")
    print("3. Find both 'valid' and country folders")

    while True:
        search_mode = input("Enter your choice (1, 2, or 3): ").strip()
        if search_mode in ['1', '2', '3']:
            break
        print("❌ Invalid choice. Please enter 1, 2, or 3.")

    # Check specific path first
    if debug_mode:
        specific_path = input("Enter a specific path to check (leave blank to skip): ").strip('"')
        if specific_path:
            check_specific_path(specific_path)

    # Ask for additional filtering options
    use_advanced = input("Use advanced filtering options? (y/n): ").strip().lower() == 'y'
    
    max_depth = None
    relevant_extensions = ['.csv', '.xlsx', '.xls'] if search_mode == '1' else ['.txt', '.csv', '.xlsx', '.xls']
    # Don't exclude any directories by default in debug mode
    exclude_dirs = [] if debug_mode else ['invalid', 'output', 'process', 'splits']
    require_files = False  # Default to not requiring files in debug mode

    if use_advanced:
        # Ask for max depth (only for valid folder search)
        if search_mode in ['1', '3']:
            depth_input = input("Enter maximum directory depth (leave blank for unlimited): ").strip()
            if depth_input and depth_input.isdigit():
                max_depth = int(depth_input)

        # Ask for file extensions
        default_ext = "csv,xlsx,xls" if search_mode == '1' else "txt,csv,xlsx,xls"
        ext_input = input(f"Enter file extensions to look for (comma-separated, default: {default_ext}): ").strip()
        if ext_input:
            relevant_extensions = [ext.strip() if ext.strip().startswith('.') else f'.{ext.strip()}' for ext in ext_input.split(',')]

        # Ask for directories to exclude (only for valid folder search)
        if search_mode in ['1', '3']:
            exclude_input = input(f"Enter directories to exclude (comma-separated, default: {','.join(exclude_dirs)}): ").strip()
            if exclude_input:
                exclude_dirs = [dir.strip() for dir in exclude_input.split(',')]

        # Ask whether to require files (only for valid folder search)
        if search_mode in ['1', '3']:
            require_files_input = input("Require relevant files in directories? (y/n, default: n): ").strip().lower()
            if require_files_input == 'y':
                require_files = True

    # Process based on search mode
    if search_mode == '1':
        # Find 'valid' folders only
        print_status("Searching for 'valid' folders...")
        print_status(f"Using filters: max_depth={max_depth}, extensions={relevant_extensions}, exclude={exclude_dirs}, require_files={require_files}")

        valid_paths = get_paths_with_valid_folder(
            root_directory,
            relevant_extensions=relevant_extensions,
            max_depth=max_depth,
            exclude_dirs=exclude_dirs,
            require_files=require_files,
            debug=debug_mode
        )

        verified_paths = verify_paths(valid_paths, debug=debug_mode)
        if len(verified_paths) != len(valid_paths):
            print_status(f"Warning: {len(valid_paths) - len(verified_paths)} paths were invalid and removed")

        output_file = save_paths_to_file(verified_paths, root_directory, no_valid_paths=(len(verified_paths) == 0))

        if verified_paths:
            verified_paths.sort()
            print_status("\nFound 'valid' folders", len(verified_paths))
            print_status(f"Full paths have been saved to: {output_file}")
            print_status("\nExample paths:")
            for path in verified_paths[:5]:
                print(f"- {path}")
            if len(verified_paths) > 5:
                print("...")
        else:
            print_status("\nNo 'valid' folders found")
            print_status(f"Status saved to: {output_file}")

    elif search_mode == '2':
        # Find country folders only
        print_status("Searching for country folders...")
        print_status(f"Using extensions: {relevant_extensions}")

        country_paths = get_country_folders(
            root_directory,
            relevant_extensions=relevant_extensions,
            debug=debug_mode
        )

        verified_country_paths = verify_paths(country_paths, debug=debug_mode)
        if len(verified_country_paths) != len(country_paths):
            print_status(f"Warning: {len(country_paths) - len(verified_country_paths)} paths were invalid and removed")

        output_file = save_country_paths_to_file(verified_country_paths, root_directory, no_country_paths=(len(verified_country_paths) == 0))

        if verified_country_paths:
            verified_country_paths.sort()
            print_status("\nFound country folders", len(verified_country_paths))
            print_status(f"Full paths have been saved to: {output_file}")
            print_status("\nExample paths:")
            for path in verified_country_paths[:5]:
                print(f"- {path}")
            if len(verified_country_paths) > 5:
                print("...")
        else:
            print_status("\nNo country folders found")
            print_status(f"Status saved to: {output_file}")

    elif search_mode == '3':
        # Find both 'valid' and country folders
        print_status("Searching for both 'valid' and country folders...")
        print_status(f"Using filters: max_depth={max_depth}, extensions={relevant_extensions}, exclude={exclude_dirs}, require_files={require_files}")

        # Find valid folders
        valid_paths = get_paths_with_valid_folder(
            root_directory,
            relevant_extensions=relevant_extensions,
            max_depth=max_depth,
            exclude_dirs=exclude_dirs,
            require_files=require_files,
            debug=debug_mode
        )

        # Find country folders
        country_paths = get_country_folders(
            root_directory,
            relevant_extensions=relevant_extensions,
            debug=debug_mode
        )

        # Verify paths
        verified_valid_paths = verify_paths(valid_paths, debug=debug_mode)
        verified_country_paths = verify_paths(country_paths, debug=debug_mode)

        # Save both types
        valid_output_file = save_paths_to_file(verified_valid_paths, root_directory, no_valid_paths=(len(verified_valid_paths) == 0))
        country_output_file = save_country_paths_to_file(verified_country_paths, root_directory, no_country_paths=(len(verified_country_paths) == 0))

        # Display results
        print_status(f"\nFound 'valid' folders: {len(verified_valid_paths)}")
        print_status(f"Found country folders: {len(verified_country_paths)}")
        print_status(f"Valid folder paths saved to: {valid_output_file}")
        print_status(f"Country folder paths saved to: {country_output_file}")

        if verified_valid_paths:
            print_status("\nExample 'valid' folder paths:")
            for path in verified_valid_paths[:3]:
                print(f"- {path}")

        if verified_country_paths:
            print_status("\nExample country folder paths:")
            for path in verified_country_paths[:3]:
                print(f"- {path}")
