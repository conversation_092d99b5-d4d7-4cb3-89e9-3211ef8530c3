"""
Google Sheets Examples - Practical examples of using GoogleSheetsManager
"""

import pandas as pd
import numpy as np
from google_sheets_manager import GoogleSheetsManager
import os
from datetime import datetime

def example_1_basic_read_write():
    """
    Example 1: Basic read and write operations
    """
    print("\n📊 EXAMPLE 1: Basic Read and Write Operations")
    print("=" * 50)
    
    # Initialize the manager
    gsm = GoogleSheetsManager('service_account')
    
    # Open an existing sheet
    sheet_url = input("Enter Google Sheet URL: ")
    worksheet = gsm.open_sheet(sheet_url)
    
    # Read data
    df = gsm.read_sheet_to_dataframe(worksheet)
    print(f"Current data shape: {df.shape}")
    print(df.head())
    
    # Modify data
    df['Last_Updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Write back to sheet
    gsm.write_dataframe_to_sheet(worksheet, df)
    print("✅ Data updated successfully!")

def example_2_create_new_sheet_with_data():
    """
    Example 2: Create a new sheet and populate with data
    """
    print("\n📊 EXAMPLE 2: Create New Sheet with Data")
    print("=" * 50)
    
    # Initialize the manager
    gsm = GoogleSheetsManager('service_account')
    
    # Create sample data
    data = {
        'Name': ['<PERSON> Doe', 'Jane Smith', 'Bob Johnson', 'Alice <PERSON>'],
        'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Age': [25, 30, 35, 28],
        'Department': ['IT', 'HR', 'Finance', 'Marketing'],
        'Salary': [50000, 60000, 70000, 55000]
    }
    df = pd.DataFrame(data)
    
    # Create new sheet
    sheet_title = f"Employee_Data_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    new_sheet = gsm.create_new_sheet(sheet_title)
    
    # Write data to the new sheet
    worksheet = new_sheet.sheet1
    gsm.write_dataframe_to_sheet(worksheet, df)
    
    # Share with someone (optional)
    email = input("Enter email to share with (or press Enter to skip): ")
    if email:
        gsm.share_sheet(new_sheet, email, 'writer')
    
    print(f"✅ New sheet created: {new_sheet.url}")

def example_3_append_data():
    """
    Example 3: Append new data to existing sheet
    """
    print("\n📊 EXAMPLE 3: Append Data to Existing Sheet")
    print("=" * 50)
    
    # Initialize the manager
    gsm = GoogleSheetsManager('service_account')
    
    # Open existing sheet
    sheet_url = input("Enter Google Sheet URL: ")
    worksheet = gsm.open_sheet(sheet_url)
    
    # Create new data to append
    new_data = {
        'Name': ['New Employee 1', 'New Employee 2'],
        'Email': ['<EMAIL>', '<EMAIL>'],
        'Age': [26, 32],
        'Department': ['IT', 'Sales'],
        'Salary': [52000, 58000]
    }
    new_df = pd.DataFrame(new_data)
    
    # Append data
    gsm.append_dataframe_to_sheet(worksheet, new_df, include_headers=False)
    print("✅ New data appended successfully!")

def example_4_update_specific_cells():
    """
    Example 4: Update specific cells and ranges
    """
    print("\n📊 EXAMPLE 4: Update Specific Cells and Ranges")
    print("=" * 50)
    
    # Initialize the manager
    gsm = GoogleSheetsManager('service_account')
    
    # Open existing sheet
    sheet_url = input("Enter Google Sheet URL: ")
    worksheet = gsm.open_sheet(sheet_url)
    
    # Update a specific cell
    gsm.update_cell(worksheet, 1, 1, "Updated Header")
    
    # Update a range
    range_values = [
        ['Status', 'Active'],
        ['Last_Check', datetime.now().strftime('%Y-%m-%d')],
        ['Version', '2.0']
    ]
    gsm.update_range(worksheet, 'G1:H3', range_values)
    
    print("✅ Specific cells and ranges updated!")

def example_5_process_csv_to_sheets():
    """
    Example 5: Process local CSV files and upload to Google Sheets
    """
    print("\n📊 EXAMPLE 5: Process CSV Files to Google Sheets")
    print("=" * 50)
    
    # Initialize the manager
    gsm = GoogleSheetsManager('service_account')
    
    # Get CSV files from current directory
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
    
    if not csv_files:
        print("No CSV files found in current directory")
        return
    
    print(f"Found {len(csv_files)} CSV files:")
    for i, file in enumerate(csv_files, 1):
        print(f"{i}. {file}")
    
    # Select file
    choice = int(input("Select file number: ")) - 1
    selected_file = csv_files[choice]
    
    # Read CSV
    df = pd.read_csv(selected_file, encoding='utf-8-sig')
    print(f"Loaded {len(df)} rows from {selected_file}")
    
    # Create new sheet
    sheet_name = f"CSV_Import_{os.path.splitext(selected_file)[0]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    new_sheet = gsm.create_new_sheet(sheet_name)
    
    # Upload data
    worksheet = new_sheet.sheet1
    gsm.write_dataframe_to_sheet(worksheet, df)
    
    print(f"✅ CSV data uploaded to: {new_sheet.url}")

def example_6_batch_operations():
    """
    Example 6: Batch operations on multiple sheets
    """
    print("\n📊 EXAMPLE 6: Batch Operations on Multiple Sheets")
    print("=" * 50)
    
    # Initialize the manager
    gsm = GoogleSheetsManager('service_account')
    
    # List of sheet URLs or names
    sheet_urls = []
    print("Enter Google Sheet URLs (press Enter when done):")
    while True:
        url = input("Sheet URL: ")
        if not url:
            break
        sheet_urls.append(url)
    
    if not sheet_urls:
        print("No sheets provided")
        return
    
    # Process each sheet
    all_data = []
    for i, url in enumerate(sheet_urls, 1):
        try:
            print(f"\nProcessing sheet {i}/{len(sheet_urls)}")
            worksheet = gsm.open_sheet(url)
            df = gsm.read_sheet_to_dataframe(worksheet)
            
            # Add source information
            df['Source_Sheet'] = f"Sheet_{i}"
            df['Processed_Date'] = datetime.now().strftime('%Y-%m-%d')
            
            all_data.append(df)
            
        except Exception as e:
            print(f"❌ Error processing sheet {i}: {str(e)}")
    
    if all_data:
        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # Create summary sheet
        summary_sheet = gsm.create_new_sheet(f"Combined_Data_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        gsm.write_dataframe_to_sheet(summary_sheet.sheet1, combined_df)
        
        print(f"✅ Combined data from {len(sheet_urls)} sheets")
        print(f"   Total rows: {len(combined_df)}")
        print(f"   Summary sheet: {summary_sheet.url}")

def example_7_email_list_management():
    """
    Example 7: Email list management (similar to your existing scripts)
    """
    print("\n📊 EXAMPLE 7: Email List Management")
    print("=" * 50)
    
    # Initialize the manager
    gsm = GoogleSheetsManager('service_account')
    
    # Create sample email data
    email_data = {
        'Author Name': ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown', 'Charlie Wilson'],
        'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Email status': ['valid', 'valid', 'invalid', 'valid', 'bounce'],
        'Conference': ['AI-2024', 'ML-2024', 'AI-2024', 'Data-2024', 'ML-2024'],
        'Registration_Date': ['2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18', '2024-01-19']
    }
    df = pd.DataFrame(email_data)
    
    # Create email management sheet
    sheet_name = f"Email_Management_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    new_sheet = gsm.create_new_sheet(sheet_name)
    worksheet = new_sheet.sheet1
    
    # Write data
    gsm.write_dataframe_to_sheet(worksheet, df)
    
    # Add summary statistics
    summary_data = [
        ['Summary Statistics', ''],
        ['Total Emails', len(df)],
        ['Valid Emails', len(df[df['Email status'] == 'valid'])],
        ['Invalid Emails', len(df[df['Email status'] == 'invalid'])],
        ['Bounced Emails', len(df[df['Email status'] == 'bounce'])],
        ['', ''],
        ['By Conference', ''],
    ]
    
    # Add conference breakdown
    conference_counts = df['Conference'].value_counts()
    for conf, count in conference_counts.items():
        summary_data.append([conf, count])
    
    # Update summary range
    gsm.update_range(worksheet, f'H1:I{len(summary_data)}', summary_data)
    
    print(f"✅ Email management sheet created: {new_sheet.url}")

def main():
    """
    Main function to run examples
    """
    print("🚀 GOOGLE SHEETS MANAGER EXAMPLES")
    print("=" * 40)
    
    examples = [
        ("Basic Read and Write Operations", example_1_basic_read_write),
        ("Create New Sheet with Data", example_2_create_new_sheet_with_data),
        ("Append Data to Existing Sheet", example_3_append_data),
        ("Update Specific Cells and Ranges", example_4_update_specific_cells),
        ("Process CSV Files to Google Sheets", example_5_process_csv_to_sheets),
        ("Batch Operations on Multiple Sheets", example_6_batch_operations),
        ("Email List Management", example_7_email_list_management),
    ]
    
    print("\nAvailable Examples:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    
    try:
        choice = int(input("\nSelect example number (1-7): ")) - 1
        if 0 <= choice < len(examples):
            examples[choice][1]()
        else:
            print("Invalid choice")
    except ValueError:
        print("Please enter a valid number")
    except Exception as e:
        print(f"❌ Error running example: {str(e)}")

if __name__ == "__main__":
    main()
