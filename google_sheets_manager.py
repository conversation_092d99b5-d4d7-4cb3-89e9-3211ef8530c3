"""
Google Sheets Manager - Access and modify Google Sheets from Python
Simple OAuth2 authentication using your Google account
"""

import gspread
import pandas as pd
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
import os
import json
import pickle
from typing import List, Dict, Any, Optional
import time

class GoogleSheetsManager:
    """
    A comprehensive class to manage Google Sheets operations using your Google account
    """

    def __init__(self, credentials_file=None):
        """
        Initialize the Google Sheets Manager

        Args:
            credentials_file (str): Path to OAuth2 credentials JSON file (optional)
        """
        self.credentials_file = credentials_file or 'credentials.json'
        self.token_file = 'token.pickle'
        self.gc = None
        self.scopes = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]

        self._authenticate()

    def _authenticate(self):
        """Authenticate with Google Sheets API using OAuth2"""
        try:
            creds = None

            # Check if we have saved credentials
            if os.path.exists(self.token_file):
                with open(self.token_file, 'rb') as token:
                    creds = pickle.load(token)

            # If there are no valid credentials, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    print("🔄 Refreshing expired credentials...")
                    creds.refresh(Request())
                else:
                    print("🔐 Getting new credentials...")
                    if not os.path.exists(self.credentials_file):
                        self._create_simple_credentials()

                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_file, self.scopes
                    )
                    creds = flow.run_local_server(port=0)

                # Save credentials for next time
                with open(self.token_file, 'wb') as token:
                    pickle.dump(creds, token)

            self.gc = gspread.authorize(creds)
            print("✅ Successfully authenticated with Google Sheets!")

        except Exception as e:
            print(f"❌ Authentication failed: {str(e)}")
            print("\n💡 Try running the simple setup first:")
            print("   python simple_google_setup.py")
            raise

    def _create_simple_credentials(self):
        """Create a simple credentials file for OAuth2"""
        print("\n🔧 SIMPLE GOOGLE SHEETS SETUP")
        print("=" * 40)
        print("We need to create OAuth2 credentials for your Google account.")
        print("This is much simpler than the service account method!")
        print("\nPlease follow these steps:")
        print("1. Go to: https://console.developers.google.com/")
        print("2. Create a new project or select existing")
        print("3. Enable Google Sheets API and Google Drive API")
        print("4. Create OAuth 2.0 Client ID (Desktop application)")
        print("5. Download the JSON file and save as 'credentials.json'")
        print("\nOr run: python simple_google_setup.py")

        raise FileNotFoundError(f"Credentials file not found: {self.credentials_file}")
    
    def open_sheet(self, sheet_name_or_url: str, worksheet_name: str = None):
        """
        Open a Google Sheet
        
        Args:
            sheet_name_or_url (str): Sheet name or URL
            worksheet_name (str): Specific worksheet name (optional)
        
        Returns:
            gspread.Worksheet: The worksheet object
        """
        try:
            # Try to open by URL first, then by name
            if 'docs.google.com' in sheet_name_or_url:
                sheet = self.gc.open_by_url(sheet_name_or_url)
            else:
                sheet = self.gc.open(sheet_name_or_url)
            
            if worksheet_name:
                worksheet = sheet.worksheet(worksheet_name)
            else:
                worksheet = sheet.sheet1  # Default to first worksheet
            
            print(f"✅ Opened sheet: {sheet.title}")
            if worksheet_name:
                print(f"   Worksheet: {worksheet_name}")
            
            return worksheet
            
        except Exception as e:
            print(f"❌ Error opening sheet: {str(e)}")
            raise
    
    def read_sheet_to_dataframe(self, worksheet) -> pd.DataFrame:
        """
        Read worksheet data into a pandas DataFrame
        
        Args:
            worksheet: gspread.Worksheet object
        
        Returns:
            pd.DataFrame: The sheet data
        """
        try:
            data = worksheet.get_all_records()
            df = pd.DataFrame(data)
            print(f"✅ Read {len(df)} rows and {len(df.columns)} columns")
            return df
            
        except Exception as e:
            print(f"❌ Error reading sheet: {str(e)}")
            raise
    
    def write_dataframe_to_sheet(self, worksheet, df: pd.DataFrame, 
                                include_index: bool = False, 
                                clear_sheet: bool = True):
        """
        Write a pandas DataFrame to a worksheet
        
        Args:
            worksheet: gspread.Worksheet object
            df (pd.DataFrame): Data to write
            include_index (bool): Include DataFrame index
            clear_sheet (bool): Clear existing data first
        """
        try:
            if clear_sheet:
                worksheet.clear()
            
            # Prepare data
            if include_index:
                df_to_write = df.reset_index()
            else:
                df_to_write = df.copy()
            
            # Convert to list of lists (including headers)
            data = [df_to_write.columns.tolist()] + df_to_write.values.tolist()
            
            # Write data
            worksheet.update('A1', data)
            print(f"✅ Written {len(df)} rows and {len(df.columns)} columns to sheet")
            
        except Exception as e:
            print(f"❌ Error writing to sheet: {str(e)}")
            raise
    
    def append_dataframe_to_sheet(self, worksheet, df: pd.DataFrame, 
                                 include_headers: bool = False):
        """
        Append a DataFrame to existing sheet data
        
        Args:
            worksheet: gspread.Worksheet object
            df (pd.DataFrame): Data to append
            include_headers (bool): Include column headers
        """
        try:
            # Convert to list of lists
            if include_headers:
                data = [df.columns.tolist()] + df.values.tolist()
            else:
                data = df.values.tolist()
            
            # Append data
            worksheet.append_rows(data)
            print(f"✅ Appended {len(df)} rows to sheet")
            
        except Exception as e:
            print(f"❌ Error appending to sheet: {str(e)}")
            raise
    
    def update_cell(self, worksheet, row: int, col: int, value: Any):
        """
        Update a specific cell
        
        Args:
            worksheet: gspread.Worksheet object
            row (int): Row number (1-based)
            col (int): Column number (1-based)
            value: Value to set
        """
        try:
            worksheet.update_cell(row, col, value)
            print(f"✅ Updated cell ({row}, {col}) with value: {value}")
            
        except Exception as e:
            print(f"❌ Error updating cell: {str(e)}")
            raise
    
    def update_range(self, worksheet, range_name: str, values: List[List[Any]]):
        """
        Update a range of cells
        
        Args:
            worksheet: gspread.Worksheet object
            range_name (str): Range in A1 notation (e.g., 'A1:C3')
            values (List[List[Any]]): 2D list of values
        """
        try:
            worksheet.update(range_name, values)
            print(f"✅ Updated range {range_name}")
            
        except Exception as e:
            print(f"❌ Error updating range: {str(e)}")
            raise
    
    def create_new_sheet(self, title: str, rows: int = 1000, cols: int = 26):
        """
        Create a new Google Sheet
        
        Args:
            title (str): Sheet title
            rows (int): Number of rows
            cols (int): Number of columns
        
        Returns:
            gspread.Spreadsheet: The new sheet
        """
        try:
            sheet = self.gc.create(title)
            sheet.sheet1.resize(rows, cols)
            print(f"✅ Created new sheet: {title}")
            print(f"   URL: {sheet.url}")
            return sheet
            
        except Exception as e:
            print(f"❌ Error creating sheet: {str(e)}")
            raise
    
    def share_sheet(self, sheet, email: str, role: str = 'writer'):
        """
        Share a sheet with someone
        
        Args:
            sheet: gspread.Spreadsheet object
            email (str): Email address to share with
            role (str): 'reader', 'writer', or 'owner'
        """
        try:
            sheet.share(email, perm_type='user', role=role)
            print(f"✅ Shared sheet with {email} as {role}")
            
        except Exception as e:
            print(f"❌ Error sharing sheet: {str(e)}")
            raise


def setup_credentials_guide():
    """
    Print setup guide for Google Sheets API credentials
    """
    print("""
🔧 GOOGLE SHEETS API SETUP GUIDE
================================

1. SERVICE ACCOUNT METHOD (Recommended for automation):
   
   a) Go to Google Cloud Console: https://console.cloud.google.com/
   b) Create a new project or select existing one
   c) Enable Google Sheets API and Google Drive API
   d) Go to "Credentials" → "Create Credentials" → "Service Account"
   e) Download the JSON key file
   f) Rename it to 'service_account_credentials.json'
   g) Share your Google Sheet with the service account email

2. OAUTH2 METHOD (For user-based access):
   
   a) Go to Google Cloud Console: https://console.cloud.google.com/
   b) Create OAuth 2.0 Client ID (Desktop application)
   c) Download the JSON file
   d) Rename it to 'oauth2_credentials.json'

3. INSTALL REQUIRED PACKAGES:
   
   pip install gspread google-auth google-auth-oauthlib google-auth-httplib2 pandas

4. EXAMPLE USAGE:
   
   # Using Service Account
   gsm = GoogleSheetsManager('service_account')
   
   # Using OAuth2
   gsm = GoogleSheetsManager('oauth2')
""")


if __name__ == "__main__":
    # Print setup guide
    setup_credentials_guide()
