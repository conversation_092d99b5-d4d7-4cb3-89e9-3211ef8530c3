Starting auto_rep_upload.py at 29-12-2025 11:55:50.43 
Running auto_rep_upload.py... 
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\My Files\auto_rep_upload.py", line 193, in <module>
    driver.get("http://swa.dbms.org.in/")
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\selenium\webdriver\remote\webdriver.py", line 353, in get
    self.execute(Command.GET, {"url": url})
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\selenium\webdriver\remote\webdriver.py", line 344, in execute
    self.error_handler.check_response(response)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\selenium\webdriver\remote\errorhandler.py", line 229, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.TimeoutException: Message: timeout: Timed out receiving message from renderer: 118.560
  (Session info: chrome=143.0.7499.170)
Stacktrace:
Symbols not available. Dumping unresolved backtrace:
	0x7ff7488688e5
	0x7ff748868940
	0x7ff74864165d
	0x7ff74862e847
	0x7ff74862e5bd
	0x7ff74862c20a
	0x7ff74862cbcf
	0x7ff74863bb8a
	0x7ff74865223f
	0x7ff74865940a
	0x7ff74862d375
	0x7ff748651df2
	0x7ff7486eb080
	0x7ff74868ac29
	0x7ff74868ba93
	0x7ff748b80640
	0x7ff748b7af80
	0x7ff748b996e6
	0x7ff748885de4
	0x7ff74888ed8c
	0x7ff748872004
	0x7ff7488721b5
	0x7ff748857ee2
	0x7ff970897374
	0x7ff97149cc91

ERROR: Script failed with error code 1 
