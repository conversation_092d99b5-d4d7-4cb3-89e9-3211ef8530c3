Starting iremp.py at 11-11-2025 12:18:27.93 
Running iremp.py... 

Processing:   0%|[32m          [0m| 0/1 [00:00<?, ?it/s]
Processing:   0%|[32m          [0m| 0/1 [00:29<?, ?it/s]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\My Files\irep_2.0.py", line 35, in <module>
    df7[['Email1', 'Email2', 'Email3']] = df7['Email'].str.split(',', expand=True) # 'Email4'
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\core\frame.py", line 3643, in __setitem__
    self._setitem_array(key, value)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\core\frame.py", line 3685, in _setitem_array
    check_key_length(self.columns, key, value)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\core\indexers\utils.py", line 428, in check_key_length
    raise ValueError("Columns must be same length as key")
ValueError: Columns must be same length as key
SUCCESS: Script completed successfully at 11-11-2025 12:18:59.15 
