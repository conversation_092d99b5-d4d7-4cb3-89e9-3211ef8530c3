Starting master-s_2.0.py at 03-11-2025 16:06:56.49 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:20<00:00, 20.22s/it]
Processing: 100%|##########| 1/1 [00:20<00:00, 20.22s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:01<00:00,  1.30s/it]
Starting: 100%|##########| 1/1 [00:01<00:00,  1.30s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:10<00:00, 10.18s/it]
Processing: 100%|##########| 1/1 [00:10<00:00, 10.18s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:31<00:00, 31.91s/it]
Processing: 100%|##########| 1/1 [00:31<00:00, 31.91s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.95it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.95it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,23,32,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:02<00:00,  2.86s/it]
Processing: 100%|##########| 1/1 [00:02<00:00,  2.86s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.89it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.89it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:44<00:00, 44.44s/it]
Processing: 100%|##########| 1/1 [00:44<00:00, 44.44s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:37<00:00, 37.19s/it]
Finishing: 100%|##########| 1/1 [00:37<00:00, 37.19s/it]
SUCCESS: master-s_2.0.py completed successfully at 03-11-2025 16:09:26.44 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 03-11-2025 16:09:47.41 
