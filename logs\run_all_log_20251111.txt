Starting master-s_2.0.py at 11-11-2025 13:02:19.14 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:20<00:00, 20.43s/it]
Processing: 100%|##########| 1/1 [00:20<00:00, 20.43s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:00<00:00, 28.91it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.16s/it]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.16s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:31<00:00, 31.32s/it]
Processing: 100%|##########| 1/1 [00:31<00:00, 31.32s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  6.67it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  6.67it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (43,44,45) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:09<00:00,  9.09s/it]
Processing: 100%|##########| 1/1 [00:09<00:00,  9.09s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.65it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.64it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:53<00:00, 53.09s/it]
Processing: 100%|##########| 1/1 [00:53<00:00, 53.09s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:49<00:00, 49.66s/it]
Finishing: 100%|##########| 1/1 [00:49<00:00, 49.66s/it]
SUCCESS: master-s_2.0.py completed successfully at 11-11-2025 13:05:17.94 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 11-11-2025 13:05:40.76 
