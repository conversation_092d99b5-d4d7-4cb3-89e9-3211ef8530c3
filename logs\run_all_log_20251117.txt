Starting master-s_2.0.py at 17-11-2025 14:19:10.26 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:19<00:00, 19.79s/it]
Processing: 100%|##########| 1/1 [00:19<00:00, 19.79s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:02<00:00,  2.43s/it]
Starting: 100%|##########| 1/1 [00:02<00:00,  2.43s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:09<00:00, 10.00s/it]
Processing: 100%|##########| 1/1 [00:09<00:00, 10.00s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:26<00:00, 26.61s/it]
Processing: 100%|##########| 1/1 [00:26<00:00, 26.61s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.35it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.35it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:08<00:00,  8.57s/it]
Processing: 100%|##########| 1/1 [00:08<00:00,  8.57s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  6.77it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  6.77it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:44<00:00, 44.15s/it]
Processing: 100%|##########| 1/1 [00:44<00:00, 44.15s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing:   0%|          | 0/1 [00:11<?, ?it/s]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py", line 494, in <module>
    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', usecols = ['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)
  File "C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py", line 494, in <listcomp>
    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', usecols = ['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\util\_decorators.py", line 311, in wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\io\parsers\readers.py", line 678, in read_csv
    return _read(filepath_or_buffer, kwds)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\io\parsers\readers.py", line 575, in _read
    parser = TextFileReader(filepath_or_buffer, **kwds)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\io\parsers\readers.py", line 932, in __init__
    self._engine = self._make_engine(f, self.engine)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\io\parsers\readers.py", line 1234, in _make_engine
    return mapping[engine](f, **self.options)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\io\parsers\c_parser_wrapper.py", line 131, in __init__
    self._validate_usecols_names(usecols, self.orig_names)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\pandas\io\parsers\base_parser.py", line 913, in _validate_usecols_names
    raise ValueError(
ValueError: Usecols do not match columns, columns expected but not found: ['status', 'email', 'date_added']
ERROR: master-s_2.0.py failed with error code 1 
