Starting master-s_2.0.py at 01-12-2025 11:07:03.63 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:20<00:00, 20.37s/it]
Processing: 100%|##########| 1/1 [00:20<00:00, 20.38s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:04<00:00,  4.70s/it]
Starting: 100%|##########| 1/1 [00:04<00:00,  4.70s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.30it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.30it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.40s/it]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.40s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  4.92it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  4.92it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:280: DtypeWarning: Columns (17,20,27) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:02<00:00,  2.12s/it]
Processing: 100%|##########| 1/1 [00:02<00:00,  2.12s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.76it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.76it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:49<00:00, 49.35s/it]
Processing: 100%|##########| 1/1 [00:49<00:00, 49.35s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [01:10<00:00, 70.48s/it]
Finishing: 100%|##########| 1/1 [01:10<00:00, 70.48s/it]
SUCCESS: master-s_2.0.py completed successfully at 01-12-2025 11:10:15.44 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 01-12-2025 11:10:38.29 
