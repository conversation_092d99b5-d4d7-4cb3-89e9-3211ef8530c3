Starting master-s_2.0.py at 22-12-2025 11:32:54.25 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:36<00:00, 36.60s/it]
Processing: 100%|##########| 1/1 [00:36<00:00, 36.60s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:01<00:00,  1.01s/it]
Starting: 100%|##########| 1/1 [00:01<00:00,  1.01s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:25<00:00, 25.13s/it]
Processing: 100%|##########| 1/1 [00:25<00:00, 25.13s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:59<00:00, 59.67s/it]
Processing: 100%|##########| 1/1 [00:59<00:00, 59.67s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  3.33it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  3.33it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:280: DtypeWarning: Columns (36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:06<00:00,  6.99s/it]
Processing: 100%|##########| 1/1 [00:06<00:00,  6.99s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  4.42it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  4.42it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [01:23<00:00, 83.01s/it]
Processing: 100%|##########| 1/1 [01:23<00:00, 83.01s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [01:47<00:00, 107.07s/it]
Finishing: 100%|##########| 1/1 [01:47<00:00, 107.07s/it]
SUCCESS: master-s_2.0.py completed successfully at 22-12-2025 11:38:17.25 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 22-12-2025 11:39:07.19 
