Starting master-s_2.0.py at 12-01-2026 12:52:04.41 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:18<00:00, 18.98s/it]
Processing: 100%|##########| 1/1 [00:18<00:00, 18.98s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:00<00:00, 26.09it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:10<00:00, 10.82s/it]
Processing: 100%|##########| 1/1 [00:10<00:00, 10.82s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:35<00:00, 35.20s/it]
Processing: 100%|##########| 1/1 [00:35<00:00, 35.20s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.71it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.71it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:280: DtypeWarning: Columns (33,34,35) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:280: DtypeWarning: Columns (33,34,35,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:04<00:00,  4.28s/it]
Processing: 100%|##########| 1/1 [00:04<00:00,  4.28s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.53it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.53it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:43<00:00, 43.51s/it]
Processing: 100%|##########| 1/1 [00:43<00:00, 43.51s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:58<00:00, 58.49s/it]
Finishing: 100%|##########| 1/1 [00:58<00:00, 58.49s/it]
SUCCESS: master-s_2.0.py completed successfully at 12-01-2026 12:54:57.83 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 12-01-2026 12:55:18.60 
