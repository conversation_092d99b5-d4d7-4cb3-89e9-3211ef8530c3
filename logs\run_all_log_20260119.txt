Starting master-s_2.0.py at 19-01-2026 14:03:36.71 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:33<00:00, 33.07s/it]
Processing: 100%|##########| 1/1 [00:33<00:00, 33.07s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:15<00:00, 15.04s/it]
Starting: 100%|##########| 1/1 [00:15<00:00, 15.04s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:15<00:00, 15.50s/it]
Processing: 100%|##########| 1/1 [00:15<00:00, 15.50s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:54<00:00, 54.20s/it]
Processing: 100%|##########| 1/1 [00:54<00:00, 54.20s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  4.53it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  4.53it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:280: DtypeWarning: Columns (36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:04<00:00,  4.07s/it]
Processing: 100%|##########| 1/1 [00:04<00:00,  4.07s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.04it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.01it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:47<00:00, 47.79s/it]
Processing: 100%|##########| 1/1 [00:47<00:00, 47.79s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [01:10<00:00, 70.07s/it]
Finishing: 100%|##########| 1/1 [01:10<00:00, 70.07s/it]
SUCCESS: master-s_2.0.py completed successfully at 19-01-2026 14:07:38.71 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 19-01-2026 14:08:02.64 
