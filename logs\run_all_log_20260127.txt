Starting master-s_2.0.py at 27-01-2026 14:03:28.95 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:15<00:00, 15.49s/it]
Processing: 100%|##########| 1/1 [00:15<00:00, 15.49s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:03<00:00,  3.13s/it]
Starting: 100%|##########| 1/1 [00:03<00:00,  3.13s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:11<00:00, 11.63s/it]
Processing: 100%|##########| 1/1 [00:11<00:00, 11.63s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:18<00:00, 18.34s/it]
Processing: 100%|##########| 1/1 [00:18<00:00, 18.34s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 10.01it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:280: DtypeWarning: Columns (36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:03<00:00,  3.51s/it]
Processing: 100%|##########| 1/1 [00:03<00:00,  3.51s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.82it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.82it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.94s/it]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.94s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [01:05<00:00, 65.94s/it]
Finishing: 100%|##########| 1/1 [01:05<00:00, 65.94s/it]
SUCCESS: master-s_2.0.py completed successfully at 27-01-2026 14:06:11.75 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 27-01-2026 14:06:34.16 
