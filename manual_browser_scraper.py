"""
Manual Browser Data Extraction Guide for Magnus Group CMS

This script provides instructions and tools for manually extracting data
from the Magnus Group admin panel.
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
from datetime import datetime

def analyze_html_file(filename):
    """Analyze an HTML file to understand its structure"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        print(f"\n=== ANALYZING {filename} ===")
        print(f"Title: {soup.title.string if soup.title else 'No title'}")
        
        # Look for tables
        tables = soup.find_all('table')
        print(f"\nFound {len(tables)} table(s):")
        
        for i, table in enumerate(tables):
            print(f"\n  Table {i+1}:")
            rows = table.find_all('tr')
            print(f"    Rows: {len(rows)}")
            
            if rows:
                # Check first row for headers
                first_row = rows[0]
                headers = first_row.find_all(['th', 'td'])
                header_texts = [h.get_text(strip=True) for h in headers]
                print(f"    Possible headers: {header_texts}")
                
                # Show a few sample rows
                for j, row in enumerate(rows[1:6]):  # Show up to 5 data rows
                    cells = row.find_all(['td', 'th'])
                    cell_texts = [cell.get_text(strip=True)[:50] for cell in cells]  # Truncate long text
                    print(f"    Row {j+2}: {cell_texts}")
                
                if len(rows) > 6:
                    print(f"    ... and {len(rows) - 6} more rows")
        
        # Look for forms
        forms = soup.find_all('form')
        print(f"\nFound {len(forms)} form(s)")
        
        # Look for divs with classes that might contain data
        data_divs = soup.find_all('div', class_=lambda x: x and any(keyword in ' '.join(x).lower() for keyword in ['item', 'entry', 'record', 'data', 'content', 'abstract']))
        print(f"\nFound {len(data_divs)} potential data container div(s)")
        
        # Look for lists
        lists = soup.find_all(['ul', 'ol'])
        print(f"\nFound {len(lists)} list(s)")
        
        # Look for pagination
        pagination_elements = soup.find_all(['a', 'button'], string=lambda text: text and any(keyword in text.lower() for keyword in ['next', 'prev', 'page', 'more']))
        print(f"\nFound {len(pagination_elements)} pagination element(s)")
        
        # Look for JavaScript that might load data
        scripts = soup.find_all('script')
        ajax_scripts = [script for script in scripts if script.string and ('ajax' in script.string.lower() or 'fetch' in script.string.lower() or 'xhr' in script.string.lower())]
        print(f"\nFound {len(ajax_scripts)} script(s) that might load data dynamically")
        
        return soup
        
    except FileNotFoundError:
        print(f"File {filename} not found")
        return None
    except Exception as e:
        print(f"Error analyzing file: {e}")
        return None

def extract_table_data(soup, table_index=0):
    """Extract data from a specific table"""
    tables = soup.find_all('table')
    
    if table_index >= len(tables):
        print(f"Table index {table_index} not found. Available tables: {len(tables)}")
        return None
    
    table = tables[table_index]
    rows = table.find_all('tr')
    
    if not rows:
        print("No rows found in table")
        return None
    
    # Extract headers
    header_row = rows[0]
    headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
    
    # Extract data rows
    data = []
    for row in rows[1:]:
        cells = row.find_all(['td', 'th'])
        row_data = {}
        for i, cell in enumerate(cells):
            if i < len(headers):
                # Extract text and any links
                text = cell.get_text(strip=True)
                links = [a.get('href') for a in cell.find_all('a', href=True)]
                
                row_data[headers[i]] = {
                    'text': text,
                    'links': links
                }
        data.append(row_data)
    
    return {
        'headers': headers,
        'data': data
    }

def save_data_to_csv(data, filename):
    """Save extracted data to CSV"""
    if not data or not data['data']:
        print("No data to save")
        return
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        # Use the headers from the data
        fieldnames = data['headers']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in data['data']:
            # Convert the complex data structure to simple text for CSV
            csv_row = {}
            for header in fieldnames:
                if header in row:
                    csv_row[header] = row[header]['text']
                else:
                    csv_row[header] = ''
            writer.writerow(csv_row)
    
    print(f"Data saved to {filename}")

def save_data_to_json(data, filename):
    """Save extracted data to JSON"""
    with open(filename, 'w', encoding='utf-8') as jsonfile:
        json.dump(data, jsonfile, indent=2, ensure_ascii=False)
    
    print(f"Data saved to {filename}")

def main():
    print("=== MAGNUS GROUP CMS DATA EXTRACTOR ===")
    print("\nThis tool helps you extract data from the Magnus Group admin panel.")
    print("\nSTEPS TO USE:")
    print("1. Manually login to: https://admin.magnusgroup.biz/admin-login.php")
    print("2. Navigate to: https://admin.magnusgroup.biz/view-all-abstracts.php")
    print("3. Save the page as HTML (Ctrl+S) and name it 'abstracts_data.html'")
    print("4. Place the HTML file in this directory")
    print("5. Run this script again")
    
    # Check if HTML file exists
    html_files = ['abstracts_data.html', 'abstracts_page_selenium.html', 'abstracts_page.html']
    
    found_file = None
    for filename in html_files:
        try:
            with open(filename, 'r'):
                found_file = filename
                break
        except FileNotFoundError:
            continue
    
    if found_file:
        print(f"\nFound HTML file: {found_file}")
        soup = analyze_html_file(found_file)
        
        if soup:
            # Try to extract table data
            tables = soup.find_all('table')
            if tables:
                print(f"\nExtracting data from {len(tables)} table(s)...")
                
                for i in range(len(tables)):
                    print(f"\n--- Processing Table {i+1} ---")
                    table_data = extract_table_data(soup, i)
                    
                    if table_data and table_data['data']:
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        csv_filename = f"abstracts_table_{i+1}_{timestamp}.csv"
                        json_filename = f"abstracts_table_{i+1}_{timestamp}.json"
                        
                        save_data_to_csv(table_data, csv_filename)
                        save_data_to_json(table_data, json_filename)
                        
                        print(f"Table {i+1} contains {len(table_data['data'])} records")
                        print(f"Headers: {table_data['headers']}")
            else:
                print("\nNo tables found. The data might be in a different format.")
                print("Please check the HTML file manually or provide more details about the page structure.")
    
    else:
        print(f"\nNo HTML files found. Please save the abstracts page as HTML first.")
        print("Looking for files: " + ", ".join(html_files))

if __name__ == "__main__":
    main()
