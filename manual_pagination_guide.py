"""
Manual Pagination Data Collection Guide
For when automated browser scraping isn't working
"""

import os
import glob
from bs4 import BeautifulSoup
import json
import csv
import re
from datetime import datetime

class ManualPaginationProcessor:
    def __init__(self):
        self.all_records = []
        
    def process_saved_html_files(self):
        """Process manually saved HTML files from each page"""
        print("=== MANUAL PAGINATION PROCESSOR ===")
        print("\nLooking for saved HTML files...")
        
        # Look for HTML files with page numbers
        html_files = []
        
        # Common naming patterns for saved pages
        patterns = [
            "abstracts_page_*.html",
            "page_*.html", 
            "magnus_page_*.html",
            "all_conferences_page_*.html"
        ]
        
        for pattern in patterns:
            html_files.extend(glob.glob(pattern))
        
        # Also look for single files
        single_files = [
            "abstracts_all_conferences.html",
            "all_conferences.html",
            "magnus_all_data.html"
        ]
        
        for filename in single_files:
            if os.path.exists(filename):
                html_files.append(filename)
        
        if not html_files:
            print("No HTML files found. Please save pages manually:")
            self.print_manual_instructions()
            return []
        
        print(f"Found {len(html_files)} HTML file(s):")
        for f in html_files:
            print(f"  - {f}")
        
        # Process each file
        for html_file in sorted(html_files):
            print(f"\nProcessing: {html_file}")
            records = self.extract_from_html_file(html_file)
            self.all_records.extend(records)
            print(f"Extracted {len(records)} records from {html_file}")
        
        return self.all_records
    
    def print_manual_instructions(self):
        """Print instructions for manual data collection"""
        print("\n" + "="*60)
        print("MANUAL DATA COLLECTION INSTRUCTIONS")
        print("="*60)
        print("\n1. LOGIN:")
        print("   - Go to: https://admin.magnusgroup.biz/admin-login.php")
        print("   - Username: <EMAIL>")
        print("   - Password: Magnus@38")
        
        print("\n2. NAVIGATE TO ABSTRACTS:")
        print("   - Go to: https://admin.magnusgroup.biz/view-all-abstracts.php")
        
        print("\n3. SET DROPDOWN:")
        print("   - Find the conference dropdown/filter")
        print("   - Select 'All Conferences'")
        print("   - Wait for page to reload")
        
        print("\n4. SAVE EACH PAGE:")
        print("   - Page 1: Save as 'page_1.html' (Ctrl+S)")
        print("   - Click 'Next' or page 2")
        print("   - Page 2: Save as 'page_2.html'")
        print("   - Continue for all pages...")
        print("   - Stop when you reach the last page")
        
        print("\n5. RUN THIS SCRIPT AGAIN:")
        print("   - Place all HTML files in this directory")
        print("   - Run: python manual_pagination_guide.py")
        
        print("\n" + "="*60)
    
    def extract_from_html_file(self, html_file):
        """Extract data from a single HTML file"""
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading {html_file}: {e}")
            return []
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # Find the main data table
        tables = soup.find_all('table')
        if not tables:
            print(f"No tables found in {html_file}")
            return []
        
        # Use the first table (usually the main data table)
        table = tables[0]
        rows = table.find_all('tr')
        
        if len(rows) <= 1:
            print(f"No data rows found in {html_file}")
            return []
        
        records = []
        page_num = self.extract_page_number(html_file)
        
        # Process each data row (skip header)
        for i, row in enumerate(rows[1:], 1):
            try:
                record = self.extract_record_from_row(row, page_num, i)
                if record:
                    records.append(record)
            except Exception as e:
                print(f"Error processing row {i} in {html_file}: {e}")
        
        return records
    
    def extract_page_number(self, filename):
        """Extract page number from filename"""
        # Try to find page number in filename
        page_match = re.search(r'page[_\s]*(\d+)', filename.lower())
        if page_match:
            return int(page_match.group(1))
        return 1
    
    def extract_record_from_row(self, row, page_num, row_num):
        """Extract data from a table row"""
        cells = row.find_all(['td', 'th'])
        
        if len(cells) < 2:
            return None
        
        record = {
            'page_number': page_num,
            'row_number': row_num
        }
        
        # Extract S.No (first column)
        if len(cells) > 0:
            record['s_no'] = cells[0].get_text(strip=True)
        
        # Extract Details (second column usually)
        if len(cells) > 1:
            details_text = cells[1].get_text(strip=True)
            parsed_details = self.parse_details_field(details_text)
            record.update(parsed_details)
        
        # Try to extract conference info
        record['conference'] = self.extract_conference_from_row(row)
        
        # Look for abstract type in detailed forms within the page
        record['abstract_type'] = self.extract_abstract_type_from_page(row)
        
        return record
    
    def parse_details_field(self, details_text):
        """Parse details field for name, email, title, date"""
        parsed = {}
        
        # Extract emails
        email_matches = re.findall(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', details_text)
        if email_matches:
            parsed['email_primary'] = email_matches[0]
            if len(email_matches) > 1:
                parsed['email_secondary'] = email_matches[1]
        
        # Extract name
        name_patterns = [
            r'([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),',
            r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        ]
        
        for pattern in name_patterns:
            name_match = re.search(pattern, details_text)
            if name_match and '@' not in name_match.group(1):
                parsed['name'] = name_match.group(1).strip()
                break
        
        # Extract abstract title
        title_match = re.search(r'Abstract Title:([^S]+?)Submitted On:', details_text)
        if title_match:
            parsed['abstract_title'] = title_match.group(1).strip()
        
        # Extract submission date
        date_match = re.search(r'Submitted On:([^$]+)', details_text)
        if date_match:
            parsed['submitted_on'] = date_match.group(1).strip()
        
        return parsed
    
    def extract_conference_from_row(self, row):
        """Extract conference information from row"""
        # This will depend on the actual page structure
        # For now, return empty - will need to see actual HTML
        return ""
    
    def extract_abstract_type_from_page(self, row):
        """Extract abstract type from detailed view in the same page"""
        # Look for abstract type in form fields within the row or nearby
        # This will be enhanced based on actual page structure
        return ""

    def save_data(self, records, filename_base="magnus_all_conferences_manual"):
        """Save the extracted data"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save to CSV
        csv_filename = f"{filename_base}_{timestamp}.csv"

        if records:
            fieldnames = [
                's_no', 'conference', 'name', 'email_primary', 'email_secondary',
                'abstract_title', 'submitted_on', 'abstract_type',
                'page_number', 'row_number'
            ]

            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for record in records:
                    clean_record = {}
                    for field in fieldnames:
                        clean_record[field] = record.get(field, '')
                    writer.writerow(clean_record)

        # Save to JSON
        json_filename = f"{filename_base}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(records, f, indent=2, ensure_ascii=False)

        print(f"\nData saved to:")
        print(f"  - {csv_filename}")
        print(f"  - {json_filename}")

        return csv_filename, json_filename

def main():
    processor = ManualPaginationProcessor()

    # Process saved HTML files
    all_records = processor.process_saved_html_files()

    if all_records:
        # Save data
        csv_file, json_file = processor.save_data(all_records)

        # Print summary
        print(f"\n=== PROCESSING SUMMARY ===")
        print(f"Total records: {len(all_records)}")

        # Count records with different data
        with_name = sum(1 for r in all_records if r.get('name'))
        with_email = sum(1 for r in all_records if r.get('email_primary'))
        with_title = sum(1 for r in all_records if r.get('abstract_title'))
        with_date = sum(1 for r in all_records if r.get('submitted_on'))

        print(f"Records with name: {with_name}")
        print(f"Records with email: {with_email}")
        print(f"Records with abstract title: {with_title}")
        print(f"Records with submission date: {with_date}")

        # Show sample record
        if all_records:
            print(f"\n=== SAMPLE RECORD ===")
            sample = all_records[0]
            for key, value in sample.items():
                print(f"{key}: {value}")

    else:
        print("\nNo records processed.")

if __name__ == "__main__":
    main()
