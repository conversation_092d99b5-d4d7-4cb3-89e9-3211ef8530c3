@ECHO OFF
setlocal enabledelayedexpansion

echo Starting master-s_2.0.py and direct_unsubs.py at %date% %time%

REM Create logs directory if it doesn't exist
if not exist "C:\Users\<USER>\OneDrive\My Files\logs" mkdir "C:\Users\<USER>\OneDrive\My Files\logs"

set "LOG_FILE=C:\Users\<USER>\OneDrive\My Files\logs\run_all_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo Starting master-s_2.0.py at %date% %time% > "%LOG_FILE%"

cd C:\Users\<USER>\OneDrive\My Files || (
    echo ERROR: Could not change to directory >> "%LOG_FILE%"
    echo ERROR: Could not change to directory
    exit /b 1
)

echo Running master-s_2.0.py...
echo Running master-s_2.0.py... >> "%LOG_FILE%"

python master-s_2.0.py 2>> "%LOG_FILE%"

if !errorlevel! neq 0 (
    echo ERROR: master-s_2.0.py failed with error code !errorlevel! >> "%LOG_FILE%"
    echo ERROR: master-s_2.0.py failed with error code !errorlevel!
    exit /b !errorlevel!
) else (
    echo SUCCESS: master-s_2.0.py completed successfully at %date% %time% >> "%LOG_FILE%"
    echo SUCCESS: master-s_2.0.py completed successfully
)

echo Running direct_unsubs.py...
echo Running direct_unsubs.py... >> "%LOG_FILE%"

python direct_unsubs.py 2>> "%LOG_FILE%"

if !errorlevel! neq 0 (
    echo ERROR: direct_unsubs.py failed with error code !errorlevel! >> "%LOG_FILE%"
    echo ERROR: direct_unsubs.py failed with error code !errorlevel!
) else (
    echo SUCCESS: direct_unsubs.py completed successfully at %date% %time% >> "%LOG_FILE%"
    echo SUCCESS: direct_unsubs.py completed successfully
)

echo Log file saved to: %LOG_FILE%

endlocal
