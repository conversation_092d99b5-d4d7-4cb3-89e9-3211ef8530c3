import csv
import os
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
import time
from prettytable import PrettyTable
import datetime
import glob

# Import the rich progress module
from rich_progress import print_status, create_progress_bar, RICH_AVAILABLE

# Function to count rows in a CSV file
def count_csv_rows(file_path):
    try:
        with open(file_path, mode='r', encoding='utf-8') as file:
            reader = csv.reader(file)
            row_count = sum(1 for row in reader) - 1  # Subtract 1 for header
        return row_count
    except Exception as e:
        print_status(f"Error counting rows in {file_path}: {str(e)}", "error")
        return 0

# Function to read Filter ID and Filter Name values from a CSV file
def read_csv(file_path):
    fid_values = []
    filter_names = []
    with open(file_path, mode='r') as file:
        reader = csv.reader(file)
        next(reader)  # Skip the header row
        for row in reader:
            fid_values.append(row[0])
            filter_names.append(row[1])
    return fid_values, filter_names

# Function to find CSV files in the specified directories
def find_csv_files(filter_name, directories):
    """
    Find CSV files matching the filter name in the specified directories.
    Returns a list of file paths found.
    """
    found_files = []
    
    # Extract the conference name from filter name (remove "_unsubscribes" suffix)
    if filter_name.endswith("_unsubscribes"):
        conference_name = filter_name.replace("_unsubscribes", "")
    elif filter_name == "Mathews Global Unsubscribes":
        conference_name = "Global_Unsubscriber"
    else:
        conference_name = filter_name
    
    for directory in directories:
        if os.path.exists(directory):
            # Look for files matching the conference name pattern
            pattern = os.path.join(directory, f"{conference_name}.csv")
            matching_files = glob.glob(pattern)
            found_files.extend(matching_files)
            
            # Also look for files with the full filter name
            pattern = os.path.join(directory, f"{filter_name}.csv")
            matching_files = glob.glob(pattern)
            found_files.extend(matching_files)
    
    # Remove duplicates
    return list(set(found_files))

# Function to take screenshot when upload fails
def take_screenshot(driver, filter_name):
    try:
        # Create screenshots directory if it doesn't exist
        screenshot_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "screenshots")
        os.makedirs(screenshot_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{screenshot_dir}/error_{filter_name}_{timestamp}.png"
        
        # Take screenshot and save
        driver.save_screenshot(filename)
        print_status(f"Screenshot saved to {filename}", "info")
        return filename
    except Exception as e:
        print_status(f"Failed to take screenshot: {str(e)}", "error")
        return None

# Configuration
csv_file_path = r"C:\Users\<USER>\OneDrive\My Files\fid_mathews_unsubs.csv"

# Directories to search for CSV files
search_directories = [
    r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\cms\merged\separated",
    r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\postpanel\merged\separated"
]

print_status("DBMS Mathews Unsubscribers Upload Process", "header")

# Read the CSV file
fid_values, filter_names = read_csv(csv_file_path)

# Ensure that the length of fid_values and filter_names are the same
if len(fid_values) != len(filter_names):
    raise ValueError("The length of fid_values and filter_names must be the same.")

# Find CSV files and get row counts
file_paths = []
row_counts = []
print_status("Finding and counting rows in CSV files...", "info")

# Create a progress bar for counting rows
count_progress, update_count = create_progress_bar(len(filter_names), "Finding files", "blue")

for i, filter_name in enumerate(filter_names):
    update_count(0, f"Finding files for {filter_name}")
    
    # Find CSV files for this filter
    found_files = find_csv_files(filter_name, search_directories)
    
    if found_files:
        # Use the first found file
        file_path = found_files[0]
        file_paths.append(file_path)
        count = count_csv_rows(file_path)
        row_counts.append(count)
        update_count(1, f"Found {os.path.basename(file_path)} - {count} rows")
        
        if len(found_files) > 1:
            print_status(f"Multiple files found for {filter_name}, using: {os.path.basename(file_path)}", "warning")
    else:
        print_status(f"No files found for {filter_name}", "warning")
        file_paths.append(None)
        row_counts.append(0)
        update_count(1, f"No files found for {filter_name}")

# Stop the progress bar
if RICH_AVAILABLE:
    count_progress.stop()

# Display the values in a table
table = PrettyTable()
table.field_names = ["Filter ID", "Filter Name", "File Found", "Row Count"]
for fid, filter_name, file_path, count in zip(fid_values, filter_names, file_paths, row_counts):
    file_name = os.path.basename(file_path) if file_path else "Not Found"
    table.add_row([fid, filter_name, file_name, count])

print_status("Files to be uploaded:", "info")
print(table)

# Check if any files were found
files_to_upload = [fp for fp in file_paths if fp is not None]
if not files_to_upload:
    print_status("No files found to upload. Exiting.", "error")
    sys.exit(1)

# Selenium setup
print_status("Setting up Chrome browser...", "info")
options = webdriver.ChromeOptions()

# Suppress console logging messages
options.add_experimental_option("excludeSwitches", ["enable-logging"])
options.add_argument('log-level=3')  # Set log level to errors only

# Headless mode and other performance settings
options.add_argument("--headless")  # Run in headless mode
options.add_argument("window-size=1920,1080")
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")
options.add_argument("--disable-extensions")
options.add_argument("--disable-notifications")

# Upload preferences
prefs = {
    "upload.default_directory": r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs",
    "upload.prompt_for_download": False,
    "upload.directory_upgrade": True,
    "browser.download.show_plugins_in_list": False,
    "browser.download.folderList": 2,
    "browser.download.manager.showWhenStarting": False
}
options.add_experimental_option('prefs', prefs)

# Initialize Chrome browser
driver = webdriver.Chrome(options=options)
driver.set_window_size(1920, 1080)

try:
    # Login to the website
    print_status("Logging in to DBMS...", "info")
    driver.get("http://swa.dbms.org.in/")
    driver.find_element(By.ID, "username").send_keys("<EMAIL>")
    driver.find_element(By.ID, "password").send_keys("Magnus@123")
    time.sleep(3)
    driver.find_element(By.ID, "btnSubmit").click()
    time.sleep(3)

    # Create a progress bar for the file upload process
    print_status("Starting file upload process...", "info")
    total_files = len(fid_values)
    
    # Create a progress bar with purple gradient
    progress, update_progress = create_progress_bar(total_files, "Uploading files", "purple")

    # Iterate over fid, filter_name, file_path, and row_count values
    for i, (fid, filter_name, file_path, count) in enumerate(zip(fid_values, filter_names, file_paths, row_counts)):
        # Update progress description with row count
        file_name = os.path.basename(file_path) if file_path else "No file"
        update_progress(0, f"Uploading {file_name} - {count} rows ({i+1}/{total_files})")

        # Skip if no file was found
        if not file_path:
            print_status(f"Skipping {filter_name} - no file found", "warning")
            update_progress(1, f"Skipped {filter_name} - no file found")
            continue

        # Navigate to the upload page
        driver.get(f"http://swa.dbms.org.in/upload_data_file.php?datafilter={fid}&datafiltername={filter_name}")
        button = driver.find_element(By.ID, 'up_file')
        time.sleep(2)

        # Check if file exists and has rows
        if not os.path.exists(file_path):
            print_status(f"File not found: {file_path}", "error")
            take_screenshot(driver, f"{filter_name}_file_not_found")
            update_progress(1, f"Skipped {filter_name} - file not found")
            continue

        if count == 0:
            print_status(f"File has no data rows: {file_path}", "warning")
            take_screenshot(driver, f"{filter_name}_no_data")
            update_progress(1, f"Skipped {filter_name} - no data rows")
            continue

        try:
            # Direct file upload using send_keys
            button.send_keys(file_path)

            # Check duplicates and submit
            driver.find_element(By.ID, "duplicates").click()
            time.sleep(3)
            driver.find_element(By.ID, "submit_key").click()
            time.sleep(10)

            # Update progress
            update_progress(1)
            print_status(f"Uploaded {filter_name} ({file_name}) with {count} rows", "success")
        except Exception as e:
            print_status(f"Error uploading {filter_name}: {str(e)}", "error")
            screenshot_path = take_screenshot(driver, filter_name.replace(" ", "_"))
            print_status(f"Error details captured in screenshot: {screenshot_path}", "info")
            update_progress(1, f"Failed {filter_name} - error during upload")

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

    # Calculate summary statistics
    uploaded_files = sum(1 for count in row_counts if count > 0)
    total_rows = sum(row_counts)

    # Print completion message with statistics
    print_status("Upload Process Completed", "header")
    print_status(f"Uploaded {uploaded_files} of {total_files} files", "success")
    print_status(f"Total rows processed: {total_rows}", "success")

    # Create a summary table
    summary_table = PrettyTable()
    summary_table.field_names = ["Metric", "Value"]
    summary_table.add_row(["Total Files", total_files])
    summary_table.add_row(["Uploaded Files", uploaded_files])
    summary_table.add_row(["Skipped Files", total_files - uploaded_files])
    summary_table.add_row(["Total Rows", total_rows])
    summary_table.add_row(["Average Rows Per File", round(total_rows / uploaded_files) if uploaded_files > 0 else 0])

    print(summary_table)

except Exception as e:
    print_status(f"Error during upload process: {str(e)}", "error")

finally:
    # Close the browser
    print_status("Closing browser...", "info")
    driver.quit()
