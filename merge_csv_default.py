import os
import glob
import pandas as pd
import rich_progress
from datetime import datetime

try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False
    rich_progress.print_status("Warning: chardet not available, using basic encoding detection", "warning")

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def detect_file_encoding(file_path):
    """Detect the encoding of a file using chardet and validation."""
    try:
        if CHARDET_AVAILABLE:
            with open(file_path, 'rb') as f:
                raw_data = f.read(50000)  # Read more data for better detection
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']

                rich_progress.print_status(f"  - Chardet detected: {encoding} (confidence: {confidence:.2f})", "info")
        else:
            # Fallback when chardet is not available
            encoding = None
            confidence = 0.0
            rich_progress.print_status("  - Using basic encoding detection (chardet not available)", "info")

            # Enhanced encoding preference list with better Unicode support
            preferred_encodings = [
                'utf-8-sig',  # UTF-8 with BOM
                'utf-8',      # UTF-8 without BOM
                'utf-16',     # UTF-16 with BOM
                'cp1252',     # Windows-1252 (Western European)
                'iso-8859-1', # Latin-1
                'iso-8859-15', # Latin-9 (includes Euro symbol)
                'cp1250',     # Windows-1250 (Central European)
                'cp1251',     # Windows-1251 (Cyrillic)
                'latin-1'     # Last resort
            ]

            # If chardet gives us a good result, try it first
            if confidence >= 0.8 and encoding:
                # Map some common detected encodings to preferred ones
                encoding_map = {
                    'ascii': 'utf-8-sig',
                    'utf-8': 'utf-8-sig',
                    'windows-1252': 'cp1252',
                    'windows-1250': 'cp1250',
                    'windows-1251': 'cp1251',
                    'iso-8859-1': 'iso-8859-1'
                }

                mapped_encoding = encoding_map.get(encoding.lower(), encoding)

                # Test the detected/mapped encoding first
                try:
                    with open(file_path, 'r', encoding=mapped_encoding) as test_file:
                        content = test_file.read(5000)
                        # Check for common problematic character sequences that indicate wrong encoding
                        if not any(seq in content for seq in ['â€™', 'â€œ', 'â€', 'Ã¼', 'Ã¡', 'Ã©', 'Ã­', 'Ã³', 'Ãº']):
                            return mapped_encoding
                except:
                    pass

            # Try preferred encodings in order
            for enc in preferred_encodings:
                try:
                    with open(file_path, 'r', encoding=enc) as test_file:
                        content = test_file.read(5000)
                        # Check for problematic character sequences
                        if not any(seq in content for seq in ['â€™', 'â€œ', 'â€', 'Ã¼', 'Ã¡', 'Ã©', 'Ã­', 'Ã³', 'Ãº']):
                            return enc
                except:
                    continue

            return 'utf-8-sig'  # Final fallback
    except Exception as e:
        rich_progress.print_status(f"  - Encoding detection error: {str(e)}", "warning")
        return 'utf-8-sig'  # Default fallback

def find_common_prefix(filenames):
    """Find the common prefix/text string from a list of filenames."""
    if not filenames:
        return "merged"

    # Remove file extensions
    names_without_ext = [os.path.splitext(name)[0] for name in filenames]

    # Find common prefix
    if len(names_without_ext) == 1:
        return names_without_ext[0]

    # Find the longest common prefix
    common_prefix = ""
    min_length = min(len(name) for name in names_without_ext)

    for i in range(min_length):
        char = names_without_ext[0][i]
        if all(name[i] == char for name in names_without_ext):
            common_prefix += char
        else:
            break

    # Clean up the prefix (remove trailing underscores, hyphens, spaces)
    common_prefix = common_prefix.rstrip('_- ')

    # If no meaningful common prefix found, try to extract meaningful parts
    if len(common_prefix) < 3:
        # Look for common words or patterns
        words_sets = [set(name.replace('_', ' ').replace('-', ' ').split()) for name in names_without_ext]
        common_words = set.intersection(*words_sets) if words_sets else set()
        if common_words:
            # Sort words and join them
            common_prefix = '_'.join(sorted(common_words))
        else:
            common_prefix = "merged"

    return common_prefix if common_prefix else "merged"

def merge_csv_files():
    """Merge all CSV files in the current directory into a single file."""

    # Print welcome header
    print_header("CSV Files Merger")
    
    # Get the path from the user
    print_section("Input Path")
    path = input("Enter directory path: ").strip().strip('"\'')
    
    if not os.path.exists(path):
        rich_progress.print_status(f"Directory not found: {path}", "error")
        return
    
    os.chdir(path)
    rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")
    
    # Find all CSV files in the directory
    print_section("Finding CSV Files")
    csv_files = glob.glob('*.csv')
    if not csv_files:
        rich_progress.print_status("No CSV files found in the directory.", "error")
        return
    
    rich_progress.print_status(f"Found {len(csv_files)} CSV files:", "success")
    for i, file in enumerate(csv_files, 1):
        rich_progress.print_status(f"  {i}. {file}", "info")
    
    # Create merged directory if it doesn't exist
    print_section("Output Configuration")
    merged_dir = os.path.join(os.getcwd(), "merged")
    os.makedirs(merged_dir, exist_ok=True)
    rich_progress.print_status(f"Output directory: {merged_dir}", "info")

    # Generate default filename based on common text string in files
    common_text = find_common_prefix(csv_files)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    default_output = f"{common_text}_merged_{timestamp}.csv"

    rich_progress.print_status(f"Detected common text: '{common_text}'", "info")

    # Ask for output filename
    output_filename = input(f"Enter output filename (default: {default_output}): ").strip()
    if not output_filename:
        output_filename = default_output

    # Full path to output file in merged directory
    output_file = os.path.join(merged_dir, output_filename)
    
    # Initialize progress bar
    print_section("Processing Files")
    total_steps = len(csv_files) + 2  # +2 for combining and saving
    progress_bar, update_progress = rich_progress.create_progress_bar(
        total=total_steps,
        description="Merging CSV files",
        color_scheme="blue"
    )
    
    # Read and process each CSV file
    all_dataframes = []
    total_input_rows = 0
    encoding_info = {}

    for i, file in enumerate(csv_files):
        # Detect encoding for this file
        detected_encoding = detect_file_encoding(file)
        encoding_info[file] = detected_encoding

        try:
            rich_progress.print_status(f"Reading {file} (encoding: {detected_encoding})...", "info")

            # Read with detected encoding and additional pandas parameters for better character handling
            df = pd.read_csv(
                file,
                encoding=detected_encoding,
                keep_default_na=False,  # Preserve empty strings instead of converting to NaN
                na_filter=False,        # Don't interpret strings as NaN
                dtype=str              # Read all columns as strings to preserve formatting
            )

            # Clean up any potential encoding artifacts in string columns
            for col in df.columns:
                if df[col].dtype == 'object':  # String columns
                    # Fix common encoding issues with comprehensive character mapping
                    df[col] = df[col].astype(str).replace({
                        # Common punctuation issues
                        'â€™': "'",      # Right single quotation mark
                        'â€œ': '"',      # Left double quotation mark
                        'â€': '"',       # Right double quotation mark
                        'â€"': '–',      # En dash
                        'â€"': '—',      # Em dash

                        # Latin characters with diacritics (Ã encoding issues)
                        'Ã¼': 'ü',       # u with diaeresis
                        'Ã¡': 'á',       # a with acute
                        'Ã©': 'é',       # e with acute
                        'Ã­': 'í',       # i with acute
                        'Ã³': 'ó',       # o with acute
                        'Ãº': 'ú',       # u with acute
                        'Ã±': 'ñ',       # n with tilde
                        'Ã§': 'ç',       # c with cedilla
                        'Ã ': 'à',       # a with grave
                        'Ã¨': 'è',       # e with grave
                        'Ã¬': 'ì',       # i with grave
                        'Ã²': 'ò',       # o with grave
                        'Ã¹': 'ù',       # u with grave
                        'Ã¢': 'â',       # a with circumflex
                        'Ãª': 'ê',       # e with circumflex
                        'Ã®': 'î',       # i with circumflex
                        'Ã´': 'ô',       # o with circumflex
                        'Ã»': 'û',       # u with circumflex
                        'Ã¤': 'ä',       # a with diaeresis
                        'Ã«': 'ë',       # e with diaeresis
                        'Ã¯': 'ï',       # i with diaeresis
                        'Ã¶': 'ö',       # o with diaeresis
                        'Ã¿': 'ÿ',       # y with diaeresis
                        'Ã…': 'Å',       # A with ring above
                        'Ã¥': 'å',       # a with ring above
                        'Ã†': 'Æ',       # AE ligature
                        'Ã¦': 'æ',       # ae ligature
                        'Ã˜': 'Ø',       # O with stroke
                        'Ã¸': 'ø',       # o with stroke

                        # Fraction character encoding issues (1⁄4 = ¼)
                        'A1⁄4': 'Ä',     # A with diaeresis
                        'O1⁄4': 'Ö',     # O with diaeresis
                        'U1⁄4': 'Ü',     # U with diaeresis
                        'a1⁄4': 'ä',     # a with diaeresis
                        'o1⁄4': 'ö',     # o with diaeresis
                        'u1⁄4': 'ü',     # u with diaeresis
                        'ss1⁄4': 'ß',    # German sharp s

                        # Specific name fixes
                        'TA1⁄4lay': 'Tülay',      # Turkish name
                        'KA1⁄4hnel': 'Kühnel',    # German name
                        'MA1⁄4ller': 'Müller',    # German name
                        'BA1⁄4rger': 'Bürger',    # German name
                        'GA1⁄4nther': 'Günther',  # German name
                        'HA1⁄4bner': 'Hübner',    # German name
                        'KA1⁄4hn': 'Kühn',        # German name
                        'LA1⁄4tke': 'Lütke',      # German name
                        'SA1⁄4ss': 'Süß',         # German name
                        'WA1⁄4rth': 'Würth',      # German name

                        # Additional common patterns
                        '1⁄4': 'ü',      # Generic fraction to ü replacement
                        '1⁄2': 'ö',      # Generic fraction to ö replacement (less common)
                        '3⁄4': 'ä',      # Generic fraction to ä replacement (less common)

                        'nan': ''        # Clean up pandas NaN strings
                    }, regex=False)

                    # Apply regex-based pattern matching for remaining fraction character issues
                    import re
                    df[col] = df[col].apply(lambda x: re.sub(r'([A-Za-z])1⁄4', r'\1ü', str(x)) if isinstance(x, str) else x)
                    df[col] = df[col].apply(lambda x: re.sub(r'([A-Za-z])1⁄2', r'\1ö', str(x)) if isinstance(x, str) else x)
                    df[col] = df[col].apply(lambda x: re.sub(r'([A-Za-z])3⁄4', r'\1ä', str(x)) if isinstance(x, str) else x)

            rows = len(df)
            total_input_rows += rows
            all_dataframes.append(df)
            rich_progress.print_status(f"  - Read {rows} rows from {file}", "success")
            update_progress(1, f"Read {file}")

        except Exception as e:
            rich_progress.print_status(f"  - Error reading {file} with {detected_encoding}: {str(e)}", "error")
            # Try fallback encodings with the same character preservation approach
            fallback_encodings = ['utf-8-sig', 'utf-8', 'cp1252', 'iso-8859-1', 'iso-8859-15', 'cp1250', 'latin-1']
            success = False

            for fallback_enc in fallback_encodings:
                if fallback_enc == detected_encoding:
                    continue  # Skip the one we already tried
                try:
                    df = pd.read_csv(
                        file,
                        encoding=fallback_enc,
                        keep_default_na=False,
                        na_filter=False,
                        dtype=str
                    )

                    # Apply the same comprehensive character cleaning
                    for col in df.columns:
                        if df[col].dtype == 'object':
                            df[col] = df[col].astype(str).replace({
                                # Punctuation
                                'â€™': "'", 'â€œ': '"', 'â€': '"', 'â€"': '–', 'â€"': '—',
                                # Latin diacritics
                                'Ã¼': 'ü', 'Ã¡': 'á', 'Ã©': 'é', 'Ã­': 'í', 'Ã³': 'ó', 'Ãº': 'ú',
                                'Ã±': 'ñ', 'Ã§': 'ç', 'Ã ': 'à', 'Ã¨': 'è', 'Ã¬': 'ì', 'Ã²': 'ò', 'Ã¹': 'ù',
                                'Ã¢': 'â', 'Ãª': 'ê', 'Ã®': 'î', 'Ã´': 'ô', 'Ã»': 'û',
                                'Ã¤': 'ä', 'Ã«': 'ë', 'Ã¯': 'ï', 'Ã¶': 'ö', 'Ã¿': 'ÿ',
                                'Ã…': 'Å', 'Ã¥': 'å', 'Ã†': 'Æ', 'Ã¦': 'æ', 'Ã˜': 'Ø', 'Ã¸': 'ø',
                                # Fraction character issues
                                'A1⁄4': 'Ä', 'O1⁄4': 'Ö', 'U1⁄4': 'Ü', 'a1⁄4': 'ä', 'o1⁄4': 'ö', 'u1⁄4': 'ü',
                                'ss1⁄4': 'ß', '1⁄4': 'ü', '1⁄2': 'ö', '3⁄4': 'ä',
                                # Specific names
                                'TA1⁄4lay': 'Tülay', 'KA1⁄4hnel': 'Kühnel', 'MA1⁄4ller': 'Müller',
                                'BA1⁄4rger': 'Bürger', 'GA1⁄4nther': 'Günther', 'HA1⁄4bner': 'Hübner',
                                'KA1⁄4hn': 'Kühn', 'LA1⁄4tke': 'Lütke', 'SA1⁄4ss': 'Süß', 'WA1⁄4rth': 'Würth',
                                'nan': ''
                            }, regex=False)

                            # Apply regex-based pattern matching for remaining cases
                            import re
                            df[col] = df[col].apply(lambda x: re.sub(r'([A-Za-z])1⁄4', r'\1ü', str(x)) if isinstance(x, str) else x)
                            df[col] = df[col].apply(lambda x: re.sub(r'([A-Za-z])1⁄2', r'\1ö', str(x)) if isinstance(x, str) else x)
                            df[col] = df[col].apply(lambda x: re.sub(r'([A-Za-z])3⁄4', r'\1ä', str(x)) if isinstance(x, str) else x)

                    rows = len(df)
                    total_input_rows += rows
                    all_dataframes.append(df)
                    encoding_info[file] = fallback_enc
                    rich_progress.print_status(f"  - Read {rows} rows from {file} (fallback: {fallback_enc})", "success")
                    update_progress(1, f"Read {file}")
                    success = True
                    break
                except Exception as fallback_error:
                    rich_progress.print_status(f"  - Failed with {fallback_enc}: {str(fallback_error)}", "warning")
                    continue

            if not success:
                rich_progress.print_status(f"  - Failed to read {file} with any encoding", "error")
                update_progress(1, f"Failed {file}")
    
    if not all_dataframes:
        rich_progress.print_status("No CSV files could be read successfully.", "error")
        progress_bar.stop()
        return
    
    # Combine all dataframes
    rich_progress.print_status("Combining all data...", "info")
    try:
        merged_df = pd.concat(all_dataframes, ignore_index=True)
        update_progress(1, "Combined data")
        rich_progress.print_status(f"Successfully combined {len(all_dataframes)} files", "success")
    except Exception as e:
        rich_progress.print_status(f"Error combining data: {str(e)}", "error")
        progress_bar.stop()
        return
    
    # Save the merged file with enhanced character preservation
    rich_progress.print_status(f"Saving to {output_file}...", "info")
    try:
        # Ensure all string columns are properly encoded and normalized
        for col in merged_df.columns:
            if merged_df[col].dtype == 'object':
                # Convert to string and ensure proper Unicode handling
                merged_df[col] = merged_df[col].astype(str)
                # Remove any remaining 'nan' strings
                merged_df[col] = merged_df[col].replace('nan', '', regex=False)
                # Normalize Unicode characters to ensure consistency
                try:
                    import unicodedata
                    merged_df[col] = merged_df[col].apply(
                        lambda x: unicodedata.normalize('NFC', x) if isinstance(x, str) and x else x
                    )
                except ImportError:
                    pass  # Skip normalization if unicodedata not available

        rich_progress.print_status("Applying final character preservation measures...", "info")

        # Save with UTF-8 BOM for maximum compatibility and character preservation
        merged_df.to_csv(
            output_file,
            index=False,
            encoding='utf-8-sig',
            errors='replace'  # Replace any problematic characters instead of failing
        )

        output_rows = len(merged_df)
        update_progress(1, "Saved file")
        rich_progress.print_status(f"Successfully saved {output_rows} rows with UTF-8-sig encoding", "success")

        # Verify the saved file by reading a sample and checking for character preservation
        try:
            verification_df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=10)
            rich_progress.print_status("File verification: Successfully read back saved file with UTF-8-sig", "success")

            # Check for any remaining character issues in the verification sample
            char_issues_found = False
            for col in verification_df.columns:
                if verification_df[col].dtype == 'object':
                    sample_text = ' '.join(verification_df[col].astype(str).head(5))
                    if any(seq in sample_text for seq in ['â€™', 'â€œ', 'â€', 'Ã¼', 'Ã¡', 'TA1⁄4']):
                        char_issues_found = True
                        break

            if char_issues_found:
                rich_progress.print_status("Warning: Some character encoding issues may still exist", "warning")
            else:
                rich_progress.print_status("Character verification: No encoding artifacts detected", "success")

        except Exception as verify_error:
            rich_progress.print_status(f"File verification warning: {str(verify_error)}", "warning")

    except Exception as e:
        rich_progress.print_status(f"Error saving file: {str(e)}", "error")
        # Try a simpler save approach as fallback
        try:
            rich_progress.print_status("Attempting fallback save method...", "warning")
            merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            rich_progress.print_status("Fallback save successful", "success")
        except Exception as fallback_error:
            rich_progress.print_status(f"Fallback save also failed: {str(fallback_error)}", "error")
            progress_bar.stop()
            return
    
    # Close progress bar
    progress_bar.stop()
    
    # Print completion summary
    print_header("Merge Completed Successfully!")
    rich_progress.print_status(f"Files processed: {len(csv_files)}", "success")
    rich_progress.print_status(f"Total input rows: {total_input_rows}", "info")
    rich_progress.print_status(f"Output rows: {len(merged_df)}", "info")
    rich_progress.print_status(f"Output file: {output_file}", "success")

    # Show encoding information
    print_section("Encoding Information")
    for file, encoding in encoding_info.items():
        rich_progress.print_status(f"  - {file}: {encoding}", "info")

    # Show column information
    print_section("Column Information")
    rich_progress.print_status(f"Columns in merged file: {len(merged_df.columns)}", "info")
    for col in merged_df.columns:
        rich_progress.print_status(f"  - {col}", "info")

if __name__ == "__main__":
    try:
        merge_csv_files()
    except KeyboardInterrupt:
        rich_progress.print_status("\nOperation cancelled by user.", "warning")
    except Exception as e:
        rich_progress.print_status(f"Unexpected error: {str(e)}", "error")
    
    print("\nPress Enter to exit...")
    input()
