import os
import shutil
import re
from pathlib import Path
from datetime import datetime

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"  {timestamp}")
    print(f"{'='*60}")

def print_section(title):
    """Print a section header."""
    print(f"\n>> {title}")
    print("-" * (len(title) + 4))

def organize_files_by_country(folder_path, batch_mode=False, folder_index=None, total_folders=None):
    """
    Organizes files by extracting country names from filenames and moving them to country folders.

    Expected filename formats:
    - Something_CountryName_Something.extension (e.g., Biofuels_Germany_May.xlsx)
    - Something_Something_Something_CountryName.extension (e.g., IOC_Speaker_May_Australia.xlsx)

    Files without a country name in the expected format will be moved to an "Others" folder.

    Args:
        folder_path (str): Path to the folder containing files to organize
        batch_mode (bool): Whether running in batch mode
        folder_index (int): Current folder index (for batch mode)
        total_folders (int): Total number of folders (for batch mode)

    Returns:
        dict: Summary statistics of the organization process
    """
    # List of known countries to check against
    countries = [
        "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Argentina", "Armenia", "Australia",
    "Austria", "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium",
    "Belize", "Benin", "Bhutan", "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei",
    "Bulgaria", "Burkina Faso", "Burundi", "Cambodia", "Cameroon", "Canada", "Chad", "Chile", "China",
    "Colombia", "Comoros", "Democratic Republic of the Congo", "Republic of the Congo", "Croatia",
    "Cuba", "Cyprus", "Czech Republic", "Denmark", "Djibouti", "Dominica", "Dominican Republic",
    "Ecuador", "Egypt", "Eritrea", "Estonia", "Eswatini", "Ethiopia", "Fiji", "Finland", "France",
    "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala", "Guinea",
    "Guinea-Bissau", "Guyana", "Haiti", "Honduras", "Hungary", "Iceland", "India", "Indonesia", "Iran",
    "Iraq", "Ireland", "Israel", "Italy", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati",
    "North Korea", "South Korea", "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon",
    "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg", "Madagascar", "Malawi",
    "Malaysia", "Maldives", "Mali", "Malta", "Mauritania", "Mauritius", "Mexico", "Micronesia",
    "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", "Myanmar", "Namibia",
    "Nauru", "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger", "Nigeria", "North Macedonia",
    "Norway", "Oman", "Pakistan", "Palau", "Palestine", "Panama", "Papua New Guinea", "Paraguay",
    "Peru", "Philippines", "Poland", "Portugal", "Qatar", "Romania", "Russia", "Rwanda", "Samoa",
    "San Marino", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore",
    "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa", "South Sudan", "Spain",
    "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland", "Syria", "Taiwan", "Tajikistan",
    "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tonga", "Trinidad and Tobago", "Tunisia",
    "Turkey", "Turkmenistan", "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom",
    "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City", "Venezuela", "Vietnam",
    "Yemen", "Zambia", "Zimbabwe"
    ]
    
    # Initialize statistics
    stats = {
        'folder_path': folder_path,
        'total_files': 0,
        'organized_files': 0,
        'others_files': 0,
        'errors': 0,
        'countries_found': set()
    }

    # Print folder header for batch mode
    if batch_mode:
        print(f"\n[{folder_index}/{total_folders}] Processing: {folder_path}")
        print("-" * 80)

    # Ensure the folder path exists
    if not os.path.isdir(folder_path):
        error_msg = f"Error: '{folder_path}' is not a valid directory"
        print(error_msg)
        stats['errors'] = 1
        return stats

    # Store original directory to restore later
    original_dir = os.getcwd()

    try:
        # Change to the specified directory
        os.chdir(folder_path)

        # Get all files in the directory
        files = [f for f in os.listdir() if os.path.isfile(f)]

        if not files:
            print(f"  ⚠ No files found in {folder_path}")
            stats['total_files'] = 0
            return stats

        stats['total_files'] = len(files)
        print(f"  📁 Found {len(files)} files to organize")
    
        # Create Others folder for files without country name
        others_folder = "Others"
        if not os.path.exists(others_folder):
            os.makedirs(others_folder)
            if not batch_mode:
                print(f"  ✓ Created 'Others' folder for files without country name")
    
        # Process each file
        for i, filename in enumerate(files, 1):
            try:
                # Split the filename by underscores
                parts = os.path.splitext(filename)[0].split('_')

                # Check each part against the countries list
                country = None
                for part in parts:
                    if part in countries:
                        country = part
                        break

                # If no exact match, try case-insensitive matching
                if not country:
                    for part in parts:
                        for c in countries:
                            if part.lower() == c.lower():
                                country = c  # Use the proper case from the list
                                break
                        if country:
                            break

                # Special handling for multi-word countries that might be separated by underscores
                if not country:
                    # Try to reconstruct multi-word country names from consecutive parts
                    for idx in range(len(parts)):
                        for j in range(idx + 1, len(parts) + 1):
                            # Join consecutive parts with spaces
                            potential_country = ' '.join(parts[idx:j])
                            for c in countries:
                                if potential_country.lower() == c.lower():
                                    country = c  # Use the proper case from the list
                                    break
                            if country:
                                break
                        if country:
                            break

                if country:
                    if not batch_mode:
                        print(f"  [{i}/{len(files)}] File: {filename} -> Country: {country}")

                    # Create country folder if it doesn't exist
                    if not os.path.exists(country):
                        os.makedirs(country)
                        if not batch_mode:
                            print(f"    ✓ Created folder: {country}")

                    # Move file to country folder
                    destination = os.path.join(country, filename)
                    shutil.move(filename, destination)
                    if not batch_mode:
                        print(f"    ✓ Moved to {destination}")

                    stats['organized_files'] += 1
                    stats['countries_found'].add(country)
                else:
                    # Move to Others folder if no country name found
                    if not batch_mode:
                        print(f"  [{i}/{len(files)}] No country name found in: {filename}")
                    destination = os.path.join(others_folder, filename)
                    shutil.move(filename, destination)
                    if not batch_mode:
                        print(f"    ✓ Moved to {others_folder}/{filename}")

                    stats['others_files'] += 1

            except Exception as e:
                print(f"    ✗ Error processing {filename}: {str(e)}")
                stats['errors'] += 1

        # Print summary for this folder
        if batch_mode:
            print(f"  ✓ Organized: {stats['organized_files']} files, Others: {stats['others_files']} files")
            if stats['countries_found']:
                print(f"  📍 Countries found: {', '.join(sorted(stats['countries_found']))}")
        else:
            print(f"\n  📊 Organization complete for {folder_path}!")

    finally:
        # Restore original directory
        os.chdir(original_dir)

    return stats

def process_batch_from_file(txt_file_path):
    """
    Process multiple folders from a text file containing folder paths.

    Args:
        txt_file_path (str): Path to the text file containing folder paths (one per line)

    Returns:
        dict: Overall batch processing statistics
    """
    print_header("Batch Country Organization Tool")

    # Check if the text file exists
    if not os.path.exists(txt_file_path):
        print(f"❌ Error: Text file '{txt_file_path}' not found!")
        return None

    # Read folder paths from the text file
    print_section("Reading Folder Paths")
    try:
        with open(txt_file_path, 'r', encoding='utf-8') as f:
            folder_paths = [line.strip() for line in f.readlines() if line.strip()]
    except Exception as e:
        print(f"❌ Error reading text file: {str(e)}")
        return None

    if not folder_paths:
        print(f"❌ No folder paths found in '{txt_file_path}'")
        return None

    print(f"📁 Found {len(folder_paths)} folder paths to process:")
    for i, path in enumerate(folder_paths, 1):
        print(f"  {i}. {path}")

    # Confirm processing
    print_section("Confirmation")
    confirm = input(f"\nProceed with organizing files in {len(folder_paths)} folders? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ Operation cancelled by user.")
        return None

    # Initialize batch statistics
    batch_stats = {
        'total_folders': len(folder_paths),
        'successful_folders': 0,
        'failed_folders': 0,
        'total_files_processed': 0,
        'total_organized': 0,
        'total_others': 0,
        'total_errors': 0,
        'all_countries_found': set(),
        'folder_results': []
    }

    # Process each folder
    print_header("Processing Folders")
    for i, folder_path in enumerate(folder_paths, 1):
        try:
            stats = organize_files_by_country(folder_path, batch_mode=True, folder_index=i, total_folders=len(folder_paths))

            # Update batch statistics
            if stats['errors'] == 0 or stats['total_files'] > 0:
                batch_stats['successful_folders'] += 1
            else:
                batch_stats['failed_folders'] += 1

            batch_stats['total_files_processed'] += stats['total_files']
            batch_stats['total_organized'] += stats['organized_files']
            batch_stats['total_others'] += stats['others_files']
            batch_stats['total_errors'] += stats['errors']
            batch_stats['all_countries_found'].update(stats['countries_found'])
            batch_stats['folder_results'].append(stats)

        except Exception as e:
            print(f"❌ Failed to process folder {i}: {folder_path}")
            print(f"   Error: {str(e)}")
            batch_stats['failed_folders'] += 1

    # Print final summary
    print_header("Batch Processing Complete!")
    print(f"📊 Overall Statistics:")
    print(f"   • Total folders processed: {batch_stats['total_folders']}")
    print(f"   • Successful folders: {batch_stats['successful_folders']}")
    print(f"   • Failed folders: {batch_stats['failed_folders']}")
    print(f"   • Total files processed: {batch_stats['total_files_processed']}")
    print(f"   • Files organized by country: {batch_stats['total_organized']}")
    print(f"   • Files moved to Others: {batch_stats['total_others']}")
    print(f"   • Processing errors: {batch_stats['total_errors']}")

    if batch_stats['all_countries_found']:
        print(f"\n🌍 Countries found across all folders:")
        countries_list = sorted(batch_stats['all_countries_found'])
        for i in range(0, len(countries_list), 5):  # Print 5 countries per line
            print(f"   {', '.join(countries_list[i:i+5])}")

    return batch_stats

def main():
    """Main function to handle both single folder and batch processing."""
    print_header("Country File Organization Tool")

    print_section("Processing Mode Selection")
    print("Choose processing mode:")
    print("1. Single folder - Organize files in one folder")
    print("2. Batch processing - Organize files in multiple folders from text file")

    while True:
        choice = input("\nEnter your choice (1 or 2): ").strip()
        if choice in ['1', '2']:
            break
        print("❌ Invalid choice. Please enter 1 or 2.")

    if choice == '1':
        # Single folder processing
        print_section("Single Folder Processing")
        folder_path = input("Enter the folder path containing files to organize: ").strip()
        if folder_path:
            stats = organize_files_by_country(folder_path)
            print(f"\n✅ Processing complete!")
        else:
            print("❌ No folder path provided.")

    elif choice == '2':
        # Batch processing
        print_section("Batch Processing Setup")
        txt_file_path = input("Enter the path to text file containing folder paths: ").strip()
        if txt_file_path:
            batch_stats = process_batch_from_file(txt_file_path)
            if batch_stats:
                print(f"\n✅ Batch processing complete!")
        else:
            print("❌ No text file path provided.")

if __name__ == "__main__":
    main()


