import pandas as pd
import re
import os

def parse_keywords_from_excel(file_path):
    """
    Parse keywords from Excel file and create structured output
    """
    try:
        # Read the Excel file
        df = pd.read_excel(file_path)
        
        # Display the structure of the file
        print("File structure:")
        print(f"Columns: {list(df.columns)}")
        print(f"Shape: {df.shape}")
        print("\nFirst few rows:")
        print(df.head())
        
        # Check if the expected columns exist
        if 'Main Keyword' not in df.columns or 'Sub Keywords' not in df.columns:
            print(f"Expected columns 'Main Keyword' and 'Sub Keywords' not found.")
            print(f"Available columns: {list(df.columns)}")
            return
        
        # Process each row
        results = []
        
        for index, row in df.iterrows():
            main_keyword = row['Main Keyword']
            sub_keywords_text = str(row['Sub Keywords'])
            
            if pd.isna(main_keyword) or pd.isna(sub_keywords_text) or sub_keywords_text == 'nan':
                continue
                
            print(f"\nProcessing: {main_keyword}")
            print(f"Sub keywords text: {sub_keywords_text[:100]}...")
            
            # Parse sub keywords - handle different separators
            keywords = []
            
            # Split by different patterns
            # Pattern 1: [TITLE] OR or [TI] OR
            if '[TITLE] OR' in sub_keywords_text or '[TI] OR' in sub_keywords_text:
                # Replace the patterns and split
                text = sub_keywords_text.replace('[TITLE] OR', '|').replace('[TI] OR', '|')
                keywords = [kw.strip() for kw in text.split('|') if kw.strip()]
            
            # Pattern 2: Comma separated
            elif ',' in sub_keywords_text:
                keywords = [kw.strip() for kw in sub_keywords_text.split(',') if kw.strip()]
            
            # Pattern 3: Single keyword or other separators
            else:
                # Try other common separators
                for sep in [';', '|', '\n', ' OR ', ' AND ']:
                    if sep in sub_keywords_text:
                        keywords = [kw.strip() for kw in sub_keywords_text.split(sep) if kw.strip()]
                        break
                
                # If no separator found, treat as single keyword
                if not keywords:
                    keywords = [sub_keywords_text.strip()]
            
            # Clean up keywords - remove empty strings and extra whitespace
            keywords = [kw.strip() for kw in keywords if kw.strip()]
            
            # Remove any remaining brackets or formatting
            cleaned_keywords = []
            for kw in keywords:
                # Remove common patterns like [TITLE], [TI], etc.
                cleaned_kw = re.sub(r'\[.*?\]', '', kw).strip()
                if cleaned_kw:
                    cleaned_keywords.append(cleaned_kw)
            
            results.append({
                'main_keyword': main_keyword,
                'keywords': cleaned_keywords,
                'keyword_count': len(cleaned_keywords)
            })
            
            print(f"Extracted {len(cleaned_keywords)} keywords")
        
        return results
        
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def create_output_sheets(results, output_file="keyword_sheets.xlsx"):
    """
    Create separate Excel worksheets for each main keyword
    """
    from collections import defaultdict

    # Group results by main keyword (handle duplicates)
    grouped_results = defaultdict(list)
    for result in results:
        main_keyword = result['main_keyword']
        grouped_results[main_keyword].extend(result['keywords'])

    # Remove duplicates while preserving order
    for main_keyword in grouped_results:
        seen = set()
        unique_keywords = []
        for keyword in grouped_results[main_keyword]:
            if keyword not in seen:
                seen.add(keyword)
                unique_keywords.append(keyword)
        grouped_results[main_keyword] = unique_keywords

    summary = []

    # Create Excel writer object
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:

        for main_keyword, keywords in grouped_results.items():
            # Create safe sheet name (Excel sheet names have limitations)
            safe_sheet_name = re.sub(r'[^\w\s-]', '', main_keyword).strip()
            safe_sheet_name = re.sub(r'[-\s]+', '_', safe_sheet_name)
            # Excel sheet names must be <= 31 characters
            if len(safe_sheet_name) > 31:
                safe_sheet_name = safe_sheet_name[:31]

            # Create DataFrame for this sheet
            keyword_data = []
            for i, keyword in enumerate(keywords, 1):
                keyword_data.append({
                    'Serial No.': i,
                    'Keyword': keyword
                })

            df_sheet = pd.DataFrame(keyword_data)

            # Write to Excel sheet
            df_sheet.to_excel(writer, sheet_name=safe_sheet_name, index=False)

            summary.append({
                'Main Keyword': main_keyword,
                'Sheet Name': safe_sheet_name,
                'Keyword Count': len(keywords)
            })

            print(f"Created sheet '{safe_sheet_name}' with {len(keywords)} keywords")

        # Create summary sheet
        summary_df = pd.DataFrame(summary)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        print(f"Created summary sheet")

    print(f"\nExcel file saved as: {output_file}")
    return summary

if __name__ == "__main__":
    # File path
    file_path = "DBMS Working Sheet(keywords list).xlsx"
    
    print("Starting keyword extraction...")
    results = parse_keywords_from_excel(file_path)
    
    if results:
        print(f"\nProcessed {len(results)} main keywords")
        
        # Create output sheets
        summary = create_output_sheets(results)
        
        print("\nSummary:")
        for item in summary:
            print(f"- {item['Main Keyword']}: {item['Keyword Count']} keywords")
    else:
        print("Failed to process the file.")
