#!/usr/bin/env python3
"""
Professional Data Engineering Script for Excel and CSV File Merging
==================================================================

This script provides enterprise-grade data merging capabilities with:
- Automatic encoding detection and conversion to UTF-8-SIG
- Support for both Excel (.xlsx, .xls) and CSV files
- Comprehensive character preservation for international data
- Standardized column mapping (Author Name, Email, Article Title)
- Professional error handling and logging
- Output directory management

Author: Data Engineering Team
Version: 1.0.0
Date: 2026-01-21
"""

import os
import sys
import pandas as pd
import glob
import chardet
import re
import unicodedata
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_merger.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ProfessionalDataMerger:
    """
    Professional data merger for Excel and CSV files with encoding management.
    """
    
    def __init__(self):
        self.supported_extensions = {'.csv', '.xlsx', '.xls'}
        self.standard_columns = ['Author Name', 'Email', 'Article Title']
        self.encoding_fixes = self._get_encoding_fixes()
        
    def _get_encoding_fixes(self) -> Dict[str, str]:
        """Get comprehensive character encoding fixes mapping."""
        return {
            # Common punctuation issues
            'â€™': "'", 'â€œ': '"', 'â€': '"', 'â€"': '–', 'â€"': '—', 'â€¦': '…',
            
            # Latin characters with diacritics (Ã encoding issues)
            'Ã¼': 'ü', 'Ã¡': 'á', 'Ã©': 'é', 'Ã­': 'í', 'Ã³': 'ó', 'Ãº': 'ú',
            'Ã±': 'ñ', 'Ã§': 'ç', 'Ã ': 'à', 'Ã¨': 'è', 'Ã¬': 'ì', 'Ã²': 'ò',
            'Ã¹': 'ù', 'Ã¢': 'â', 'Ãª': 'ê', 'Ã®': 'î', 'Ã´': 'ô', 'Ã»': 'û',
            'Ã¤': 'ä', 'Ã«': 'ë', 'Ã¯': 'ï', 'Ã¶': 'ö', 'Ã¿': 'ÿ', 'Ã…': 'Å',
            'Ã¥': 'å', 'Ã†': 'Æ', 'Ã¦': 'æ', 'Ã˜': 'Ø', 'Ã¸': 'ø', 'Ã': 'Ý',
            'Ã½': 'ý', 'Ãƒ': 'Ã', 'Ã£': 'ã', 'Ã•': 'Õ', 'Ãµ': 'õ',
            
            # Fraction character encoding issues
            'A1⁄4': 'Ä', 'O1⁄4': 'Ö', 'U1⁄4': 'Ü', 'a1⁄4': 'ä', 'o1⁄4': 'ö',
            'u1⁄4': 'ü', 'ss1⁄4': 'ß', '1⁄4': 'ü', '1⁄2': 'ö', '3⁄4': 'ä',
            
            # Clean up pandas artifacts
            'nan': '', 'None': '', 'NaN': ''
        }
    
    def fix_character_encoding(self, text: str) -> str:
        """Fix character encoding issues while preserving special characters."""
        if not isinstance(text, str) or text in ['nan', 'None', '']:
            return text
            
        # Apply direct replacements
        for old, new in self.encoding_fixes.items():
            text = text.replace(old, new)
        
        # Apply regex patterns for remaining issues
        text = re.sub(r'([A-Za-z])1⁄4', r'\1ü', text)
        text = re.sub(r'([A-Za-z])1⁄2', r'\1ö', text)
        text = re.sub(r'([A-Za-z])3⁄4', r'\1ä', text)
        
        # Normalize Unicode characters
        try:
            text = unicodedata.normalize('NFC', text)
        except:
            pass
            
        return text

    def detect_file_encoding(self, file_path: str) -> str:
        """Detect file encoding with fallback options."""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(100000)  # Read substantial amount for better detection
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']

                logger.info(f"Detected encoding for {file_path}: {encoding} (confidence: {confidence:.2f})")

                # Enhanced encoding preference list
                preferred_encodings = ['utf-8-sig', 'utf-8', 'cp1252', 'iso-8859-1', 'iso-8859-15', 'cp1250', 'cp1251', 'latin-1']

                # Use detected encoding if reliable
                if confidence >= 0.8 and encoding:
                    encoding_map = {
                        'ascii': 'utf-8-sig', 'utf-8': 'utf-8-sig', 'windows-1252': 'cp1252',
                        'windows-1250': 'cp1250', 'windows-1251': 'cp1251', 'iso-8859-1': 'iso-8859-1'
                    }
                    return encoding_map.get(encoding.lower(), encoding)

                # Test preferred encodings
                for enc in preferred_encodings:
                    try:
                        with open(file_path, 'r', encoding=enc) as test_file:
                            test_file.read(5000)
                            return enc
                    except:
                        continue

                return 'latin-1'  # Final fallback
        except Exception as e:
            logger.warning(f"Encoding detection failed for {file_path}: {e}")
            return 'latin-1'

    def convert_csv_to_utf8_sig(self, file_path: str) -> bool:
        """Convert CSV file to UTF-8-SIG encoding if needed."""
        try:
            detected_encoding = self.detect_file_encoding(file_path)

            # Skip if already UTF-8-SIG
            if detected_encoding.lower() in ['utf-8-sig', 'utf-8-sig']:
                logger.info(f"File {file_path} already in UTF-8-SIG format")
                return True

            logger.info(f"Converting {file_path} from {detected_encoding} to UTF-8-SIG")

            # Read with detected encoding
            df = pd.read_csv(file_path, encoding=detected_encoding, dtype=str, keep_default_na=False, na_filter=False)

            # Apply character fixes
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = df[col].apply(self.fix_character_encoding)

            # Write back with UTF-8-SIG
            df.to_csv(file_path, index=False, encoding='utf-8-sig', errors='replace')
            logger.info(f"Successfully converted {file_path} to UTF-8-SIG")
            return True

        except Exception as e:
            logger.error(f"Failed to convert {file_path} to UTF-8-SIG: {e}")
            return False

    def standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize column names to match expected format."""
        column_mapping = {}

        for col in df.columns:
            col_lower = col.lower().strip()

            # Map various column names to standard format
            if any(name in col_lower for name in ['author', 'name', 'full name']) and 'email' not in col_lower and 'e-mail' not in col_lower:
                column_mapping[col] = 'Author Name'
            elif any(name in col_lower for name in ['email', 'e-mail']):
                column_mapping[col] = 'Email'
            elif any(name in col_lower for name in ['article', 'title', 'subject']):
                column_mapping[col] = 'Article Title'

        if column_mapping:
            df = df.rename(columns=column_mapping)
            logger.info(f"Standardized columns: {column_mapping}")

        return df

    def read_file(self, file_path: str) -> Optional[pd.DataFrame]:
        """Read a single file (CSV or Excel) with proper encoding handling."""
        try:
            file_ext = Path(file_path).suffix.lower()

            if file_ext == '.csv':
                # Convert to UTF-8-SIG if needed
                self.convert_csv_to_utf8_sig(file_path)

                # Read CSV with UTF-8-SIG
                df = pd.read_csv(file_path, encoding='utf-8-sig', dtype=str, keep_default_na=False, na_filter=False)

            elif file_ext in ['.xlsx', '.xls']:
                # Read Excel file
                df = pd.read_excel(file_path, dtype=str, keep_default_na=False, na_filter=False)

            else:
                logger.warning(f"Unsupported file format: {file_ext}")
                return None

            # Apply character encoding fixes
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = df[col].apply(self.fix_character_encoding)

            # Standardize column names
            df = self.standardize_columns(df)

            logger.info(f"Successfully read {file_path}: {len(df)} rows, columns: {list(df.columns)}")
            return df

        except Exception as e:
            logger.error(f"Failed to read {file_path}: {e}")
            return None

    def merge_files(self, input_directory: str) -> bool:
        """Main method to merge all Excel and CSV files in a directory."""
        try:
            input_path = Path(input_directory)
            if not input_path.exists():
                logger.error(f"Input directory does not exist: {input_directory}")
                return False

            # Create output directory
            output_dir = input_path / "output"
            output_dir.mkdir(exist_ok=True)
            logger.info(f"Created output directory: {output_dir}")

            # Find all supported files
            all_files = []
            for ext in self.supported_extensions:
                all_files.extend(input_path.glob(f"*{ext}"))

            if not all_files:
                logger.error(f"No supported files found in {input_directory}")
                return False

            logger.info(f"Found {len(all_files)} files to merge: {[f.name for f in all_files]}")

            # Read and merge all files
            all_dataframes = []
            total_rows = 0

            for file_path in all_files:
                df = self.read_file(str(file_path))
                if df is not None:
                    all_dataframes.append(df)
                    total_rows += len(df)
                    logger.info(f"Added {len(df)} rows from {file_path.name}")

            if not all_dataframes:
                logger.error("No data could be read from any files")
                return False

            # Combine all dataframes
            logger.info("Merging all data...")
            merged_df = pd.concat(all_dataframes, ignore_index=True, sort=False)

            # Clean up duplicate columns and merge email columns
            if 'Email' in merged_df.columns and 'E-mail' in merged_df.columns:
                # Merge E-mail into Email column
                merged_df['Email'] = merged_df['Email'].fillna(merged_df['E-mail'])
                merged_df = merged_df.drop(columns=['E-mail'])
                logger.info("Merged 'E-mail' column into 'Email' column")

            # Ensure we have the standard columns in the right order
            final_columns = []
            for std_col in self.standard_columns:
                if std_col in merged_df.columns:
                    final_columns.append(std_col)

            # Add any remaining columns
            for col in merged_df.columns:
                if col not in final_columns:
                    final_columns.append(col)

            merged_df = merged_df[final_columns]

            # Remove duplicates based on Email if available, prioritizing entries with Author Name
            if 'Email' in merged_df.columns:
                original_count = len(merged_df)

                # Sort by Author Name (non-empty values first) to prioritize entries with author names
                if 'Author Name' in merged_df.columns:
                    # Create a sorting key: entries with author names come first
                    merged_df['_has_author'] = merged_df['Author Name'].notna() & (merged_df['Author Name'].str.strip() != '')
                    merged_df = merged_df.sort_values(['Email', '_has_author'], ascending=[True, False])
                    # Drop the helper column
                    merged_df = merged_df.drop(columns=['_has_author'])
                    logger.info("Sorted entries to prioritize those with Author Name when removing duplicates")

                # Now remove duplicates, keeping the first (which will be the one with author name if available)
                merged_df = merged_df.drop_duplicates(subset=['Email'], keep='first')
                duplicate_count = original_count - len(merged_df)
                if duplicate_count > 0:
                    logger.info(f"Removed {duplicate_count} duplicate email entries, prioritizing entries with Author Name")

            # Generate output filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = output_dir / f"merged_data_{timestamp}.csv"

            # Save merged data with UTF-8-SIG encoding
            merged_df.to_csv(output_file, index=False, encoding='utf-8-sig', errors='replace')

            logger.info(f"Successfully merged {len(all_files)} files into {output_file}")
            logger.info(f"Total rows: {len(merged_df)} (from {total_rows} original rows)")
            logger.info(f"Columns: {list(merged_df.columns)}")

            return True

        except Exception as e:
            logger.error(f"Failed to merge files: {e}")
            return False


def main():
    """Main function for command-line usage."""
    print("Professional Data Engineering - File Merger")
    print("=" * 50)

    # Get input directory
    if len(sys.argv) > 1:
        input_directory = sys.argv[1]
    else:
        input_directory = input("Enter the directory path containing Excel and CSV files: ").strip().strip('"\'')

    if not input_directory:
        print("Error: No directory specified")
        return

    # Initialize merger and process files
    merger = ProfessionalDataMerger()
    success = merger.merge_files(input_directory)

    if success:
        print("\n✅ Data merge completed successfully!")
        print(f"📁 Check the 'output' folder in {input_directory} for the merged file")
    else:
        print("\n❌ Data merge failed. Check the log for details.")

    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
