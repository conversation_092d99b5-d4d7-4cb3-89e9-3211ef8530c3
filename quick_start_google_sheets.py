"""
Quick Start Guide for Google Sheets Integration
This script demonstrates the most common operations you'll need
"""

import pandas as pd
from google_sheets_manager import GoogleSheetsManager
from datetime import datetime
import os

def quick_demo():
    """
    Quick demonstration of Google Sheets operations
    """
    print("🚀 GOOGLE SHEETS QUICK START DEMO")
    print("=" * 40)
    
    try:
        # Initialize the manager (using OAuth2 with your Google account)
        print("1. Initializing Google Sheets Manager...")
        gsm = GoogleSheetsManager()
        
        # Create sample data (similar to your email processing scripts)
        print("2. Creating sample data...")
        sample_data = {
            'Author Name': [
                '<PERSON>', '<PERSON>', '<PERSON>', 
                '<PERSON>', '<PERSON>', '<PERSON>'
            ],
            'Email': [
                '<EMAIL>', '<EMAIL>', 
                '<EMAIL>', '<EMAIL>',
                '<EMAIL>', '<EMAIL>'
            ],
            'Email status': ['valid', 'valid', 'invalid', 'valid', 'bounce', 'valid'],
            'Conference': ['AI-2024', 'ML-2024', 'AI-2024', 'Data-2024', 'ML-2024', 'AI-2024'],
            'Registration_Date': [
                '2024-01-15', '2024-01-16', '2024-01-17', 
                '2024-01-18', '2024-01-19', '2024-01-20'
            ],
            'Last_Updated': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')] * 6
        }
        df = pd.DataFrame(sample_data)
        print(f"   Created DataFrame with {len(df)} rows and {len(df.columns)} columns")
        
        # Create a new Google Sheet
        print("3. Creating new Google Sheet...")
        sheet_title = f"Demo_Sheet_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        new_sheet = gsm.create_new_sheet(sheet_title)
        worksheet = new_sheet.sheet1
        
        # Write data to the sheet
        print("4. Writing data to Google Sheet...")
        gsm.write_dataframe_to_sheet(worksheet, df)
        
        # Add some summary statistics
        print("5. Adding summary statistics...")
        summary_data = [
            ['SUMMARY STATISTICS', ''],
            ['Total Records', len(df)],
            ['Valid Emails', len(df[df['Email status'] == 'valid'])],
            ['Invalid Emails', len(df[df['Email status'] == 'invalid'])],
            ['Bounced Emails', len(df[df['Email status'] == 'bounce'])],
            ['', ''],
            ['CONFERENCE BREAKDOWN', ''],
        ]
        
        # Add conference counts
        conference_counts = df['Conference'].value_counts()
        for conf, count in conference_counts.items():
            summary_data.append([conf, count])
        
        # Update the summary range
        gsm.update_range(worksheet, f'H1:I{len(summary_data)}', summary_data)
        
        # Read the data back to verify
        print("6. Reading data back from Google Sheet...")
        read_df = gsm.read_sheet_to_dataframe(worksheet)
        print(f"   Successfully read {len(read_df)} rows")
        
        print(f"\n✅ Demo completed successfully!")
        print(f"📊 Your Google Sheet URL: {new_sheet.url}")
        print(f"📧 Share this sheet with others by adding their email addresses")
        
        return new_sheet.url
        
    except FileNotFoundError as e:
        print(f"❌ Credentials file not found: {e}")
        print("   Please run 'python setup_google_sheets.py' first")
        return None
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        return None

def process_existing_csv():
    """
    Process an existing CSV file and upload to Google Sheets
    """
    print("\n📁 PROCESS EXISTING CSV FILE")
    print("=" * 40)
    
    # Find CSV files in current directory
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
    
    if not csv_files:
        print("No CSV files found in current directory")
        return None
    
    print("Available CSV files:")
    for i, file in enumerate(csv_files, 1):
        print(f"  {i}. {file}")
    
    try:
        choice = int(input(f"\nSelect file (1-{len(csv_files)}): ")) - 1
        if choice < 0 or choice >= len(csv_files):
            print("Invalid selection")
            return None
        
        selected_file = csv_files[choice]
        print(f"Processing: {selected_file}")
        
        # Read CSV file
        df = pd.read_csv(selected_file, encoding='utf-8-sig')
        print(f"Loaded {len(df)} rows and {len(df.columns)} columns")
        
        # Initialize Google Sheets Manager
        gsm = GoogleSheetsManager()
        
        # Create new sheet
        base_name = os.path.splitext(selected_file)[0]
        sheet_title = f"{base_name}_GSheets_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        new_sheet = gsm.create_new_sheet(sheet_title)
        
        # Upload data
        worksheet = new_sheet.sheet1
        gsm.write_dataframe_to_sheet(worksheet, df)
        
        print(f"✅ CSV uploaded successfully!")
        print(f"📊 Google Sheet URL: {new_sheet.url}")
        
        return new_sheet.url
        
    except Exception as e:
        print(f"❌ Error processing CSV: {e}")
        return None

def update_existing_sheet():
    """
    Update an existing Google Sheet
    """
    print("\n📝 UPDATE EXISTING GOOGLE SHEET")
    print("=" * 40)
    
    try:
        # Get sheet URL from user
        sheet_url = input("Enter Google Sheet URL: ").strip()
        if not sheet_url:
            print("No URL provided")
            return None
        
        # Initialize Google Sheets Manager
        gsm = GoogleSheetsManager('service_account')
        
        # Open the sheet
        worksheet = gsm.open_sheet(sheet_url)
        
        # Read current data
        current_df = gsm.read_sheet_to_dataframe(worksheet)
        print(f"Current sheet has {len(current_df)} rows and {len(current_df.columns)} columns")
        
        # Show current data preview
        print("\nCurrent data preview:")
        print(current_df.head())
        
        # Add a timestamp column
        current_df['Last_Modified'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Write updated data back
        gsm.write_dataframe_to_sheet(worksheet, current_df)
        
        print("✅ Sheet updated successfully!")
        return sheet_url
        
    except Exception as e:
        print(f"❌ Error updating sheet: {e}")
        return None

def main():
    """
    Main function with menu options
    """
    print("🔗 GOOGLE SHEETS INTEGRATION - QUICK START")
    print("=" * 50)
    
    options = [
        ("Run Quick Demo (Create new sheet with sample data)", quick_demo),
        ("Process existing CSV file", process_existing_csv),
        ("Update existing Google Sheet", update_existing_sheet),
    ]
    
    print("\nSelect an option:")
    for i, (description, _) in enumerate(options, 1):
        print(f"  {i}. {description}")
    
    try:
        choice = int(input(f"\nEnter choice (1-{len(options)}): ")) - 1
        
        if 0 <= choice < len(options):
            result = options[choice][1]()
            
            if result:
                print(f"\n🎉 Operation completed successfully!")
                
                # Ask if user wants to share the sheet
                email = input("\nEnter email to share the sheet with (or press Enter to skip): ").strip()
                if email and result:
                    try:
                        gsm = GoogleSheetsManager('service_account')
                        sheet = gsm.gc.open_by_url(result)
                        gsm.share_sheet(sheet, email, 'writer')
                        print(f"✅ Sheet shared with {email}")
                    except Exception as e:
                        print(f"❌ Error sharing sheet: {e}")
            else:
                print("❌ Operation failed or was cancelled")
        else:
            print("Invalid choice")
            
    except ValueError:
        print("Please enter a valid number")
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
