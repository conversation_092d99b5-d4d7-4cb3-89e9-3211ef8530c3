@ECHO OFF
setlocal enabledelayedexpansion

echo Starting iremp.py at %date% %time%

REM Create logs directory if it doesn't exist
if not exist "C:\Users\<USER>\OneDrive\My Files\logs" mkdir "C:\Users\<USER>\OneDrive\My Files\logs"

set "LOG_FILE=C:\Users\<USER>\OneDrive\My Files\logs\rep_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo Starting iremp.py at %date% %time% > "%LOG_FILE%"

cd C:\Users\<USER>\OneDrive\My Files || (
    echo ERROR: Could not change to directory >> "%LOG_FILE%"
    echo ERROR: Could not change to directory
    exit /b 1
)

echo Running iremp.py...
echo Running iremp.py... >> "%LOG_FILE%"

python iremp.py 2>> "%LOG_FILE%"

if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    echo ERROR: Script failed with error code !errorlevel!
) else (
    echo SUCCESS: <PERSON>rip<PERSON> completed successfully at %date% %time% >> "%LOG_FILE%"
    echo SUCCESS: Script completed successfully
)

echo Log file saved to: %LOG_FILE%

endlocal