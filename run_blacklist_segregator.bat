@echo off
echo Blacklist Data Segregator
echo ========================
echo.
echo Choose an option:
echo 1. Preview data (sample 1000 rows)
echo 2. Preview full data
echo 3. Process all files (create segregated files)
echo 4. Process sample (1000 rows for testing)
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo.
    echo Running preview with 1000 rows sample...
    python blacklist_segregator_cli.py --preview --sample 1000
    pause
) else if "%choice%"=="2" (
    echo.
    echo Running full preview...
    python blacklist_segregator_cli.py --preview
    pause
) else if "%choice%"=="3" (
    echo.
    echo Processing all files...
    echo WARNING: This will create many output files!
    set /p confirm="Are you sure? (y/n): "
    if /i "%confirm%"=="y" (
        python blacklist_segregator.py
    ) else (
        echo Operation cancelled.
    )
    pause
) else if "%choice%"=="4" (
    echo.
    echo Processing sample data (1000 rows)...
    python blacklist_segregator_cli.py --sample 1000
    pause
) else if "%choice%"=="5" (
    echo Goodbye!
    exit /b
) else (
    echo Invalid choice. Please try again.
    pause
    goto :eof
)

echo.
echo Press any key to exit...
pause > nul
