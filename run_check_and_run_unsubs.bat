@echo off
echo ===============================================
echo    CSV CHECK AND UNSUBSCRIBE PROCESSING
echo ===============================================
echo Started at: %date% %time%
echo.

REM Set the scripts directory - using %~dp0 to get the directory where this batch file is located
set "SCRIPTS_DIR=%~dp0"

REM Create logs directory if it doesn't exist
if not exist "%SCRIPTS_DIR%logs" mkdir "%SCRIPTS_DIR%logs"

REM Create a log file
set "LOG_FILE=%SCRIPTS_DIR%logs\batch_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo Script execution started at %date% %time% > "%LOG_FILE%"

echo Running check_and_run_unsubs.py...
echo Running check_and_run_unsubs.py... >> "%LOG_FILE%"

REM Run the Python script
python "%SCRIPTS_DIR%check_and_run_unsubs.py"
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
)

echo.
echo ===============================================
echo Process completed at %date% %time%
echo ===============================================
echo Process completed at %date% %time% >> "%LOG_FILE%"

echo Log file saved to: %LOG_FILE%
echo.
pause
