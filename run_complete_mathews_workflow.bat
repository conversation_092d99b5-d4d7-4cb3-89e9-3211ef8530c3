@echo off
echo ========================================
echo Complete Mathews Workflow Process
echo ========================================
echo This will run the complete Mathews processing workflow:
echo 1. <PERSON> Unsubscribes and Replied Bounces Workflow
echo ========================================
echo.

:: Change to the working directory
cd /d "C:\Users\<USER>\OneDrive\My Files"

echo Running Complete Mathews Workflow
echo ===================================
echo Executing run_mathews_unsubs_workflow.bat...
echo.

call "run_mathews_unsubs_workflow.bat"

:: Check if the batch file executed successfully
if %errorlevel% neq 0 (
    echo.
    echo ERROR: run_mathews_unsubs_workflow.bat failed with error code %errorlevel%
    echo Complete workflow stopped. Please check the error and try again.
    pause
    exit /b %errorlevel%
)

echo.
echo ========================================
echo Complete Mathews Workflow Finished!
echo ========================================
echo All processes have completed successfully:
echo.
echo ✓ CMS Mathews Unsubs CSV Download
echo ✓ Mathews Postpanel Info PP Unsubs CSV Download
echo ✓ MI Conferences Postpanel Info PP Unsubs CSV Download
echo ✓ Concatenate CMS Unsubs
echo ✓ Concatenate Postpanel Unsubs
echo ✓ Mathews Unsubs Upload to DBMS
echo ✓ Separate Replied Bounces
echo ✓ Auto Rep Upload Mathews
echo.
echo The complete Mathews workflow is now finished!
echo.
echo Press any key to exit...
pause > nul
