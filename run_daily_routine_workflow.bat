@echo off
setlocal enabledelayedexpansion

:: Check if running in automated mode (scheduled task)
set "AUTOMATED_MODE=false"
if "%1"=="-auto" set "AUTOMATED_MODE=true"
if "%1"=="--automated" set "AUTOMATED_MODE=true"

:: Set console colors and title
title Daily Routine Workflow - Unsubscribe Data Processing
color 0A

echo ===============================================
echo    DAILY ROUTINE WORKFLOW
echo    UNSUBSCRIBE DATA PROCESSING
echo ===============================================
echo Started at: %date% %time%
echo.
echo This batch file will execute the following scripts in order:
echo 1. CMS Unsubs CSV Download
echo 2. MG Port1 PP Unsubs CSV Download
echo 3. Magnus Group Port1 PP Unsubs CSV Download
echo 4. Magnus SMTPS PP Unsubs CSV Download
echo 5. Email Invite Net Unsubs Download
echo 6. Email Invite Z Unsubs Download
echo 7. Daily Unsubs Upload to DBMS
echo 8. Upload Unsubs to DBMS
echo.
echo ===============================================
echo.

REM Clean up existing files before starting
echo Deleting all files in H:\Master Bounces and Unsubs\Postpanel Unsubs...
del /s /q "H:\Master Bounces and Unsubs\Postpanel Unsubs\*.*"
echo Cleanup completed.
echo.

REM Set the scripts directory - using %~dp0 to get the directory where this batch file is located
set "SCRIPTS_DIR=%~dp0"

REM Create logs directory if it doesn't exist
if not exist "%SCRIPTS_DIR%logs" mkdir "%SCRIPTS_DIR%logs"

REM Create a log file with locale-independent timestamp using PowerShell
for /f "tokens=*" %%a in ('powershell -Command "Get-Date -Format 'yyyyMMdd_HHmmss'"') do set TIMESTAMP=%%a
set "LOG_FILE=%SCRIPTS_DIR%logs\script_log_%TIMESTAMP%.txt"
echo Script execution started at %date% %time% > "%LOG_FILE%"
echo Automated Mode: %AUTOMATED_MODE% >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

REM Configuration: Retry settings
set "MAX_RETRIES=2"
set "RETRY_DELAY=5"

REM Count total scripts and track success/failure
set "TOTAL_SCRIPTS=8"
set "CURRENT_SCRIPT=1"
set "SUCCESS_COUNT=0"
set "FAILED_COUNT=0"
set "error_occurred=false"
set "critical_error=false"
set "SKIPPED_COUNT=0"
set "RETRY_COUNT=0"

REM ============================================================================
REM Function to run a script with retry logic
REM Usage: call :run_script <script_number> <script_name> <is_critical>
REM ============================================================================
goto :start_execution

:run_script
set "SCRIPT_NUM=%~1"
set "SCRIPT_NAME=%~2"
set "IS_CRITICAL=%~3"
set "ATTEMPT=1"

:retry_loop
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: %SCRIPT_NAME% (Attempt %ATTEMPT%/%MAX_RETRIES%)
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: %SCRIPT_NAME% (Attempt %ATTEMPT%/%MAX_RETRIES%) >> "%LOG_FILE%"

python "%SCRIPTS_DIR%%SCRIPT_NAME%"
set "EXIT_CODE=!errorlevel!"

if !EXIT_CODE! equ 0 (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_%SCRIPT_NUM%_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
    goto :run_script_end
)

REM Script failed - check if we should retry
set /a "ATTEMPT+=1"
if !ATTEMPT! gtr %MAX_RETRIES% (
    echo ERROR: Script failed with error code !EXIT_CODE! after %MAX_RETRIES% attempts
    echo ERROR: Script failed with error code !EXIT_CODE! after %MAX_RETRIES% attempts >> "%LOG_FILE%"
    if "%IS_CRITICAL%"=="true" (
        echo WARNING: This is a CRITICAL download script. Failure may affect data availability.
        echo WARNING: This is a CRITICAL download script. Failure may affect data availability. >> "%LOG_FILE%"
        set "critical_error=true"
    )
    set "SCRIPT_%SCRIPT_NUM%_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
    goto :run_script_end
)

REM Retry the script
echo WARNING: Script failed with error code !EXIT_CODE!. Retrying in %RETRY_DELAY% seconds...
echo WARNING: Script failed with error code !EXIT_CODE!. Retrying in %RETRY_DELAY% seconds... >> "%LOG_FILE%"
set /a "RETRY_COUNT+=1"
timeout /t %RETRY_DELAY% /nobreak > nul
goto :retry_loop

:run_script_end
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"
goto :eof

:start_execution
REM ============================================================================
REM Start executing scripts
REM ============================================================================

call :run_script 1 "cms_unsubs_csv_download.py" "true"
call :run_script 2 "mg.port1.in_pp_unsubs_csv_download.py" "true"
call :run_script 3 "magnusgroup.port1.in_pp_unsubs_csv_download.py" "true"
call :run_script 4 "magnus.smtps.in_pp_unsubs_csv_download.py" "true"
call :run_script 5 "1unsubs_from_emailinvite.net_2.0.py" "true"
call :run_script 6 "2unsubs_from_emailinvitez.com.py" "true"

REM Check if critical download scripts failed before running upload scripts
if "!critical_error!"=="true" (
    echo ===============================================
    echo    CRITICAL ERROR DETECTED!
    echo ===============================================
    echo.
    echo One or more CRITICAL download scripts failed.
    echo Skipping upload scripts to prevent uploading incomplete data.
    echo.
    echo Skipping: [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] daily_unsubs_upload2dbms.py
    echo Skipping: [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] daily_unsubs_upload2dbms.py >> "%LOG_FILE%"
    set "SCRIPT_7_STATUS=SKIPPED"
    set /a "SKIPPED_COUNT+=1"
    set /a "CURRENT_SCRIPT+=1"

    echo Skipping: [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] upl_unsubs_dbms.py
    echo Skipping: [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] upl_unsubs_dbms.py >> "%LOG_FILE%"
    set "SCRIPT_8_STATUS=SKIPPED"
    set /a "SKIPPED_COUNT+=1"
    echo.
    echo. >> "%LOG_FILE%"

    goto :show_summary
)

call :run_script 7 "daily_unsubs_upload2dbms.py" "false"
call :run_script 8 "upl_unsubs_dbms.py" "false"

:show_summary
:: Display completion summary
if "!error_occurred!"=="false" (
    echo ===============================================
    echo    DAILY ROUTINE WORKFLOW COMPLETED SUCCESSFULLY!
    echo ===============================================
    echo.
    echo All 8 scripts completed successfully at %date% %time%
    echo.
    echo Summary:
    echo [OK] CMS Unsubs CSV Download
    echo [OK] MG Port1 PP Unsubs CSV Download
    echo [OK] Magnus Group Port1 PP Unsubs CSV Download
    echo [OK] Magnus SMTPS PP Unsubs CSV Download
    echo [OK] Email Invite Net Unsubs Download
    echo [OK] Email Invite Z Unsubs Download
    echo [OK] Daily Unsubs Upload to DBMS
    echo [OK] Upload Unsubs to DBMS
    echo.
    echo Statistics:
    echo - Total Scripts: !TOTAL_SCRIPTS!
    echo - Successful: !SUCCESS_COUNT!
    echo - Failed: !FAILED_COUNT!
    echo - Skipped: !SKIPPED_COUNT!
    echo.
) else (
    echo ===============================================
    echo    DAILY ROUTINE WORKFLOW COMPLETED WITH ERRORS!
    echo ===============================================
    echo.
    echo Workflow completed at %date% %time%
    echo.
    echo Summary:
    if "!SCRIPT_1_STATUS!"=="SUCCESS" (echo [OK] CMS Unsubs CSV Download) else (echo [FAILED] CMS Unsubs CSV Download)
    if "!SCRIPT_2_STATUS!"=="SUCCESS" (echo [OK] MG Port1 PP Unsubs CSV Download) else (echo [FAILED] MG Port1 PP Unsubs CSV Download)
    if "!SCRIPT_3_STATUS!"=="SUCCESS" (echo [OK] Magnus Group Port1 PP Unsubs CSV Download) else (echo [FAILED] Magnus Group Port1 PP Unsubs CSV Download)
    if "!SCRIPT_4_STATUS!"=="SUCCESS" (echo [OK] Magnus SMTPS PP Unsubs CSV Download) else (echo [FAILED] Magnus SMTPS PP Unsubs CSV Download)
    if "!SCRIPT_5_STATUS!"=="SUCCESS" (echo [OK] Email Invite Net Unsubs Download) else (echo [FAILED] Email Invite Net Unsubs Download)
    if "!SCRIPT_6_STATUS!"=="SUCCESS" (echo [OK] Email Invite Z Unsubs Download) else (echo [FAILED] Email Invite Z Unsubs Download)
    if "!SCRIPT_7_STATUS!"=="SUCCESS" (echo [OK] Daily Unsubs Upload to DBMS) else if "!SCRIPT_7_STATUS!"=="SKIPPED" (echo [SKIPPED] Daily Unsubs Upload to DBMS) else (echo [FAILED] Daily Unsubs Upload to DBMS)
    if "!SCRIPT_8_STATUS!"=="SUCCESS" (echo [OK] Upload Unsubs to DBMS) else if "!SCRIPT_8_STATUS!"=="SKIPPED" (echo [SKIPPED] Upload Unsubs to DBMS) else (echo [FAILED] Upload Unsubs to DBMS)
    echo.
    echo Statistics:
    echo - Total Scripts: !TOTAL_SCRIPTS!
    echo - Successful: !SUCCESS_COUNT!
    echo - Failed: !FAILED_COUNT!
    echo - Skipped: !SKIPPED_COUNT!
    echo.
    if "!critical_error!"=="true" (
        echo CRITICAL ERROR: Download scripts failed. Upload scripts were skipped to prevent incomplete data upload.
        echo.
    )
    echo Please check the error messages above and fix any issues.
    echo.
)

echo All scripts completed at %date% %time% >> "%LOG_FILE%"
echo Total retries performed: !RETRY_COUNT! >> "%LOG_FILE%"
echo Log file saved to: %LOG_FILE%
echo.

REM Send email notification
echo Sending email notification...
python "%SCRIPTS_DIR%send_workflow_notification.py" "!error_occurred!" "!SUCCESS_COUNT!" "!FAILED_COUNT!" "!SKIPPED_COUNT!" "!critical_error!" "!RETRY_COUNT!" "!SCRIPT_1_STATUS!" "!SCRIPT_2_STATUS!" "!SCRIPT_3_STATUS!" "!SCRIPT_4_STATUS!" "!SCRIPT_5_STATUS!" "!SCRIPT_6_STATUS!" "!SCRIPT_7_STATUS!" "!SCRIPT_8_STATUS!" "%LOG_FILE%"
if !errorlevel! equ 0 (
    echo Email notification sent successfully.
) else (
    echo Warning: Failed to send email notification.
)
echo.

REM Only pause if not in automated mode
if "%AUTOMATED_MODE%"=="false" (
    echo Press any key to exit...
    pause > nul
) else (
    echo Running in automated mode - exiting without pause.
    echo Workflow completed. Exit code: !errorlevel!
)
