@echo off
echo ===============================================
echo         DBMS STATISTICS EXTRACTOR
echo ===============================================
echo Started at: %date% %time%
echo.

REM Set the scripts directory
set "SCRIPTS_DIR=%~dp0"

REM Create logs directory if it doesn't exist
set "LOGS_DIR=%SCRIPTS_DIR%logs"
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

REM Create a log file in the logs directory
set "LOG_FILE=%LOGS_DIR%\dbms_stats_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo DBMS Stats extraction started at %date% %time% > "%LOG_FILE%"

echo Running dbms_stats.py...
echo Running dbms_stats.py... >> "%LOG_FILE%"

REM Run the Python script
python "%SCRIPTS_DIR%dbms_stats.py"

REM Check if the script executed successfully
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: DBMS statistics extraction completed successfully
    echo SUCCESS: DBMS statistics extraction completed successfully >> "%LOG_FILE%"
)

echo.
echo ===============================================
echo Process completed at %date% %time%
echo ===============================================
echo Process completed at %date% %time% >> "%LOG_FILE%"

echo Log file saved to: %LOG_FILE%
echo.
echo Press any key to exit...
pause > nul
