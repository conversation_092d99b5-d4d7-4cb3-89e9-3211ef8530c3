@echo off
echo Installing required packages...
pip install -r thematic_requirements.txt

echo.
echo Running Thematic Keyword Analyzer for H:\thematic...
echo.

REM Check if H:\thematic exists
if not exist "H:\thematic" (
    echo Error: Directory H:\thematic does not exist!
    echo Please make sure:
    echo 1. The H: drive is accessible
    echo 2. The thematic folder exists
    echo 3. You have permission to access the directory
    echo.
    pause
    exit /b 1
)

echo Processing Excel files in H:\thematic...
python thematic_keyword_analyzer.py "H:\thematic"

echo.
echo Processing complete! Check the directory for files with '_with_themes.xlsx' suffix.
pause
