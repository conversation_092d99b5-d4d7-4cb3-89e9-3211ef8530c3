@echo off
echo Installing required packages...
pip install -r thematic_requirements.txt

echo.
echo Thematic Keyword Analyzer
echo ========================
echo.
echo This script will analyze research titles in Excel files and generate thematic keywords.
echo The keywords will be added to columns named 'Thematic 1', 'Thematic 2', etc.
echo.
echo Usage examples:
echo   For a single file: python thematic_keyword_analyzer.py "C:\path\to\file.xlsx"
echo   For a directory: python thematic_keyword_analyzer.py "H:\thematic"
echo   With custom column: python thematic_keyword_analyzer.py "H:\thematic" --title-column "Research Title"
echo.

:input_path
set /p path="Enter the full path to your Excel file or directory: "

REM Remove quotes if present
set path=%path:"=%

REM Check if path exists
if not exist "%path%" (
    echo.
    echo Error: Path "%path%" does not exist!
    echo Please check the path and try again.
    echo.
    goto input_path
)

echo.
echo Processing: %path%
python thematic_keyword_analyzer.py "%path%"

echo.
echo Processing complete!
echo Look for files with '_with_themes.xlsx' suffix for the results.
echo.
pause
