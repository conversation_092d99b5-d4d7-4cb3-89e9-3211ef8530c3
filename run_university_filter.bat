@echo off
REM University Email Filter - Easy Batch Script
REM This script makes it easy to run the university email filter

echo ========================================
echo University Email Filter - Batch Runner
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

echo Python is available. Starting university email filter...
echo.

REM Prompt for directory path
set /p "csv_dir=Enter the path to your CSV files directory: "

REM Check if directory exists
if not exist "%csv_dir%" (
    echo ERROR: Directory "%csv_dir%" does not exist
    pause
    exit /b 1
)

echo.
echo Directory found: %csv_dir%
echo.

REM Ask for email column name
set /p "email_col=Enter email column name (press Enter for 'Email'): "
if "%email_col%"=="" set email_col=Email

echo.
echo Using email column: %email_col%
echo.

REM Ask for output directory
set /p "output_dir=Enter output directory (press Enter for auto-create): "

echo.
echo Starting university email filtering...
echo ========================================

REM Run the Python script
if "%output_dir%"=="" (
    python university_email_filter.py "%csv_dir%" --email-column "%email_col%"
) else (
    python university_email_filter.py "%csv_dir%" --output "%output_dir%" --email-column "%email_col%"
)

echo.
echo ========================================
echo University email filtering completed!
echo ========================================
echo.
pause
