@echo off
echo ===============================================
echo    POWERPACK UNSUBSCRIBE DATA UPLOAD
echo ===============================================
echo Started at: %date% %time%
echo.

REM Set the scripts directory - using %~dp0 to get the directory where this batch file is located
set "SCRIPTS_DIR=%~dp0"

REM Create logs directory if it doesn't exist
set "LOGS_DIR=%SCRIPTS_DIR%logs"
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

REM Create a log file in the logs directory
set "LOG_FILE=%LOGS_DIR%\pp_upload_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo Script execution started at %date% %time% > "%LOG_FILE%"

echo Running upl_unsubs_pp.py...
echo Running upl_unsubs_pp.py... >> "%LOG_FILE%"

REM Run the Python script
python "%SCRIPTS_DIR%upl_unsubs_pp.py"

REM Check if the script executed successfully
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: PowerPack upload completed successfully
    echo SUCCESS: PowerPack upload completed successfully >> "%LOG_FILE%"
)

echo.
echo ===============================================
echo Process completed at %date% %time%
echo ===============================================
echo Process completed at %date% %time% >> "%LOG_FILE%"

echo Log file saved to: %LOG_FILE%
echo.
echo Press any key to exit...
pause > nul
