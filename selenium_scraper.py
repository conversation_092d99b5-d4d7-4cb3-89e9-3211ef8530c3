from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import time
import json
import csv

class MagnusGroupSeleniumScraper:
    def __init__(self, headless=False):
        self.chrome_options = Options()
        if headless:
            self.chrome_options.add_argument("--headless")
        self.chrome_options.add_argument("--no-sandbox")
        self.chrome_options.add_argument("--disable-dev-shm-usage")
        self.chrome_options.add_argument("--disable-gpu")
        self.chrome_options.add_argument("--window-size=1920,1080")
        
        self.driver = None
        self.login_url = "https://admin.magnusgroup.biz/admin-login.php"
        self.abstracts_url = "https://admin.magnusgroup.biz/view-all-abstracts.php"
        
    def start_driver(self):
        """Initialize the Chrome driver"""
        try:
            self.driver = webdriver.Chrome(options=self.chrome_options)
            print("Chrome driver started successfully")
            return True
        except Exception as e:
            print(f"Failed to start Chrome driver: {e}")
            print("Make sure ChromeDriver is installed and in PATH")
            return False
    
    def login(self, username, password):
        """Login to the admin panel"""
        if not self.driver:
            print("Driver not initialized")
            return False
            
        print("Navigating to login page...")
        self.driver.get(self.login_url)
        
        try:
            # Wait for the page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "name"))
            )
            
            # Find and fill the login form
            username_field = self.driver.find_element(By.NAME, "name")
            password_field = self.driver.find_element(By.NAME, "password")
            
            username_field.clear()
            username_field.send_keys(username)
            
            password_field.clear()
            password_field.send_keys(password)
            
            # Find and click the submit button
            submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
            submit_button.click()
            
            # Wait for redirect or page change
            time.sleep(3)
            
            current_url = self.driver.current_url
            print(f"Current URL after login: {current_url}")
            
            # Check if login was successful
            if "admin-login.php" not in current_url or "dashboard" in current_url.lower():
                print("Login successful!")
                return True
            else:
                print("Login may have failed - checking page content...")
                page_source = self.driver.page_source
                if "logout" in page_source.lower() or "dashboard" in page_source.lower():
                    print("Login successful (detected from page content)!")
                    return True
                else:
                    print("Login failed")
                    return False
                    
        except Exception as e:
            print(f"Error during login: {e}")
            return False
    
    def explore_abstracts_page(self):
        """Navigate to and explore the abstracts page"""
        if not self.driver:
            print("Driver not initialized")
            return None
            
        print("Navigating to abstracts page...")
        self.driver.get(self.abstracts_url)
        
        # Wait for page to load
        time.sleep(3)
        
        current_url = self.driver.current_url
        print(f"Current URL: {current_url}")
        
        # Check if we were redirected back to login
        if "admin-login.php" in current_url:
            print("Redirected to login page - session may have expired")
            return None
        
        # Get page source and analyze
        page_source = self.driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # Save the HTML for analysis
        with open('abstracts_page_selenium.html', 'w', encoding='utf-8') as f:
            f.write(soup.prettify())
        
        print("Page content saved to 'abstracts_page_selenium.html'")
        
        # Analyze the page structure
        print("\n=== PAGE ANALYSIS ===")
        
        # Look for tables
        tables = soup.find_all('table')
        print(f"Found {len(tables)} table(s)")
        
        if tables:
            for i, table in enumerate(tables):
                rows = table.find_all('tr')
                print(f"  Table {i+1}: {len(rows)} rows")
                if rows:
                    headers = rows[0].find_all(['th', 'td'])
                    print(f"    Headers: {[h.get_text(strip=True) for h in headers]}")
        
        # Look for data containers
        divs_with_data = soup.find_all('div', class_=lambda x: x and ('data' in x or 'content' in x or 'item' in x))
        print(f"Found {len(divs_with_data)} potential data container(s)")
        
        # Look for pagination
        pagination = soup.find_all(['a', 'button'], string=lambda text: text and ('next' in text.lower() or 'prev' in text.lower() or 'page' in text.lower()))
        print(f"Found {len(pagination)} pagination element(s)")
        
        # Look for any lists
        lists = soup.find_all(['ul', 'ol'])
        print(f"Found {len(lists)} list(s)")
        
        return soup
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            print("Browser closed")

# Test the scraper
if __name__ == "__main__":
    scraper = MagnusGroupSeleniumScraper(headless=False)  # Set to True for headless mode
    
    if scraper.start_driver():
        # Login credentials
        username = "<EMAIL>"
        password = "Magnus@38"
        
        # Attempt login
        if scraper.login(username, password):
            # Explore the abstracts page
            soup = scraper.explore_abstracts_page()
            
            if soup:
                print("\nPage exploration complete. Check 'abstracts_page_selenium.html' for full page content.")
        
        # Keep browser open for manual inspection (comment out to close automatically)
        input("Press Enter to close the browser...")
        scraper.close()
    else:
        print("Failed to start browser driver")
