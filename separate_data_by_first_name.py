#!/usr/bin/env python3
"""
Scrip<PERSON> to separate CSV data based on first_name column.
Creates separate CSV files for each unique first_name value.
"""

import pandas as pd
import os
from pathlib import Path
import re

# Note: openpyxl is required for Excel file support
# Install with: pip install openpyxl

def clean_filename(name):
    """Clean the first_name to create a valid filename."""
    # Remove or replace invalid characters for Windows filenames
    cleaned = re.sub(r'[<>:"/\\|?*]', '_', str(name))
    # Remove extra spaces and replace with underscores
    cleaned = re.sub(r'\s+', '_', cleaned.strip())
    return cleaned

def separate_data_by_first_name(input_file_path, output_directory):
    """
    Separate CSV data based on first_name column.
    
    Args:
        input_file_path (str): Path to the input CSV file
        output_directory (str): Directory where output files will be saved
    """
    
    print(f"Reading data from: {input_file_path}")
    
    try:
        # Read the CSV file
        df = pd.read_csv(input_file_path)
        print(f"Total records loaded: {len(df)}")

        # Filter out blacklisted records
        original_count = len(df)
        df = df[df['status'] != 'blacklisted']
        filtered_count = len(df)
        blacklisted_count = original_count - filtered_count
        print(f"Excluded {blacklisted_count} blacklisted records")
        print(f"Processing {filtered_count} non-blacklisted records")

        # Check if first_name column exists
        if 'first_name' not in df.columns:
            print("Error: 'first_name' column not found in the CSV file.")
            print(f"Available columns: {list(df.columns)}")
            return
        
        # Get unique first_name values
        unique_first_names = df['first_name'].unique()
        print(f"Found {len(unique_first_names)} unique first_name values")
        
        # Create output directory if it doesn't exist
        Path(output_directory).mkdir(parents=True, exist_ok=True)
        
        # Process each unique first_name
        for first_name in unique_first_names:
            if pd.isna(first_name):
                # Handle NaN values
                cleaned_name = "Unknown"
                print(f"Processing records with empty first_name as 'Unknown'")
            else:
                cleaned_name = clean_filename(first_name)
                print(f"Processing: {first_name} -> {cleaned_name}")
            
            # Filter data for this first_name
            filtered_df = df[df['first_name'] == first_name] if not pd.isna(first_name) else df[df['first_name'].isna()]

            # Select only email column and rename it to 'Email'
            email_only_df = filtered_df[['email']].copy()
            email_only_df.rename(columns={'email': 'Email'}, inplace=True)

            # Create output filenames
            csv_filename = f"{cleaned_name}_Unsubscribes.csv"
            xlsx_filename = f"{cleaned_name}_Unsubscribes.xlsx"
            csv_path = os.path.join(output_directory, csv_filename)
            xlsx_path = os.path.join(output_directory, xlsx_filename)

            # Save to CSV
            email_only_df.to_csv(csv_path, index=False)

            # Save to XLSX
            email_only_df.to_excel(xlsx_path, index=False)

            print(f"  -> Saved {len(email_only_df)} records to: {csv_filename} and {xlsx_filename}")
        
        print(f"\nSeparation complete! Files saved in: {output_directory}")
        
    except FileNotFoundError:
        print(f"Error: File not found - {input_file_path}")
    except Exception as e:
        print(f"Error processing file: {str(e)}")

def main():
    # Define paths
    input_file = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs\mailwizz_unsubscribers.csv"
    output_dir = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs"
    
    print("=== CSV Data Separator by First Name ===")
    print(f"Input file: {input_file}")
    print(f"Output directory: {output_dir}")
    print("-" * 50)
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file does not exist - {input_file}")
        return
    
    # Run the separation
    separate_data_by_first_name(input_file, output_dir)

if __name__ == "__main__":
    main()
