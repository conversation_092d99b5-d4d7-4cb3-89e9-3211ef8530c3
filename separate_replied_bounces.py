import os
import pandas as pd
import re
import numpy as np
import datetime
import subprocess
import time
from tqdm import tqdm

from rich.console import Console
from rich.table import Table

console = Console()

def get_todays_file_info():
    """
    Get today's date and generate the corresponding file information
    """
    today_date = datetime.datetime.now().strftime("%d-%m-%Y")
    file_name = f"Replied Bounces {today_date}.xlsx"
    base_dir = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\replied bounces"
    file_path = os.path.join(base_dir, file_name)

    return {
        'date': today_date,
        'filename': file_name,
        'filepath': file_path,
        'base_dir': base_dir
    }

def process_replied_bounces_workflow():
    """
    Main workflow for processing replied bounces with date-based filename
    Similar to irep_2.0.py workflow
    """
    console.print("[bold green]Starting Replied Bounces Processing Workflow[/bold green]")

    # Get today's file information
    file_info = get_todays_file_info()

    console.print(f"[cyan]Processing date:[/cyan] {file_info['date']}")
    console.print(f"[cyan]Target file:[/cyan] {file_info['filename']}")
    console.print(f"[cyan]Full path:[/cyan] {file_info['filepath']}")

    # Check if today's file exists
    if not os.path.exists(file_info['filepath']):
        console.print(f"[yellow]⚠ Today's file not found:[/yellow] {file_info['filename']}")
        console.print("[yellow]Please ensure the file exists in the replied bounces directory[/yellow]")
        console.print("[red]Exiting - file not found[/red]")
        return False

    # Process the file
    return separate_excel_by_column(file_info['filepath'], 'Conference Short Name')

def separate_excel_by_column_interactive():
    """
    Interactive version for custom file selection
    """
    console.print("[bold blue]Excel File Separator by Conference Short Name[/bold blue]")
    console.print("=" * 60)

    # Get today's file info for default
    file_info = get_todays_file_info()

    console.print(f"[cyan]Today's date:[/cyan] {file_info['date']}")
    console.print(f"[cyan]Auto-generated filename:[/cyan] {file_info['filename']}")
    console.print(f"[cyan]Default file path:[/cyan] {file_info['filepath']}")

    # Ask user for file path or use default
    user_input = input("Press Enter to use default path, or enter a different path: ").strip()

    file_path = user_input if user_input else file_info['filepath']

    # Run the separation
    return separate_excel_by_column(file_path, 'Conference Short Name')

def trim_all_columns(df):
    """
    Trim whitespace from ends of each value across all series in dataframe
    """
    trim_strings = lambda x: x.strip('.,;() ') if isinstance(x, str) else x
    return df.applymap(trim_strings)

def find_email(text):
    """
    Extract valid email addresses using regex
    """
    email = re.findall(r'^[a-zA-Z0-9._-]+(?:\.[a-zA-Z0-9._-]+)*@[a-zA-Z0-9._-]+(?:\.[a-zA-Z0-9._-]+)*$', str(text))
    return ",".join(email)

def clean_email_data(df):
    """
    Clean and process email data similar to irep_2.0.py
    """
    console.print("[yellow]Cleaning email data...[/yellow]")

    # Remove mailto: prefixes and clean email data
    df = df.apply(lambda x: x.str.replace('mailto:', '') if x.dtype == 'object' else x)
    df = df.apply(lambda x: x.str.replace('mailto', '') if x.dtype == 'object' else x)
    df = df.apply(lambda x: x.str.replace(':', ',') if x.dtype == 'object' else x)
    df = df.apply(lambda x: x.str.replace(';', ',') if x.dtype == 'object' else x)

    # Remove spaces from email column
    if 'Email' in df.columns:
        df['Email'] = df['Email'].astype(str).str.replace(' ', '')

        # Split emails by comma and expand
        email_split = df['Email'].str.split(',', expand=True)

        # Only use the first 3 columns to avoid issues
        max_cols = min(3, email_split.shape[1])
        email_cols = [f'Email{i+1}' for i in range(max_cols)]

        for i in range(max_cols):
            df[email_cols[i]] = email_split.iloc[:, i]

        df.drop('Email', axis=1, inplace=True)

        # Create separate dataframes for each email column
        email_dfs = []
        for col in email_cols:
            if col in df.columns:
                temp_df = df[df[col].notna()][['Conference Short Name', 'Name', col]].copy()
                temp_df.rename(columns={col: 'Email'}, inplace=True)
                email_dfs.append(temp_df)

        # Combine all email dataframes
        if email_dfs:
            df = pd.concat(email_dfs, ignore_index=True)

    # Trim all columns
    df = trim_all_columns(df)

    # Apply email validation
    if 'Email' in df.columns:
        df['Email'] = df["Email"].apply(lambda x: find_email(x))
        df[['Email']] = df[['Email']].applymap(lambda x: x.lower() if isinstance(x, str) else x)

    # Remove duplicates and empty emails
    df = df.drop_duplicates(subset="Email")
    df.replace(r'^\s*$', np.nan, regex=True, inplace=True)
    df = df.dropna(subset=['Email'])

    console.print(f"[green]✓ Email data cleaned. {len(df)} valid records remaining[/green]")
    return df

def clean_filename(name):
    """
    Clean a string to be used as a filename by removing invalid characters
    and replacing spaces around hyphens.
    """
    if pd.isna(name) or name == '':
        return 'Unknown'
    
    # Convert to string and strip whitespace
    name = str(name).strip()
    
    # Replace spaces around hyphens (e.g., 'M-Nano - 2025' becomes 'M-Nano-2025')
    name = re.sub(r'\s*-\s*', '-', name)
    
    # Remove or replace invalid filename characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        name = name.replace(char, '_')
    
    # Remove extra spaces and replace with underscores
    name = re.sub(r'\s+', '_', name)
    
    return name

def separate_excel_by_column(file_path, column_name='Conference Short Name'):
    """
    Separate Excel file data based on a specified column and save to separated folder.
    
    Args:
        file_path: Path to the Excel file
        column_name: Name of the column to separate by
    """
    console.print(f"[bold blue]Processing Excel file:[/bold blue] {file_path}")
    
    # Check if file exists
    if not os.path.exists(file_path):
        console.print(f"[bold red]Error: File not found:[/bold red] {file_path}")
        return
    
    try:
        # Read the Excel file
        console.print("[yellow]Reading Excel file...[/yellow]")
        df = pd.read_excel(file_path)
        
        console.print(f"[green]✓ Successfully loaded {len(df)} rows[/green]")
        
        # Display column information
        console.print(f"[cyan]Available columns:[/cyan] {', '.join(df.columns.tolist())}")
        
        # Check if the specified column exists
        if column_name not in df.columns:
            console.print(f"[bold red]Error: Column '{column_name}' not found in the file.[/bold red]")
            console.print(f"[yellow]Available columns are:[/yellow] {', '.join(df.columns.tolist())}")
            return
        
        # Get the directory of the input file
        input_dir = os.path.dirname(file_path)
        
        # Create separated directory
        separated_dir = os.path.join(input_dir, "separated")
        os.makedirs(separated_dir, exist_ok=True)
        console.print(f"[green]✓ Created separated directory:[/green] {separated_dir}")
        
        # Combine Email and Alternate Email columns (similar to irep_2.0.py)
        console.print("[yellow]Combining Email and Alternate Email columns...[/yellow]")

        # Read Email column data
        df1 = df[['Conference Short Name', 'Name', 'Email']].copy()
        df1 = df1[df1['Email'].notna() & (df1['Email'] != '')]

        # Read Alternate Email column data
        df2 = df[['Conference Short Name', 'Name', 'Alternate Email']].copy()
        df2 = df2[df2['Alternate Email'].notna() & (df2['Alternate Email'] != '')]
        df2.rename(columns={'Alternate Email': 'Email'}, inplace=True)

        # Combine both dataframes
        df = pd.concat([df1, df2], ignore_index=True)
        console.print(f"[green]✓ Combined {len(df1)} Email records and {len(df2)} Alternate Email records[/green]")
        console.print(f"[green]✓ Total combined records: {len(df)}[/green]")

        # Apply email cleaning and processing
        df = clean_email_data(df)

        # Get unique values from the specified column
        unique_values = df[column_name].dropna().unique()
        console.print(f"[cyan]Found {len(unique_values)} unique values in '{column_name}' column[/cyan]")
        
        # Create a summary table
        summary_table = Table(title="Separation Summary")
        summary_table.add_column("Conference Short Name", style="cyan")
        summary_table.add_column("Filename", style="green")
        summary_table.add_column("Rows", style="yellow", justify="right")
        
        # Process each unique value with progress bar (using tqdm like irep_2.0.py)
        console.print(f"[cyan]Processing {len(unique_values)} conference(s)...[/cyan]")

        for value in tqdm(unique_values, desc="Processing conferences", colour='green'):
                # Filter data for this value
                filtered_df = df[df[column_name] == value].copy()

                # Keep only Name and Email columns (as per irep_2.0.py)
                if 'Name' in filtered_df.columns and 'Email' in filtered_df.columns:
                    filtered_df = filtered_df[['Name', 'Email']].copy()
                elif 'Email' in filtered_df.columns:
                    filtered_df = filtered_df[['Email']].copy()

                # Clean the value for filename but preserve the original format
                # Keep the original conference name format (e.g., "Neuro Care-2026")
                clean_value = str(value).strip() if not pd.isna(value) else 'Unknown'

                # Only remove truly invalid filename characters, keep spaces and hyphens
                invalid_chars = '<>:"/\\|?*'
                for char in invalid_chars:
                    clean_value = clean_value.replace(char, '_')

                # Create output filename in the format: "Conference Name_replied_bouncers.csv"
                output_filename = f"{clean_value}_replied_bouncers.csv"
                output_path = os.path.join(separated_dir, output_filename)

                # Save the filtered data
                filtered_df.to_csv(output_path, index=False, encoding='utf-8-sig')
                
                # Add to summary table
                summary_table.add_row(str(value), output_filename, str(len(filtered_df)))
        
        # Display summary
        console.print("\n")
        console.print(summary_table)
        
        console.print(f"\n[bold green]✓ Separation complete![/bold green]")
        console.print(f"[green]Files saved to:[/green] {separated_dir}")
        
        # Handle rows with missing values in the column
        missing_data = df[df[column_name].isna()]
        if not missing_data.empty:
            console.print(f"\n[yellow]⚠ Found {len(missing_data)} rows with missing '{column_name}' values[/yellow]")
            
            # Save missing data to a separate file
            missing_filename = "Missing_Conference_replied_bouncers.csv"
            missing_path = os.path.join(separated_dir, missing_filename)
            missing_data.to_csv(missing_path, index=False, encoding='utf-8-sig')
            console.print(f"[yellow]Saved rows with missing values to:[/yellow] {missing_filename}")
        
    except Exception as e:
        console.print(f"[bold red]Error processing file:[/bold red] {str(e)}")

def main():
    """Main function to run the separation process with automatic fallback logic."""
    console.print("[bold green]Replied Bounces Processing System[/bold green]")
    console.print("=" * 60)

    console.print("[cyan]Attempting to auto-process today's replied bounces file...[/cyan]")

    # Try option 1 first (auto-process today's file)
    result = process_replied_bounces_workflow()

    # If file not found (result is False), automatically exit
    if result is False:
        console.print("[yellow]Exiting - today's file not available for processing[/yellow]")
        return

    # If successful, show completion message
    console.print("[bold green]✓ Processing completed successfully![/bold green]")

if __name__ == "__main__":
    main()
