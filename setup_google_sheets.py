"""
Setup script for Google Sheets integration
This script will install required packages and guide you through the setup process
"""

import subprocess
import sys
import os
import json

def install_packages():
    """Install required packages"""
    packages = [
        'gspread',
        'google-auth',
        'google-auth-oauthlib', 
        'google-auth-httplib2',
        'pandas',
        'openpyxl'
    ]
    
    print("📦 Installing required packages...")
    print("=" * 40)
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    print("\n✅ All packages installed successfully!")
    return True

def create_sample_credentials():
    """Create sample credentials files"""
    
    # Sample service account credentials structure
    service_account_sample = ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    
    # Sample OAuth2 credentials structure
    oauth2_sample = {
        "installed": *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
    
    # Create sample files
    with open('service_account_credentials_SAMPLE.json', 'w') as f:
        json.dump(service_account_sample, f, indent=2)
    
    with open('oauth2_credentials_SAMPLE.json', 'w') as f:
        json.dump(oauth2_sample, f, indent=2)
    
    print("📄 Sample credential files created:")
    print("   - service_account_credentials_SAMPLE.json")
    print("   - oauth2_credentials_SAMPLE.json")

def print_setup_instructions():
    """Print detailed setup instructions"""
    
    instructions = """
🔧 GOOGLE SHEETS API SETUP INSTRUCTIONS
======================================

STEP 1: Enable Google Sheets API
--------------------------------
1. Go to Google Cloud Console: https://console.cloud.google.com/
2. Create a new project or select an existing one
3. Go to "APIs & Services" > "Library"
4. Search for "Google Sheets API" and enable it
5. Search for "Google Drive API" and enable it

STEP 2A: Service Account Setup (Recommended for automation)
----------------------------------------------------------
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "Service Account"
3. Fill in the service account details
4. Click "Create and Continue"
5. Skip role assignment (click "Continue")
6. Click "Done"
7. Click on the created service account
8. Go to "Keys" tab
9. Click "Add Key" > "Create new key" > "JSON"
10. Download the JSON file
11. Rename it to 'service_account_credentials.json'
12. Place it in your project directory

STEP 2B: OAuth2 Setup (For user-based access)
---------------------------------------------
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Select "Desktop application"
4. Give it a name
5. Click "Create"
6. Download the JSON file
7. Rename it to 'oauth2_credentials.json'
8. Place it in your project directory

STEP 3: Share Your Google Sheet (For Service Account)
----------------------------------------------------
1. Open your Google Sheet
2. Click "Share" button
3. Add the service account email (found in the JSON file)
4. Give it "Editor" permissions
5. Click "Send"

STEP 4: Test the Setup
---------------------
Run: python google_sheets_examples.py

IMPORTANT NOTES:
- Keep your credential files secure and never commit them to version control
- Add *.json to your .gitignore file
- Service account method is better for automation
- OAuth2 method is better for user-interactive applications

TROUBLESHOOTING:
- If you get authentication errors, check your credentials file
- If you get permission errors, make sure the sheet is shared with the service account
- If you get API errors, make sure both Google Sheets API and Google Drive API are enabled
"""
    
    print(instructions)

def create_gitignore():
    """Create or update .gitignore file"""
    gitignore_entries = [
        "# Google Sheets API Credentials",
        "service_account_credentials.json",
        "oauth2_credentials.json",
        "token.json",
        "*.json",
        "",
        "# Python",
        "__pycache__/",
        "*.pyc",
        "*.pyo",
        "*.pyd",
        ".Python",
        "env/",
        "venv/",
        ".env",
        ".venv/",
        ""
    ]
    
    gitignore_path = '.gitignore'
    
    # Read existing .gitignore if it exists
    existing_entries = set()
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r') as f:
            existing_entries = set(line.strip() for line in f.readlines())
    
    # Add new entries
    new_entries = []
    for entry in gitignore_entries:
        if entry not in existing_entries and entry.strip():
            new_entries.append(entry)
    
    if new_entries:
        with open(gitignore_path, 'a') as f:
            f.write('\n'.join(new_entries) + '\n')
        print(f"✅ Updated .gitignore with {len(new_entries)} new entries")
    else:
        print("✅ .gitignore already contains necessary entries")

def main():
    """Main setup function"""
    print("🚀 GOOGLE SHEETS INTEGRATION SETUP")
    print("=" * 40)
    
    # Install packages
    if not install_packages():
        print("❌ Package installation failed. Please install manually.")
        return
    
    print("\n" + "=" * 40)
    
    # Create sample credentials
    create_sample_credentials()
    
    print("\n" + "=" * 40)
    
    # Create/update .gitignore
    create_gitignore()
    
    print("\n" + "=" * 40)
    
    # Print setup instructions
    print_setup_instructions()
    
    print("\n🎉 Setup complete! Follow the instructions above to configure your credentials.")

if __name__ == "__main__":
    main()
