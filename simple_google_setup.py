"""
Simple Google Sheets Setup - Easy authentication with your Google account
This creates OAuth2 credentials that work with your personal Google account
"""

import json
import os
import webbrowser
from google_auth_oauthlib.flow import InstalledAppFlow
import pickle

def create_oauth_credentials():
    """Create OAuth2 credentials file for Google Sheets access"""
    
    print("🔐 SIMPLE GOOGLE SHEETS AUTHENTICATION SETUP")
    print("=" * 50)
    print("This will set up Google Sheets access using your Google account.")
    print("Much simpler than service accounts!")
    
    # Check if credentials already exist
    if os.path.exists('credentials.json'):
        print("\n✅ Found existing credentials.json file")
        use_existing = input("Use existing credentials? (y/n): ").lower().strip()
        if use_existing != 'y':
            os.remove('credentials.json')
            print("Removed existing credentials.json")
    
    # Create credentials if they don't exist
    if not os.path.exists('credentials.json'):
        print("\n📋 CREATING OAUTH2 CREDENTIALS")
        print("=" * 40)
        
        # Create a basic OAuth2 credentials structure
        # This is a template - user needs to fill in their own values
        oauth_template = {
            "installed": *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        }
        
        print("I'll help you create the credentials.json file.")
        print("\nFirst, let's open Google Cloud Console...")
        
        # Open Google Cloud Console
        webbrowser.open("https://console.developers.google.com/")
        
        print("\n📝 FOLLOW THESE STEPS IN THE BROWSER:")
        print("=" * 40)
        print("1. Create a new project or select an existing one")
        print("2. Go to 'APIs & Services' > 'Library'")
        print("3. Search and enable 'Google Sheets API'")
        print("4. Search and enable 'Google Drive API'")
        print("5. Go to 'APIs & Services' > 'Credentials'")
        print("6. Click 'Create Credentials' > 'OAuth 2.0 Client IDs'")
        print("7. Select 'Desktop application'")
        print("8. Give it a name (e.g., 'Google Sheets Access')")
        print("9. Click 'Create'")
        print("10. Download the JSON file")
        print("11. Save it as 'credentials.json' in this directory")
        
        input("\nPress Enter when you've downloaded and saved credentials.json...")
        
        if not os.path.exists('credentials.json'):
            print("❌ credentials.json not found. Please download it and try again.")
            return False
    
    # Test the credentials
    print("\n🧪 TESTING CREDENTIALS")
    print("=" * 30)
    
    try:
        scopes = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]
        
        flow = InstalledAppFlow.from_client_secrets_file('credentials.json', scopes)
        creds = flow.run_local_server(port=0)
        
        # Save the credentials
        with open('token.pickle', 'wb') as token:
            pickle.dump(creds, token)
        
        print("✅ Authentication successful!")
        print("✅ Credentials saved for future use")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you downloaded the correct JSON file")
        print("2. Ensure it's named 'credentials.json'")
        print("3. Check that both APIs are enabled")
        return False

def test_google_sheets_access():
    """Test Google Sheets access"""
    print("\n🧪 TESTING GOOGLE SHEETS ACCESS")
    print("=" * 40)
    
    try:
        from google_sheets_manager import GoogleSheetsManager
        import pandas as pd
        from datetime import datetime
        
        # Initialize manager
        gsm = GoogleSheetsManager()
        
        # Create a test sheet
        test_name = f"Test_Sheet_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_sheet = gsm.create_new_sheet(test_name)
        
        # Add test data
        test_data = {
            'Name': ['Moses M', 'Test User'],
            'Email': ['<EMAIL>', '<EMAIL>'],
            'Status': ['Active', 'Test'],
            'Date': [datetime.now().strftime('%Y-%m-%d')] * 2
        }
        df = pd.DataFrame(test_data)
        
        # Write to sheet
        gsm.write_dataframe_to_sheet(test_sheet.sheet1, df)
        
        print("✅ Test successful!")
        print(f"📊 Test sheet created: {test_sheet.url}")
        print("🎉 You can now use Google Sheets with Python!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def show_usage_examples():
    """Show how to use the Google Sheets integration"""
    print("\n📚 HOW TO USE GOOGLE SHEETS IN YOUR SCRIPTS")
    print("=" * 50)
    
    example_code = '''
# Basic usage example:
from google_sheets_manager import GoogleSheetsManager
import pandas as pd

# Initialize (will use saved credentials)
gsm = GoogleSheetsManager()

# Open an existing sheet by URL
sheet_url = "https://docs.google.com/spreadsheets/d/YOUR_SHEET_ID/edit"
worksheet = gsm.open_sheet(sheet_url)

# Read data into pandas DataFrame
df = gsm.read_sheet_to_dataframe(worksheet)

# Modify your data (like your existing email processing)
df['Email status'] = df['Email status'].str.lower()
df['Last_Updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# Write back to Google Sheets
gsm.write_dataframe_to_sheet(worksheet, df)

# Or create a new sheet
new_sheet = gsm.create_new_sheet("My_Email_List")
gsm.write_dataframe_to_sheet(new_sheet.sheet1, df)
'''
    
    print(example_code)
    
    print("\n🚀 NEXT STEPS:")
    print("1. Run: python quick_start_google_sheets.py")
    print("2. Try: python google_sheets_examples.py")
    print("3. Integrate with your existing email processing scripts")

def main():
    """Main setup function"""
    print("🔗 GOOGLE SHEETS - SIMPLE SETUP")
    print("=" * 40)
    
    # Create OAuth credentials
    if create_oauth_credentials():
        print("\n" + "=" * 40)
        
        # Test access
        if test_google_sheets_access():
            print("\n" + "=" * 40)
            show_usage_examples()
        else:
            print("\n❌ Setup completed but test failed.")
            print("Try running: python test_google_sheets_connection.py")
    else:
        print("\n❌ Setup failed. Please try again.")

if __name__ == "__main__":
    main()
