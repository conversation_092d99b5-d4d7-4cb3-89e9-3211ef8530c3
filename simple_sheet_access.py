"""
Simple Google Sheet Access
Minimal setup required - just run this script
"""

import subprocess
import sys
import os

def install_required_packages():
    """Install required packages if not already installed"""
    packages = ['gspread', 'google-auth', 'google-auth-oauthlib', 'pandas']
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])

def create_minimal_credentials():
    """Create minimal credentials setup"""
    import json
    
    if os.path.exists('credentials.json'):
        return True
    
    print("🔧 MINIMAL GOOGLE SHEETS SETUP")
    print("=" * 40)
    print("To access your Google Sheet, we need to set up authentication.")
    print("This is a one-time setup.")
    
    print("\n📋 QUICK STEPS:")
    print("1. Go to: https://console.developers.google.com/")
    print("2. Create a project")
    print("3. Enable Google Sheets API")
    print("4. Create OAuth 2.0 credentials (Desktop app)")
    print("5. Download JSON and save as 'credentials.json'")
    
    # Open the console
    import webbrowser
    webbrowser.open("https://console.developers.google.com/")
    
    input("\nPress Enter when you've saved credentials.json...")
    
    return os.path.exists('credentials.json')

def access_sheet_simple():
    """Simple sheet access"""
    # Install packages
    install_required_packages()
    
    # Import after installation
    import gspread
    from google_auth_oauthlib.flow import InstalledAppFlow
    import pandas as pd
    import pickle
    from datetime import datetime
    
    # Your sheet URL
    SHEET_URL = "https://docs.google.com/spreadsheets/d/1fnTv_S9MEOHUX_awF6Fxgm3vTXNqtes2DlLEGcpEOv4/edit"
    
    print("🔗 ACCESSING YOUR GOOGLE SHEET")
    print("=" * 40)
    
    # Check credentials
    if not create_minimal_credentials():
        print("❌ Credentials setup failed")
        return
    
    try:
        # Simple authentication
        SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
        
        creds = None
        if os.path.exists('token.pickle'):
            with open('token.pickle', 'rb') as token:
                creds = pickle.load(token)
        
        if not creds or not creds.valid:
            flow = InstalledAppFlow.from_client_secrets_file('credentials.json', SCOPES)
            creds = flow.run_local_server(port=0)
            with open('token.pickle', 'wb') as token:
                pickle.dump(creds, token)
        
        # Access sheet
        gc = gspread.authorize(creds)
        sheet = gc.open_by_url(SHEET_URL)
        worksheet = sheet.sheet1
        
        print(f"✅ Successfully opened: {sheet.title}")
        
        # Read data
        data = worksheet.get_all_records()
        if data:
            df = pd.DataFrame(data)
            print(f"📊 Found {len(df)} rows and {len(df.columns)} columns")
            print("\nData preview:")
            print(df.head())
            
            # Simple operations
            print("\n🛠️ Available operations:")
            print("1. View all data")
            print("2. Add timestamp column")
            print("3. Export to CSV")
            print("4. Add new row")
            
            choice = input("\nWhat would you like to do? (1-4): ")
            
            if choice == "1":
                print("\nFull data:")
                print(df.to_string())
                
            elif choice == "2":
                df['Timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # Write back
                worksheet.clear()
                data_to_write = [df.columns.tolist()] + df.values.tolist()
                worksheet.update('A1', data_to_write)
                print("✅ Timestamp added!")
                
            elif choice == "3":
                filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df.to_csv(filename, index=False)
                print(f"✅ Exported to {filename}")
                
            elif choice == "4":
                print("Enter new row data:")
                new_row = []
                for col in df.columns:
                    value = input(f"{col}: ")
                    new_row.append(value)
                worksheet.append_row(new_row)
                print("✅ New row added!")
        
        else:
            print("📋 Sheet is empty or has no data")
            # Show raw values
            all_values = worksheet.get_all_values()
            if all_values:
                print("Raw sheet content:")
                for i, row in enumerate(all_values[:10]):
                    print(f"Row {i+1}: {row}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you have the right permissions to access the sheet")
        print("2. Check that credentials.json is valid")
        print("3. Ensure Google Sheets API is enabled")

if __name__ == "__main__":
    access_sheet_simple()
