import os
import sys
import pandas as pd
import openpyxl  # Required for pandas to read Excel files with engine='openpyxl'
import re
import argparse
from pathlib import Path
import rich_progress
import logging
from datetime import datetime

def normalize_whitespace(text):
    """
    Normalize whitespace in text by:
    1. Replacing multiple spaces with a single space
    2. Removing leading and trailing whitespace
    3. Handling other whitespace characters (tabs, newlines, etc.)

    Args:
        text: Text to normalize

    Returns:
        Normalized text
    """
    if not isinstance(text, str):
        return text

    # Replace all whitespace characters (spaces, tabs, newlines) with a single space
    normalized = re.sub(r'\s+', ' ', text)
    # Remove leading and trailing whitespace
    normalized = normalized.strip()
    return normalized

def clean_name(name):
    """
    Clean name by:
    1. Removing text after the first comma
    2. Normalizing whitespace (removing leading/trailing spaces and extra spaces between words)
    3. Handling other whitespace characters (tabs, newlines, etc.)

    Args:
        name: Name string to clean

    Returns:
        Cleaned name string
    """
    if not isinstance(name, str):
        return name

    # Take only the part before the first comma
    name_part = name.split(',')[0]
    # Normalize whitespace
    return normalize_whitespace(name_part)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Custom logger function that uses rich_progress for status messages
def log_status(message, level="info"):
    """Log a message with both the logger and rich_progress status.

    Args:
        message (str): The message to log
        level (str): The log level ("info", "warning", "error", "debug", "success")
    """
    # Map log levels to logger methods
    log_methods = {
        "info": logger.info,
        "warning": logger.warning,
        "error": logger.error,
        "debug": logger.debug,
        "success": logger.info  # Logger doesn't have success, use info
    }

    # Log with standard logger
    if level in log_methods:
        log_methods[level](message)
    else:
        logger.info(message)

    # Also log with rich_progress for better visual feedback
    rich_progress.print_status(message, level)

def extract_segment_from_path(path):
    """Extract conference segment name from path.

    Args:
        path (str): The file path to extract segment from

    Returns:
        str or None: The extracted segment name or None if not found
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        segment = match.group(1)
        log_status(f"Found segment: {segment}", "success")
        return segment
    else:
        log_status(f"Desired segment not found in path: {path}", "warning")
        # Prompt for manual input
        segment = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
        if segment.strip():
            log_status(f"Using manually entered segment: {segment}", "info")
            return segment
        else:
            log_status("No segment name provided", "error")
            return None

def rename_csv_to_xlsx(path):
    """Rename all CSV files in the directory to XLSX files.

    Args:
        path (str): Directory containing CSV files
    """
    rename_bar = None
    try:
        path_obj = Path(path)
        csv_files = list(path_obj.glob('*.csv'))

        if not csv_files:
            log_status(f"No CSV files found in {path}", "info")
            return

        log_status(f"Found {len(csv_files)} CSV files to rename", "info")

        # Create a progress bar for renaming files
        rename_bar, update_rename = rich_progress.create_progress_bar(
            total=len(csv_files),
            description="Renaming CSV files",
            color_scheme="green"
        )

        for csv_file in csv_files:
            xlsx_file = csv_file.with_suffix('.xlsx')
            try:
                csv_file.rename(xlsx_file)
                update_rename(1, f"Renamed {csv_file.name} to {xlsx_file.name}")
            except Exception as e:
                log_status(f"Failed to rename {csv_file}: {str(e)}", "error")
                update_rename(1, f"Error with {csv_file.name}")

        # Stop the progress bar
        rename_bar.stop()
        rename_bar = None
        log_status(f"Renamed {len(csv_files)} CSV files to XLSX format", "success")

    except Exception as e:
        log_status(f"Error processing directory {path}: {str(e)}", "error")
        # Make sure to stop the progress bar if an error occurs
        if rename_bar:
            try:
                rename_bar.stop()
            except:
                pass

def process_files(path, sheetname, csn, output_folder, output_filename=None, save_individual=True, use_progress_bar=True):
    """Process Excel files and extract data from specified sheet.

    Args:
        path (str): Directory containing Excel files
        sheetname (str): Name of the sheet to extract data from
        csn (str): Conference segment name for output filename
        output_folder (str): Folder to save output CSV
        output_filename (str, optional): Custom filename for output
        save_individual (bool, optional): Whether to save individual sheet files
        use_progress_bar (bool, optional): Whether to use a progress bar for this operation

    Returns:
        tuple: (output_file_path, dataframe)
    """
    excel_bar = None
    try:
        path_obj = Path(path)
        output_folder_obj = Path(output_folder)

        # Create output directory if it doesn't exist
        output_folder_obj.mkdir(exist_ok=True, parents=True)
        log_status(f"Created output directory: {output_folder}", "info")

        # Find all Excel files
        files_xls = list(path_obj.glob('*.xlsx'))

        if not files_xls:
            log_status(f"No Excel files found in {path}", "warning")
            return None, pd.DataFrame()

        log_status(f"Found {len(files_xls)} Excel files to process for sheet '{sheetname}'", "info")

        # Initialize an empty list to store dataframes
        dfs = []

        # Create a progress bar for processing Excel files if requested
        if use_progress_bar:
            excel_bar, update_excel = rich_progress.create_progress_bar(
                total=len(files_xls),
                description=f"Processing sheet '{sheetname}'",
                color_scheme="purple"
            )

        # Process each Excel file
        for excel_file in files_xls:
            try:
                data = pd.read_excel(excel_file, sheet_name=sheetname, engine='openpyxl')
                if not data.empty:
                    dfs.append(data)
                    if use_progress_bar:
                        update_excel(1, f"Processed {excel_file.name}: {len(data)} rows")
                else:
                    if use_progress_bar:
                        update_excel(1, f"Sheet '{sheetname}' in {excel_file.name} is empty")
            except Exception as e:
                log_status(f"Error processing {excel_file}, sheet '{sheetname}': {str(e)}", "error")
                if use_progress_bar:
                    update_excel(1, f"Error with {excel_file.name}")

        # Stop the progress bar if it was created
        if use_progress_bar and excel_bar:
            excel_bar.stop()

        # Combine all dataframes
        if dfs:
            df = pd.concat(dfs, ignore_index=True)
            log_status(f"Combined {len(dfs)} files for sheet '{sheetname}', total rows: {len(df)}", "success")
        else:
            log_status(f"No data found in sheet '{sheetname}' across all files", "warning")
            df = pd.DataFrame()

        # Rename 'Author Name' column to 'Name' if it exists
        if not df.empty and 'Author Name' in df.columns:
            df.rename(columns={'Author Name': 'Name'}, inplace=True)
            log_status(f"Renamed 'Author Name' column to 'Name' in sheet '{sheetname}'", "info")

        # Determine output filename
        if output_filename:
            output_file_path = output_folder_obj / f"{output_filename}.csv"
        else:
            output_file_path = output_folder_obj / f"{csn}_{sheetname}.csv"

        # Filter to only keep Name and Email columns if we have data
        if not df.empty and 'Email' in df.columns:
            # Check if we need to create a Name column
            if 'Name' not in df.columns:
                df['Name'] = 'Colleague'
                log_status(f"Added Name column with default value 'Colleague' to sheet '{sheetname}'", "info")
            else:
                # Clean the Name column
                df['Name'] = df['Name'].apply(clean_name)
                log_status(f"Cleaned Name column for sheet '{sheetname}'", "info")

            # Filter to only Name and Email columns
            df = df[['Name', 'Email']]
            log_status(f"Filtered sheet '{sheetname}' to only include Name and Email columns", "info")

        # Save to CSV only if save_individual is True or it's not a valid sheet
        if not df.empty and (save_individual or sheetname == 'Invalid'):
            df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            log_status(f"Saved {len(df)} rows to {output_file_path}", "success")
        elif not df.empty:
            log_status(f"Skipped saving individual file for sheet '{sheetname}' (will be included in merged file)", "info")
        else:
            log_status(f"No data to save to {output_file_path}", "warning")

        return str(output_file_path) if (save_individual or sheetname == 'Invalid') else None, df

    except Exception as e:
        log_status(f"Error in process_files: {str(e)}", "error")
        # Make sure to stop the progress bar if an error occurs
        if use_progress_bar and excel_bar:
            try:
                excel_bar.stop()
            except:
                pass
        return None, pd.DataFrame()

def process_valid_sheets(path, csn, sheetnames, output_folder):
    """Process and merge valid sheets.

    Args:
        path (str): Directory containing Excel files
        csn (str): Conference segment name
        sheetnames (list): List of sheet names to process
        output_folder (str): Folder to save output

    Returns:
        pd.DataFrame: Merged dataframe from valid sheets
    """
    valid_dfs = []
    sheets_bar = None

    try:
        # Create a progress bar for processing valid sheets
        sheets_bar, update_sheets = rich_progress.create_progress_bar(
            total=len(sheetnames),
            description="Processing valid sheets",
            color_scheme="blue"
        )

        # Process each valid sheet but don't save individual files
        for sheetname in sheetnames:
            log_status(f"Processing valid sheet: {sheetname}", "info")
            # Process files without their own progress bar to avoid conflicts
            _, df = process_files(path, sheetname, csn, output_folder, save_individual=False, use_progress_bar=False)
            if not df.empty:
                valid_dfs.append(df)
                update_sheets(1, f"Processed {sheetname}: {len(df)} rows")
            else:
                update_sheets(1, f"No data in {sheetname}")

        # Stop the progress bar
        sheets_bar.stop()
        sheets_bar = None

        # Merge valid dataframes
        if valid_dfs:
            log_status("Merging valid data sheets...", "info")
            merged_df = pd.concat(valid_dfs, ignore_index=True)

            # Remove duplicate rows based on Email column if it exists
            if 'Email' in merged_df.columns:
                original_count = len(merged_df)
                merged_df.drop_duplicates(subset=['Email'], inplace=True)
                log_status(f"Removed {original_count - len(merged_df)} duplicate emails", "info")

                # Filter to only keep Name and Email columns
                if 'Name' in merged_df.columns:
                    # Clean the Name column
                    merged_df['Name'] = merged_df['Name'].apply(clean_name)
                    log_status("Cleaned Name column in merged data", "info")

                    merged_df = merged_df[['Name', 'Email']]
                    log_status("Filtered output to only include Name and Email columns", "info")
                else:
                    # If Name column doesn't exist but we need it, create it with default value
                    merged_df['Name'] = 'Colleague'
                    merged_df = merged_df[['Name', 'Email']]
                    log_status("Added Name column with default value 'Colleague' and filtered output", "info")

            log_status(f"Merged {len(valid_dfs)} valid sheets with {len(merged_df)} total rows", "success")
            return merged_df
        else:
            log_status("No valid data to merge", "warning")
            return pd.DataFrame()

    except Exception as e:
        log_status(f"Error in process_valid_sheets: {str(e)}", "error")
        # Make sure to stop the progress bar if an error occurs
        if sheets_bar:
            try:
                sheets_bar.stop()
            except:
                pass
        return pd.DataFrame()

def process_invalid_sheet(path, csn, sheetname, output_folder, output_filename):
    """Process invalid sheet.

    Args:
        path (str): Directory containing Excel files
        csn (str): Conference segment name
        sheetname (str): Sheet name to process
        output_folder (str): Folder to save output
        output_filename (str): Custom filename for output

    Returns:
        tuple: (output_file_path, dataframe)
    """
    log_status(f"Processing invalid sheet: {sheetname}", "info")
    # Use progress bar for invalid sheet since it's processed separately
    file_path, df = process_files(path, sheetname, csn, output_folder, output_filename, use_progress_bar=True)

    # Filter to only keep Name and Email columns if we have data
    if not df.empty and 'Email' in df.columns:
        # Check if we need to create a Name column
        if 'Name' not in df.columns:
            df['Name'] = 'Colleague'
            log_status("Added Name column with default value 'Colleague' to invalid data", "info")
        else:
            # Clean the Name column
            df['Name'] = df['Name'].apply(clean_name)
            log_status("Cleaned Name column in invalid data", "info")

        # Filter to only Name and Email columns
        df = df[['Name', 'Email']]
        log_status("Filtered invalid data to only include Name and Email columns", "info")

        # Save the filtered data
        if file_path:
            output_path = Path(file_path)
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            log_status(f"Saved filtered invalid data to {output_path}", "success")

    return file_path, df

def update_master_hardbounces(invalid_file_path, master_file_path):
    """Update master hardbounces file with new invalid emails.

    Args:
        invalid_file_path (str): Path to invalid emails file
        master_file_path (str): Path to master hardbounces file
    """
    update_bar = None
    try:
        if not invalid_file_path or not Path(invalid_file_path).exists():
            log_status(f"Invalid file not found: {invalid_file_path}", "warning")
            return

        master_path = Path(master_file_path)
        if not master_path.exists():
            log_status(f"Master hardbounces file not found: {master_file_path}", "warning")
            return

        log_status("Updating master hardbounces file...", "info")

        # Create a progress bar for updating master hardbounces
        update_bar, update_progress = rich_progress.create_progress_bar(
            total=3,  # Read invalid, read master, save updated
            description="Updating hardbounces",
            color_scheme="orange"
        )

        # Read files
        df_invalid = pd.read_csv(invalid_file_path)
        update_progress(1, f"Read {len(df_invalid)} invalid emails")

        df_hb_all = pd.read_csv(master_file_path)
        update_progress(1, f"Read {len(df_hb_all)} existing hardbounces")

        if 'Email' not in df_invalid.columns:
            log_status("Invalid file does not contain 'Email' column", "error")
            update_bar.stop()
            update_bar = None
            return

        # Ensure we only use the Email column from the invalid file
        if 'Email' in df_invalid.columns:
            invalid_emails = df_invalid[['Email']]
        else:
            log_status("Invalid file does not contain 'Email' column", "error")
            update_bar.stop()
            update_bar = None
            return

        # Ensure master file has Email column
        if 'Email' not in df_hb_all.columns:
            df_hb_all['Email'] = ''
            log_status("Added missing Email column to master hardbounces file", "warning")

        # Combine and remove duplicates
        df_concat = pd.concat([invalid_emails, df_hb_all[['Email']]], ignore_index=True)
        original_count = len(df_concat)
        df_concat.drop_duplicates(subset=['Email'], inplace=True)
        duplicates_removed = original_count - len(df_concat)

        # Save back to master file
        df_concat.to_csv(master_file_path, index=False, encoding='utf-8-sig')
        update_progress(1, "Saved updated hardbounces")

        # Stop the progress bar
        update_bar.stop()
        update_bar = None

        log_status(f"Updated master hardbounces file with {len(df_invalid)} new entries", "success")
        if duplicates_removed > 0:
            log_status(f"Removed {duplicates_removed} duplicate emails", "info")

    except Exception as e:
        log_status(f"Error updating master hardbounces: {str(e)}", "error")
        # Make sure to stop the progress bar if an error occurs
        if update_bar:
            try:
                update_bar.stop()
            except:
                pass

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Process Excel files and merge sheets.')
    parser.add_argument('--path', '-p', help='Path to directory containing Excel files')
    parser.add_argument('--valid-output', default='valid', help='Output folder for valid data')
    parser.add_argument('--invalid-output', default='invalid', help='Output folder for invalid data')
    parser.add_argument('--master-hb', default="H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv",
                        help='Path to master hardbounces file')
    return parser.parse_args()

def main():
    """Main function to process files."""
    try:
        # Print welcome header
        rich_progress.print_status("\nSIT Email Validation Processor", "header")
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        rich_progress.print_status(timestamp, "info")
        rich_progress.print_status("=" * 50, "info")

        # Parse command line arguments
        args = parse_arguments()

        # Get path from arguments or user input
        if args.path:
            path = args.path
        else:
            rich_progress.print_status("\n>> Input Path", "info")
            path = input("Enter loc: ")

        # Change to the specified directory
        try:
            os.chdir(path)
            log_status(f"Changed working directory to: {path}", "info")
        except Exception as e:
            log_status(f"Failed to change directory to {path}: {str(e)}", "error")
            return 1

        # Extract conference segment name
        rich_progress.print_status("\n>> Conference Segment Detection", "info")
        csn = extract_segment_from_path(path)
        if not csn:
            log_status("Could not extract conference segment name from path and no manual input provided", "error")
            return 1

        log_status(f"Processing files for conference segment: {csn}", "info")

        # Initialize main progress bar
        rich_progress.print_status("\n>> Processing Steps", "info")
        # We'll use individual progress bars for each step instead of an overall progress bar
        # This avoids the "Only one live display may be active at once" error

        # Step 1: Rename CSV files to XLSX
        log_status("Step 1: Renaming CSV files to XLSX", "info")
        rename_csv_to_xlsx(path)
        log_status("Step 1 completed: Renamed CSV files", "success")

        # Step 2: Process valid sheets and merge
        log_status("Step 2: Processing valid sheets", "info")
        valid_sheets = ['Valid+BasicCheck+DEA', 'CatchAll_AcceptAll']
        merged_valid_df = process_valid_sheets(path, csn, valid_sheets, args.valid_output)

        # Save merged valid data
        if not merged_valid_df.empty:
            valid_output = Path(args.valid_output)
            valid_output.mkdir(exist_ok=True, parents=True)
            valid_file_path = valid_output / f'{csn}_SIT_Valid.csv'
            merged_valid_df.to_csv(valid_file_path, index=False, encoding='utf-8-sig')
            log_status(f"Merged valid data saved to {valid_file_path} ({len(merged_valid_df)} rows)", "success")

        log_status("Step 2 completed: Processed valid sheets", "success")

        # Step 3: Process invalid sheet
        log_status("Step 3: Processing invalid sheet", "info")
        invalid_file_path, _ = process_invalid_sheet(
            path, csn, 'Invalid', args.invalid_output, f'{csn}_SIT_Invalid')
        log_status("Step 3 completed: Processed invalid sheet", "success")

        # Step 4: Update master hardbounces
        log_status("Step 4: Updating master hardbounces", "info")
        update_master_hardbounces(invalid_file_path, args.master_hb)
        log_status("Step 4 completed: Updated master hardbounces", "success")

        # Print completion message
        rich_progress.print_status("\nProcessing Completed Successfully!", "header")
        rich_progress.print_status(f"Conference segment: {csn}", "success")
        rich_progress.print_status(f"Total execution time: {datetime.now() - datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')}", "info")

    except Exception as e:
        log_status(f"An error occurred in the main process: {str(e)}", "error")
        return 1

    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        rich_progress.print_status("\nOperation cancelled by user (Ctrl+C).", "warning")
        sys.exit(1)
    except Exception as e:
        rich_progress.print_status(f"\nUnhandled exception: {str(e)}", "error")
        sys.exit(1)