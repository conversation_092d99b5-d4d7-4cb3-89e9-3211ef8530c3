#!/usr/bin/env python3
"""
Script to split Excel rows with Daily Limit > 10,000 into multiple rows of 10,000 each.
The final row contains the remainder (if any).
Rows with Daily Limit ≤ 10,000 are left unchanged.
"""

import pandas as pd
import os
from pathlib import Path

def split_daily_limits():
    """
    Split rows with Daily Limit > 10,000 into multiple rows.
    """
    
    # Input file path
    input_file = "DBMS_Daily_Campaigns_Commands.xlsx"
    
    # Check if file exists
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' not found!")
        return
    
    print(f"Reading Excel file: {input_file}")
    
    try:
        # Read the Excel file
        df = pd.read_excel(input_file)
        print(f"Successfully read file with {len(df)} rows")
    except Exception as e:
        print(f"Error reading file: {str(e)}")
        return
    
    # Create list to store new rows
    new_rows = []
    
    print("Processing rows...")
    
    # Process each row
    for index, row in df.iterrows():
        daily_limit = int(row['Daily Limit'])
        
        if daily_limit <= 10000:
            # Keep row unchanged
            new_rows.append(row.copy())
        else:
            # Split into multiple rows
            full_chunks = daily_limit // 10000
            remainder = daily_limit % 10000
            
            # Create full chunks of 10,000
            for i in range(full_chunks):
                new_row = row.copy()
                new_row['Daily Limit'] = 10000
                new_rows.append(new_row)
            
            # Add remainder row if exists
            if remainder > 0:
                new_row = row.copy()
                new_row['Daily Limit'] = remainder
                new_rows.append(new_row)
    
    # Create new DataFrame
    new_df = pd.DataFrame(new_rows)
    
    # Generate output filename
    output_file = "DBMS_Daily_Campaigns_Commands_split.xlsx"
    
    # Save the new Excel file
    print(f"Saving to: {output_file}")
    new_df.to_excel(output_file, index=False)
    
    # Display summary
    print(f"\nSUMMARY:")
    print(f"Original rows: {len(df)}")
    print(f"New rows: {len(new_df)}")
    print(f"Rows added: {len(new_df) - len(df)}")
    print(f"Output saved as: {output_file}")

if __name__ == "__main__":
    split_daily_limits()

