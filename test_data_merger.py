#!/usr/bin/env python3
"""
Test script for Professional Data Merger
Creates sample data files to test the merger functionality.
"""

import os
import pandas as pd
from pathlib import Path

def create_test_data():
    """Create sample test data files."""
    
    # Create test directory
    test_dir = Path("test_data")
    test_dir.mkdir(exist_ok=True)
    
    # Sample data with international characters
    csv_data1 = {
        'Name': ['<PERSON>', '<PERSON>, <PERSON>', '<PERSON>', 'Αλέξανδρος Παπαδόπουλος'],
        'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', 'alex.papa<PERSON><PERSON><EMAIL>'],
        'Article Title': ['Climate Change Research', 'Quantum Computing Advances', 'Machine Learning Applications', 'Renewable Energy Solutions']
    }
    
    csv_data2 = {
        'Author Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'Email': ['<EMAIL>', '<EMAIL>', 'anna.schmi<PERSON>@example.at', '<EMAIL>'],
        'Subject': ['Artificial Intelligence', 'Biotechnology Research', 'Environmental Science', 'Medical Innovations']
    }
    
    excel_data = {
        'Full Name': ['<PERSON>', 'Jurgen <PERSON>hafer', 'Erik <PERSON>', 'Jean Chateau'],
        'E-mail': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Title': ['Pharmaceutical Research', 'Engineering Solutions', 'Data Analytics', 'Chemical Engineering']
    }
    
    # Create CSV files with different encodings
    df1 = pd.DataFrame(csv_data1)
    df1.to_csv(test_dir / "sample1.csv", index=False, encoding='utf-8')
    
    df2 = pd.DataFrame(csv_data2)
    df2.to_csv(test_dir / "sample2.csv", index=False, encoding='cp1252')
    
    # Create Excel file
    df3 = pd.DataFrame(excel_data)
    df3.to_excel(test_dir / "sample3.xlsx", index=False)
    
    print(f"✅ Created test data in {test_dir}")
    print("Files created:")
    print("- sample1.csv (UTF-8 encoding)")
    print("- sample2.csv (CP1252 encoding)")
    print("- sample3.xlsx (Excel format)")
    print("\nYou can now test the merger with this directory!")
    
    return str(test_dir)

if __name__ == "__main__":
    test_directory = create_test_data()
    
    # Ask if user wants to run the merger on test data
    response = input("\nDo you want to run the data merger on the test data? (y/n): ").lower()
    if response.startswith('y'):
        from professional_data_merger import ProfessionalDataMerger
        merger = ProfessionalDataMerger()
        success = merger.merge_files(test_directory)
        
        if success:
            print("\n✅ Test merge completed successfully!")
            print(f"📁 Check the 'output' folder in {test_directory}")
        else:
            print("\n❌ Test merge failed. Check the log for details.")
    
    input("\nPress Enter to exit...")
