"""
Test Google Sheets connection
Run this after setting up your credentials
"""

import os
from google_sheets_manager import GoogleSheetsManager
import pandas as pd
from datetime import datetime

def test_connection():
    """Test the Google Sheets connection"""
    print("🔍 TESTING GOOGLE SHEETS CONNECTION")
    print("=" * 40)
    
    # Check if credentials file exists
    creds_file = 'service_account_credentials.json'
    if not os.path.exists(creds_file):
        print(f"❌ Credentials file not found: {creds_file}")
        print("   Please download your service account JSON file and rename it to:")
        print(f"   {creds_file}")
        return False
    
    try:
        # Initialize the manager
        print("1. Initializing Google Sheets Manager...")
        gsm = GoogleSheetsManager('service_account', creds_file)
        print("✅ Authentication successful!")
        
        # Create a test sheet
        print("2. Creating test sheet...")
        test_sheet_name = f"Test_Connection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_sheet = gsm.create_new_sheet(test_sheet_name)
        print(f"✅ Test sheet created: {test_sheet.title}")
        
        # Create test data
        print("3. Creating test data...")
        test_data = {
            'Name': ['Test User 1', 'Test User 2', 'Test User 3'],
            'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'Status': ['Active', 'Active', 'Inactive'],
            'Created': [datetime.now().strftime('%Y-%m-%d')] * 3
        }
        df = pd.DataFrame(test_data)
        
        # Write data to sheet
        print("4. Writing data to sheet...")
        worksheet = test_sheet.sheet1
        gsm.write_dataframe_to_sheet(worksheet, df)
        print("✅ Data written successfully!")
        
        # Read data back
        print("5. Reading data back...")
        read_df = gsm.read_sheet_to_dataframe(worksheet)
        print(f"✅ Read {len(read_df)} rows back from sheet")
        
        # Display results
        print("\n📊 TEST RESULTS:")
        print("=" * 40)
        print(f"✅ Connection: SUCCESS")
        print(f"✅ Sheet Creation: SUCCESS")
        print(f"✅ Data Writing: SUCCESS")
        print(f"✅ Data Reading: SUCCESS")
        print(f"\n📋 Test Sheet URL: {test_sheet.url}")
        print(f"📧 Service Account Email: {gsm.gc.auth.service_account_email}")
        
        return True
        
    except FileNotFoundError as e:
        print(f"❌ Credentials file error: {e}")
        return False
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure Google Sheets API and Google Drive API are enabled")
        print("2. Check that your credentials file is valid JSON")
        print("3. Verify the service account has the correct permissions")
        return False

def show_next_steps():
    """Show what to do after successful connection"""
    print("\n🎉 NEXT STEPS:")
    print("=" * 40)
    print("1. Share your existing Google Sheets with the service account email")
    print("2. Run the quick start demo: python quick_start_google_sheets.py")
    print("3. Try the examples: python google_sheets_examples.py")
    print("4. Integrate with your existing email processing scripts")
    
    print("\n📝 TO SHARE A SHEET:")
    print("1. Open your Google Sheet")
    print("2. Click 'Share' button")
    print("3. Add the service account email (shown above)")
    print("4. Give it 'Editor' permissions")
    print("5. Click 'Send'")

if __name__ == "__main__":
    success = test_connection()
    if success:
        show_next_steps()
    else:
        print("\n❌ Please set up your credentials first and try again.")
        print("   Follow the instructions from: python setup_google_sheets.py")
