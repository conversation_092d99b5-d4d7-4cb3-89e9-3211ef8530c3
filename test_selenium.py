"""
Simple test to check if Selenium and ChromeDriver work
"""

try:
    print("Testing Selenium setup...")
    
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.chrome.service import Service
    
    print("✓ Imports successful")
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Run in background
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    print("✓ Chrome options configured")
    
    # Try to get ChromeDriver
    print("Downloading/setting up ChromeDriver...")
    service = Service(ChromeDriverManager().install())
    print("✓ ChromeDriver setup complete")
    
    # Try to start browser
    print("Starting Chrome browser...")
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✓ Chrome browser started successfully")
    
    # Test basic navigation
    print("Testing navigation...")
    driver.get("https://www.google.com")
    title = driver.title
    print(f"✓ Navigation successful - Page title: {title}")
    
    # Close browser
    driver.quit()
    print("✓ Browser closed successfully")
    
    print("\n🎉 SUCCESS: Selenium and ChromeDriver are working correctly!")
    print("The automated scraper should work now.")
    
except Exception as e:
    print(f"\n❌ ERROR: {e}")
    print("\nTroubleshooting:")
    print("1. Make sure Chrome browser is installed")
    print("2. Try running: pip install --upgrade selenium webdriver-manager")
    print("3. If still failing, use the manual method instead")
