#!/usr/bin/env python3
"""
University Email Filter - Professional Data Engineering Solution
Filters university and academic emails from datasets with comprehensive pattern matching.
"""

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime
import argparse

# Import rich_progress for gradient progress bars
try:
    import rich_progress
    HAS_RICH = True
except ImportError:
    HAS_RICH = False
    print("Note: rich_progress not available. Using basic progress indicators.")

# Suppress pandas warnings - compatible with both old and new pandas versions
try:
    # Try the new location first (pandas >= 1.5.0)
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        # Try the old location (pandas < 1.5.0)
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        # If neither works, just ignore pandas warnings in general
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

# Country TLD mapping for email domain country extraction
tld_to_country = {
    # A
    '.ad': 'Andorra', '.ae': 'United Arab Emirates', '.af': 'Afghanistan', '.ag': 'Antigua and Barbuda',
    '.ai': 'Anguilla', '.al': 'Albania', '.am': 'Armenia', '.ao': 'Angola', '.aq': 'Antarctica',
    '.ar': 'Argentina', '.as': 'American Samoa', '.at': 'Austria', '.au': 'Australia', '.aw': 'Aruba',
    '.ax': 'Åland Islands', '.az': 'Azerbaijan',
    # B
    '.ba': 'Bosnia and Herzegovina', '.bb': 'Barbados', '.bd': 'Bangladesh', '.be': 'Belgium',
    '.bf': 'Burkina Faso', '.bg': 'Bulgaria', '.bh': 'Bahrain', '.bi': 'Burundi', '.bj': 'Benin',
    '.bl': 'Saint Barthélemy', '.bm': 'Bermuda', '.bn': 'Brunei', '.bo': 'Bolivia', '.bq': 'Caribbean Netherlands',
    '.br': 'Brazil', '.bs': 'Bahamas', '.bt': 'Bhutan', '.bv': 'Bouvet Island', '.bw': 'Botswana',
    '.by': 'Belarus', '.bz': 'Belize',
    # C
    '.ca': 'Canada', '.cc': 'Cocos Islands', '.cd': 'Democratic Republic of the Congo', '.cf': 'Central African Republic',
    '.cg': 'Republic of the Congo', '.ch': 'Switzerland', '.ci': 'Côte d\'Ivoire', '.ck': 'Cook Islands',
    '.cl': 'Chile', '.cm': 'Cameroon', '.cn': 'China', '.co': 'Colombia', '.cr': 'Costa Rica',
    '.cu': 'Cuba', '.cv': 'Cape Verde', '.cw': 'Curaçao', '.cx': 'Christmas Island', '.cy': 'Cyprus',
    '.cz': 'Czech Republic',
    # D
    '.de': 'Germany', '.dj': 'Djibouti', '.dk': 'Denmark', '.dm': 'Dominica', '.do': 'Dominican Republic',
    '.dz': 'Algeria',
    # E
    '.ec': 'Ecuador', '.ee': 'Estonia', '.eg': 'Egypt', '.eh': 'Western Sahara', '.er': 'Eritrea',
    '.es': 'Spain', '.et': 'Ethiopia', '.eu': 'European Union',
    # F
    '.fi': 'Finland', '.fj': 'Fiji', '.fk': 'Falkland Islands', '.fm': 'Federated States of Micronesia',
    '.fo': 'Faroe Islands', '.fr': 'France',
    # G
    '.ga': 'Gabon', '.gb': 'United Kingdom', '.gd': 'Grenada', '.ge': 'Georgia', '.gf': 'French Guiana',
    '.gg': 'Guernsey', '.gh': 'Ghana', '.gi': 'Gibraltar', '.gl': 'Greenland', '.gm': 'Gambia',
    '.gn': 'Guinea', '.gp': 'Guadeloupe', '.gq': 'Equatorial Guinea', '.gr': 'Greece', '.gs': 'South Georgia',
    '.gt': 'Guatemala', '.gu': 'Guam', '.gw': 'Guinea-Bissau', '.gy': 'Guyana',
    # H
    '.hk': 'Hong Kong', '.hm': 'Heard Island', '.hn': 'Honduras', '.hr': 'Croatia', '.ht': 'Haiti',
    '.hu': 'Hungary',
    # I
    '.id': 'Indonesia', '.ie': 'Ireland', '.il': 'Israel', '.im': 'Isle of Man', '.in': 'India',
    '.io': 'British Indian Ocean Territory', '.iq': 'Iraq', '.ir': 'Iran', '.is': 'Iceland', '.it': 'Italy',
    # J
    '.je': 'Jersey', '.jm': 'Jamaica', '.jo': 'Jordan', '.jp': 'Japan',
    # K
    '.ke': 'Kenya', '.kg': 'Kyrgyzstan', '.kh': 'Cambodia', '.ki': 'Kiribati', '.km': 'Comoros',
    '.kn': 'Saint Kitts and Nevis', '.kp': 'North Korea', '.kr': 'South Korea', '.kw': 'Kuwait',
    '.ky': 'Cayman Islands', '.kz': 'Kazakhstan',
    # L
    '.la': 'Laos', '.lb': 'Lebanon', '.lc': 'Saint Lucia', '.li': 'Liechtenstein', '.lk': 'Sri Lanka',
    '.lr': 'Liberia', '.ls': 'Lesotho', '.lt': 'Lithuania', '.lu': 'Luxembourg', '.lv': 'Latvia',
    '.ly': 'Libya',
    # M
    '.ma': 'Morocco', '.mc': 'Monaco', '.md': 'Moldova', '.me': 'Montenegro', '.mf': 'Saint Martin',
    '.mg': 'Madagascar', '.mh': 'Marshall Islands', '.mk': 'North Macedonia', '.ml': 'Mali', '.mm': 'Myanmar',
    '.mn': 'Mongolia', '.mo': 'Macao', '.mp': 'Northern Mariana Islands', '.mq': 'Martinique',
    '.mr': 'Mauritania', '.ms': 'Montserrat', '.mt': 'Malta', '.mu': 'Mauritius', '.mv': 'Maldives',
    '.mw': 'Malawi', '.mx': 'Mexico', '.my': 'Malaysia', '.mz': 'Mozambique',
    # N
    '.na': 'Namibia', '.nc': 'New Caledonia', '.ne': 'Niger', '.nf': 'Norfolk Island', '.ng': 'Nigeria',
    '.ni': 'Nicaragua', '.nl': 'Netherlands', '.no': 'Norway', '.np': 'Nepal', '.nr': 'Nauru',
    '.nu': 'Niue', '.nz': 'New Zealand',
    # O
    '.om': 'Oman',
    # P
    '.pa': 'Panama', '.pe': 'Peru', '.pf': 'French Polynesia', '.pg': 'Papua New Guinea', '.ph': 'Philippines',
    '.pk': 'Pakistan', '.pl': 'Poland', '.pm': 'Saint Pierre and Miquelon', '.pn': 'Pitcairn Islands',
    '.pr': 'Puerto Rico', '.ps': 'Palestine', '.pt': 'Portugal', '.pw': 'Palau', '.py': 'Paraguay',
    # Q
    '.qa': 'Qatar',
    # R
    '.re': 'Réunion', '.ro': 'Romania', '.rs': 'Serbia', '.ru': 'Russia', '.rw': 'Rwanda',
    # S
    '.sa': 'Saudi Arabia', '.sb': 'Solomon Islands', '.sc': 'Seychelles', '.sd': 'Sudan', '.se': 'Sweden',
    '.sg': 'Singapore', '.sh': 'Saint Helena', '.si': 'Slovenia', '.sj': 'Svalbard and Jan Mayen',
    '.sk': 'Slovakia', '.sl': 'Sierra Leone', '.sm': 'San Marino', '.sn': 'Senegal', '.so': 'Somalia',
    '.sr': 'Suriname', '.ss': 'South Sudan', '.st': 'São Tomé and Príncipe', '.sv': 'El Salvador',
    '.sx': 'Sint Maarten', '.sy': 'Syria', '.sz': 'Eswatini',
    # T
    '.tc': 'Turks and Caicos Islands', '.td': 'Chad', '.tf': 'French Southern Territories', '.tg': 'Togo',
    '.th': 'Thailand', '.tj': 'Tajikistan', '.tk': 'Tokelau', '.tl': 'East Timor', '.tm': 'Turkmenistan',
    '.tn': 'Tunisia', '.to': 'Tonga', '.tr': 'Turkey', '.tt': 'Trinidad and Tobago', '.tv': 'Tuvalu',
    '.tw': 'Taiwan', '.tz': 'Tanzania',
    # U
    '.ua': 'Ukraine', '.ug': 'Uganda', '.uk': 'United Kingdom', '.um': 'United States Minor Outlying Islands',
    '.us': 'United States', '.uy': 'Uruguay', '.uz': 'Uzbekistan',
    # V
    '.va': 'Vatican City', '.vc': 'Saint Vincent and the Grenadines', '.ve': 'Venezuela', '.vg': 'British Virgin Islands',
    '.vi': 'United States Virgin Islands', '.vn': 'Vietnam', '.vu': 'Vanuatu',
    # W
    '.wf': 'Wallis and Futuna', '.ws': 'Samoa',
    # Y
    '.ye': 'Yemen', '.yt': 'Mayotte',
    # Z
    '.za': 'South Africa', '.zm': 'Zambia', '.zw': 'Zimbabwe',
    # Special domains
    '.edu': 'United States', '.gov': 'United States', '.mil': 'United States'
}

# Special Chinese email domains mapping
chinese_domains = {
    '163.com': 'China', 'qq.com': 'China', '126.com': 'China', 'sina.com': 'China',
    'sohu.com': 'China', 'tom.com': 'China', 'aliyun.com': 'China', '21cn.com': 'China',
    'baidu.com': 'China', 'yeah.net': 'China', 'sogou.com': 'China', '163.net': 'China',
    'sina.net': 'China', 'chinaren.com': 'China'
}

def print_status(message, status_type="info"):
    """Print status message with or without rich formatting."""
    if HAS_RICH:
        rich_progress.print_status(message, status_type)
    else:
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

def print_header(title):
    """Print a header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    if HAS_RICH:
        rich_progress.print_status(f"\n{title}", "header")
        rich_progress.print_status(timestamp, "info")
        rich_progress.print_status("=" * 60, "info")
    else:
        print(f"\n{'='*60}")
        print(f"{title}")
        print(f"{timestamp}")
        print(f"{'='*60}")

def print_section(title):
    """Print a section header."""
    if HAS_RICH:
        rich_progress.print_status(f"\n>> {title}", "info")
        rich_progress.print_status("-" * (len(title) + 4), "info")
    else:
        print(f"\n>> {title}")
        print("-" * (len(title) + 4))

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special Chinese domains first
    for domain, country in chinese_domains.items():
        if email.endswith(domain):
            return country

    # Check for country TLDs
    for tld, country in tld_to_country.items():
        if email.endswith(tld):
            return country

    # If no match found, return 'Other'
    return 'Other'

class UniversityEmailFilter:
    """Professional university email filtering class."""
    
    def __init__(self):
        """Initialize the filter with comprehensive university patterns."""
        
        # Core academic domain patterns
        self.academic_domains = [
            ".edu",      # US education domains
            ".ac.uk", ".ac.in", ".ac.za", ".ac.jp", ".ac.kr", ".ac.cn",
            ".ac.th", ".ac.id", ".ac.my", ".ac.sg", ".ac.nz", ".ac.au",
            ".edu.au", ".edu.sg", ".edu.my", ".edu.ph", ".edu.tw", ".edu.hk",
            ".edu.cn", ".edu.in", ".edu.pk", ".edu.bd", ".edu.lk", ".edu.np",
            ".edu.br", ".edu.ar", ".edu.mx", ".edu.co", ".edu.pe", ".edu.cl",
            ".edu.eg", ".edu.sa", ".edu.ae", ".edu.qa", ".edu.kw", ".edu.jo",
            ".edu.tr", ".edu.pl", ".edu.ro", ".edu.gr", ".edu.it", ".edu.es"
        ]
        
        # University/academic keywords
        self.university_keywords = [
            "university", "univ", "college", "school", "institute", "institut",
            "research", "academy", "academic", "scholar", "student",
            "campus", "faculty", "dept", "department", "uni"
        ]

        # Specific university domain patterns
        self.university_patterns = [
            r"\.edu$",           # US education
            r"\.ac\.",           # Academic domains
            r"university\.",     # university.domain
            r"college\.",        # college.domain
            r"institute\.",      # institute.domain
            r"research\.",       # research.domain
            r"\.uni-",           # German/European uni- pattern
            r"uni\.",            # uni.domain pattern
            r"@uni[a-z]*\.",     # @uni*, @unimelb, @unibo, etc.
            r"\.uni[a-z]*\.",    # .uni*, .unimelb., .unibo., etc.
        ]
    
    def is_university_email(self, email):
        """
        Check if an email belongs to a university/academic institution.
        
        Args:
            email (str): Email address to check
            
        Returns:
            bool: True if email is from university/academic domain
        """
        if pd.isna(email):
            return False
            
        email = str(email).lower().strip()
        
        # Check specific academic domains
        for domain in self.academic_domains:
            if email.endswith(domain):
                return True
        
        # Check university keywords in domain
        for keyword in self.university_keywords:
            if keyword in email:
                return True
        
        # Check regex patterns
        for pattern in self.university_patterns:
            if re.search(pattern, email):
                return True
                
        return False
    
    def filter_university_emails(self, df, email_column='Email', add_country=True):
        """
        Filter dataframe to return only university emails.

        Args:
            df (pd.DataFrame): Input dataframe
            email_column (str): Name of email column
            add_country (bool): Whether to add country column based on email domain

        Returns:
            pd.DataFrame: Filtered dataframe with university emails only
        """
        if email_column not in df.columns:
            raise ValueError(f"Column '{email_column}' not found in dataframe")

        # Create filter mask
        university_mask = df[email_column].apply(self.is_university_email)

        # Filter the dataframe
        filtered_df = df[university_mask].copy()

        # Add country column if requested
        if add_country and len(filtered_df) > 0:
            filtered_df['Country'] = filtered_df[email_column].apply(extract_country_from_email)

        return filtered_df
    
    def get_filter_statistics(self, df, email_column='Email'):
        """
        Get statistics about university email filtering.

        Args:
            df (pd.DataFrame): Input dataframe
            email_column (str): Name of email column

        Returns:
            dict: Statistics about filtering results
        """
        total_emails = len(df)
        university_mask = df[email_column].apply(self.is_university_email)
        university_count = university_mask.sum()

        # Breakdown by domain type
        edu_count = df[email_column].str.contains('.edu', na=False).sum()
        ac_count = df[email_column].str.contains('.ac.', na=False).sum()

        # Get country statistics for university emails
        university_df = df[university_mask]
        country_stats = {}
        if len(university_df) > 0:
            countries = university_df[email_column].apply(extract_country_from_email)
            country_counts = countries.value_counts()
            country_stats = country_counts.to_dict()

        return {
            'total_emails': total_emails,
            'university_emails': university_count,
            'non_university_emails': total_emails - university_count,
            'university_percentage': (university_count / total_emails * 100) if total_emails > 0 else 0,
            'edu_domains': edu_count,
            'ac_domains': ac_count,
            'country_breakdown': country_stats
        }

def process_directory(directory_path, output_dir=None, email_column='Email'):
    """
    Process all CSV files in a directory to filter university emails.
    
    Args:
        directory_path (str): Path to directory containing CSV files
        output_dir (str): Output directory (default: creates 'university_filtered' subdirectory)
        email_column (str): Name of email column
    """
    print_header("University Email Filter - Data Engineering Solution")
    
    # Initialize filter
    filter_engine = UniversityEmailFilter()
    
    # Set working directory
    print_section("Setting Up Environment")
    os.chdir(directory_path)
    print_status(f"Working directory: {os.getcwd()}", "info")
    
    # Find CSV files
    print_section("Discovering CSV Files")
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print_status("No CSV files found in the directory.", "error")
        return
    
    print_status(f"Found {len(csv_files)} CSV files to process", "success")
    
    # Create output directory
    if output_dir is None:
        output_dir = os.path.join(directory_path, "university_filtered")
    os.makedirs(output_dir, exist_ok=True)
    print_status(f"Output directory: {output_dir}", "info")
    
    # Process files
    print_section("Processing Files")
    
    total_stats = {
        'files_processed': 0,
        'total_records': 0,
        'university_records': 0,
        'files_with_errors': 0
    }
    
    for i, csv_file in enumerate(csv_files, 1):
        print_status(f"Processing file {i}/{len(csv_files)}: {csv_file}", "info")
        
        try:
            # Read CSV file
            df = pd.read_csv(csv_file, low_memory=False)
            
            if email_column not in df.columns:
                print_status(f"Warning: '{email_column}' column not found in {csv_file}. Skipping.", "warning")
                continue
            
            # Get statistics
            stats = filter_engine.get_filter_statistics(df, email_column)
            
            # Filter university emails (with country column)
            university_df = filter_engine.filter_university_emails(df, email_column, add_country=True)

            # Save filtered data
            output_filename = f"university_{csv_file}"
            output_path = os.path.join(output_dir, output_filename)
            university_df.to_csv(output_path, encoding='utf-8-sig', index=False)

            # Update totals
            total_stats['files_processed'] += 1
            total_stats['total_records'] += stats['total_emails']
            total_stats['university_records'] += stats['university_emails']

            # Display statistics with top countries
            print_status(f"  ✓ {stats['university_emails']:,} university emails from {stats['total_emails']:,} total ({stats['university_percentage']:.1f}%)", "success")

            # Show top 3 countries if available
            if stats['country_breakdown']:
                top_countries = sorted(stats['country_breakdown'].items(), key=lambda x: x[1], reverse=True)[:3]
                country_info = ", ".join([f"{country}: {count}" for country, count in top_countries])
                print_status(f"    Top countries: {country_info}", "info")
            
        except Exception as e:
            print_status(f"Error processing {csv_file}: {str(e)}", "error")
            total_stats['files_with_errors'] += 1
    
    # Print final results
    print_header("Processing Complete!")
    print_section("Final Statistics")
    print_status(f"Files processed: {total_stats['files_processed']}", "success")
    print_status(f"Files with errors: {total_stats['files_with_errors']}", "info")
    print_status(f"Total records processed: {total_stats['total_records']:,}", "info")
    print_status(f"University emails found: {total_stats['university_records']:,}", "success")
    
    if total_stats['total_records'] > 0:
        percentage = (total_stats['university_records'] / total_stats['total_records']) * 100
        print_status(f"University email percentage: {percentage:.2f}%", "info")
    
    print_status(f"Output saved to: {output_dir}", "info")

def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description='Filter university emails from CSV datasets')
    parser.add_argument('directory', nargs='?', help='Directory containing CSV files to process')
    parser.add_argument('--output', '-o', help='Output directory (default: creates university_filtered subdirectory)')
    parser.add_argument('--email-column', '-e', default='Email', help='Name of email column (default: Email)')
    parser.add_argument('--interactive', '-i', action='store_true', help='Interactive mode with prompts')

    args = parser.parse_args()

    if args.interactive or not args.directory:
        # Interactive mode
        print_header("University Email Filter - Interactive Mode")
        directory = input("Enter directory path containing CSV files: ").strip()
        email_column = input("Enter email column name (default: Email): ").strip() or 'Email'
        output_dir = input("Enter output directory (optional): ").strip() or None
    else:
        directory = args.directory
        email_column = args.email_column
        output_dir = args.output

    if not os.path.exists(directory):
        print_status(f"Error: Directory '{directory}' does not exist.", "error")
        return

    process_directory(directory, output_dir, email_column)

if __name__ == "__main__":
    main()
