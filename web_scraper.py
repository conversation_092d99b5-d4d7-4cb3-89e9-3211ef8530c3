import requests
from bs4 import Beautiful<PERSON>oup
import json
import csv
import time
from urllib.parse import urljoin

class MagnusGroupScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.base_url = "https://admin.magnusgroup.biz"
        self.login_url = "https://admin.magnusgroup.biz/admin-login.php"
        self.abstracts_url = "https://admin.magnusgroup.biz/view-all-abstracts.php"
        
    def login(self, username, password):
        """Login to the admin panel"""
        print("Attempting to login...")

        # First, get the login page to check for any CSRF tokens or hidden fields
        login_page = self.session.get(self.login_url)
        soup = BeautifulSoup(login_page.content, 'html.parser')

        print("Login page HTML structure:")
        print(soup.prettify()[:1000])  # Print first 1000 chars for debugging

        # Find the login form
        login_form = soup.find('form')
        if not login_form:
            print("Could not find login form")
            return False

        # Find all input fields to understand the form structure
        all_inputs = soup.find_all('input')
        print(f"Found {len(all_inputs)} input fields:")
        for inp in all_inputs:
            print(f"  - Name: {inp.get('name')}, Type: {inp.get('type')}, Value: {inp.get('value')}")

        # Try different common field names
        possible_username_fields = ['username', 'email', 'user', 'login', 'userid']
        possible_password_fields = ['password', 'pass', 'pwd']

        username_field = None
        password_field = None

        for inp in all_inputs:
            field_name = inp.get('name', '').lower()
            field_type = inp.get('type', '').lower()

            if field_type == 'email' or any(field in field_name for field in possible_username_fields):
                username_field = inp.get('name')
            elif field_type == 'password' or any(field in field_name for field in possible_password_fields):
                password_field = inp.get('name')

        print(f"Detected username field: {username_field}")
        print(f"Detected password field: {password_field}")

        # Prepare login data with the correct field names
        login_data = {
            'name': username,  # Based on the form analysis, username field is 'name'
            'password': password
        }

        # Check for any hidden fields or CSRF tokens
        hidden_inputs = soup.find_all('input', type='hidden')
        for hidden_input in hidden_inputs:
            name = hidden_input.get('name')
            value = hidden_input.get('value', '')
            if name:
                login_data[name] = value

        print(f"Login data being sent: {list(login_data.keys())}")

        # Submit login form
        response = self.session.post(self.login_url, data=login_data, allow_redirects=True)

        print(f"Response status: {response.status_code}")
        print(f"Response URL: {response.url}")
        print(f"Response content preview: {response.text[:500]}")

        # Check if login was successful by looking for redirect or success indicators
        if ("dashboard" in response.url.lower() or
            "admin" in response.url.lower() or
            "view-all-abstracts" in response.url.lower() or
            "logout" in response.text.lower() or
            "welcome" in response.text.lower()):
            print("Login successful!")
            return True
        else:
            print("Login may have failed - checking session...")
            # Try to access the abstracts page directly to test session
            test_response = self.session.get(self.abstracts_url)
            if "admin-login.php" not in test_response.text:
                print("Session appears to be valid!")
                return True
            else:
                print("Login failed - session not established")
                return False
    
    def explore_abstracts_page(self):
        """Explore the abstracts page to see what data is available"""
        print("Exploring abstracts page...")
        
        response = self.session.get(self.abstracts_url)
        if response.status_code != 200:
            print(f"Failed to access abstracts page. Status code: {response.status_code}")
            return None
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Save the HTML for analysis
        with open('abstracts_page.html', 'w', encoding='utf-8') as f:
            f.write(soup.prettify())
        
        print("Page content saved to 'abstracts_page.html'")
        
        # Analyze the page structure
        print("\n=== PAGE ANALYSIS ===")
        
        # Look for tables
        tables = soup.find_all('table')
        print(f"Found {len(tables)} table(s)")
        
        # Look for forms
        forms = soup.find_all('form')
        print(f"Found {len(forms)} form(s)")
        
        # Look for common data containers
        divs = soup.find_all('div', class_=True)
        print(f"Found {len(divs)} div(s) with classes")
        
        # Look for pagination
        pagination = soup.find_all(['a', 'button'], string=lambda text: text and ('next' in text.lower() or 'prev' in text.lower() or 'page' in text.lower()))
        print(f"Found {len(pagination)} pagination element(s)")
        
        return soup

# Test the scraper
if __name__ == "__main__":
    scraper = MagnusGroupScraper()
    
    # Login credentials
    username = "<EMAIL>"
    password = "Magnus@38"
    
    # Attempt login
    if scraper.login(username, password):
        # Explore the abstracts page
        soup = scraper.explore_abstracts_page()
        
        if soup:
            print("\nPage exploration complete. Check 'abstracts_page.html' for full page content.")
    else:
        print("Failed to login. Please check credentials.")
